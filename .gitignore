# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.production
.env.staging

# Credentials and secrets
service-account-key.json
*.pem
*.key
*.crt
credentials.json
secrets.yaml
secrets.yml

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore

# ADK specific
.adk/
adk-cache/

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/
.nox/

# Documentation
docs/_build/
site/

# Temporary files
*.tmp
*.temp
.cache/

# Node.js (for any future web components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter Notebooks
.ipynb_checkpoints

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
