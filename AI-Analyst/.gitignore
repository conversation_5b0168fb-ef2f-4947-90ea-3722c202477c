# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.production
.env.staging

# Credentials and secrets
service-account-key.json
*.pem
*.key
*.crt
credentials.json
secrets.yaml
secrets.yml

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.dockerignore

# ADK specific
.adk/
adk-cache/
.adk_cache/

# Testing
.coverage
.pytest_cache/
.tox/
htmlcov/
.nox/

# Documentation
docs/_build/
site/

# Temporary files
*.tmp
*.temp
.cache/

# Node.js (for any future web components)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Jupyter Notebooks
.ipynb_checkpoints

# Database
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Poetry
poetry.lock

# MyPy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VSCode
.vscode/
*.code-workspace

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs documentation
/site

# Agent specific
agents/*/logs/
agents/*/.env
agents/*/service-account-key.json
agents/*/credentials.json

# Deployment specific
deployment/*/secrets/
deployment/*/.env
deployment/*/credentials/

# Shared resources
shared/*/secrets/
shared/*/.env
