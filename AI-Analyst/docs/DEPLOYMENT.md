# Deployment Guide

This guide covers deployment options for AI-Analyst agents in various environments.

## Prerequisites

- <PERSON><PERSON> and Docker Compose
- Google Cloud Project with Vertex AI enabled
- Service Account with appropriate permissions
- Jenkins server access (for Jenkins Reader Agent)

## Local Development

### Single Agent Deployment

1. **Navigate to agent directory:**
   ```bash
   cd agents/jenkins-reader-agent
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Run with Docker:**
   ```bash
   docker build -f Dockerfile.adk -t jenkins-reader-agent:latest .
   docker run -p 8000:8000 jenkins-reader-agent:latest
   ```

4. **Access the interface:**
   ```
   http://localhost:8000
   ```

## Production Deployment

### Google Cloud Run

1. **Build and push image:**
   ```bash
   docker build -f Dockerfile.adk -t gcr.io/PROJECT_ID/jenkins-reader-agent:latest .
   docker push gcr.io/PROJECT_ID/jenkins-reader-agent:latest
   ```

2. **Deploy to Cloud Run:**
   ```bash
   gcloud run deploy jenkins-reader-agent \
     --image gcr.io/PROJECT_ID/jenkins-reader-agent:latest \
     --platform managed \
     --region us-central1 \
     --port 8000
   ```

## Environment Configuration

### Required Environment Variables

```bash
# Google Cloud Configuration
GOOGLE_GENAI_USE_VERTEXAI=1
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json

# Model Configuration
GEMINI_PRO_MODEL=gemini-2.5-pro-preview-05-06
GEMINI_FLASH_MODEL=gemini-2.5-flash-preview-05-20
```

## Security Considerations

- Use Google Secret Manager for production
- Never commit credentials to version control
- Rotate credentials regularly
- Use least privilege principle

## Troubleshooting

### Common Issues

1. **Authentication Errors:**
   - Verify service account key
   - Check IAM permissions

2. **Connection Issues:**
   - Check network connectivity
   - Verify firewall rules

### Health Checks

```bash
# Check agent health
curl http://localhost:8000/

# Check logs
docker logs container_name
```
