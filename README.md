# AI-Analyst

A comprehensive collection of AI-powered agents for enterprise infrastructure analysis and automation, built with Google's Agent Development Kit (ADK).

## Overview

AI-Analyst is a multi-agent platform designed to provide intelligent analysis and insights across various enterprise systems. Each agent is specialized for specific infrastructure components while maintaining consistent security, reliability, and enterprise-grade standards.

## Available Agents

### 🔧 Jenkins Reader Agent
**Location**: `agents/jenkins-reader-agent/`

A comprehensive Jenkins read-only agent that provides secure, scalable access to Jenkins CI/CD infrastructure data.

**Key Features:**
- ✅ **Read-Only Operations**: Safe infrastructure analysis without disrupting operations
- ✅ **Enterprise Security**: Google IAM integration with service account authentication
- ✅ **Comprehensive Data Extraction**: Jobs, builds, configurations, artifacts, and relationships
- ✅ **Real-time Analysis**: Live Jenkins data with performance metrics
- ✅ **Gemini 2.5 Integration**: Powered by latest Gemini Pro and Flash models
- ✅ **Docker Ready**: Production-ready containerization

**Capabilities:**
- Job listing and configuration analysis
- Build history and trend analysis
- Artifact management and lifecycle tracking
- System health monitoring and reporting
- Dependency mapping and relationship analysis

## Architecture

```
AI-Analyst/
├── agents/
│   ├── jenkins-reader-agent/     # Jenkins CI/CD analysis agent
│   ├── github-agent/             # (Future) GitHub repository analysis
│   ├── aws-agent/                # (Future) AWS infrastructure analysis
│   └── kubernetes-agent/         # (Future) Kubernetes cluster analysis
├── shared/
│   ├── common/                   # Shared utilities and libraries
│   ├── security/                 # Common security configurations
│   └── monitoring/               # Shared monitoring and logging
├── deployment/
│   ├── docker-compose/           # Multi-agent deployment configs
│   ├── kubernetes/               # K8s deployment manifests
│   └── terraform/                # Infrastructure as code
└── docs/
    ├── architecture/             # System architecture documentation
    ├── security/                 # Security guidelines and policies
    └── deployment/               # Deployment guides
```

## Quick Start

### Jenkins Reader Agent

1. **Navigate to the agent directory:**
   ```bash
   cd agents/jenkins-reader-agent
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your Jenkins and Google Cloud settings
   ```

3. **Run with Docker:**
   ```bash
   docker build -f Dockerfile.adk -t jenkins-reader-agent:latest .
   docker run -p 8000:8000 jenkins-reader-agent:latest
   ```

4. **Access the web interface:**
   ```
   http://localhost:8000
   ```

## Technology Stack

- **Framework**: Google Agent Development Kit (ADK)
- **AI Models**: Gemini 2.5 Pro, Gemini 2.5 Flash
- **Authentication**: Google Cloud IAM, Service Accounts
- **Containerization**: Docker, Docker Compose
- **Languages**: Python 3.12+
- **Security**: Enterprise-grade with audit logging

## Security & Compliance

- **Read-Only Operations**: All agents maintain strict read-only access
- **Authentication**: Google Cloud IAM with service account credentials
- **Audit Logging**: Comprehensive logging for all operations
- **Rate Limiting**: Built-in protection against overwhelming target systems
- **Data Sanitization**: Automatic removal of sensitive information

## Development Guidelines

### Adding New Agents

1. Create agent directory: `agents/your-agent-name/`
2. Follow ADK agent structure with `agent.py` and `__init__.py`
3. Implement security validation and audit logging
4. Add comprehensive documentation and tests
5. Create Docker configuration for deployment

### Security Requirements

- All agents must implement read-only operations
- Service account authentication required
- Comprehensive audit logging mandatory
- Input validation and sanitization required
- Rate limiting and error handling implemented

## Future Roadmap

- **GitHub Agent**: Repository analysis, code quality metrics, security scanning
- **AWS Agent**: Infrastructure monitoring, cost analysis, security assessment
- **Kubernetes Agent**: Cluster health, resource optimization, security compliance
- **Database Agent**: Performance monitoring, query analysis, optimization recommendations
- **Security Agent**: Cross-platform security assessment and compliance reporting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow the development guidelines
4. Add comprehensive tests
5. Submit a pull request

## Support

For questions, issues, or feature requests, please contact the development team or create an issue in the repository.

---

**Built with ❤️ using Google's Agent Development Kit**
