# API Reference

The Agent Development Kit (ADK) provides comprehensive API references for both Python and Java, allowing you to dive deep into all available classes, methods, and functionalities.

<div class.="grid cards" markdown>

-   :fontawesome-brands-python:{ .lg .middle } **Python API Reference**

    ---
    Explore the complete API documentation for the Python Agent Development Kit. Discover detailed information on all modules, classes, functions, and examples to build sophisticated AI agents with Python.

    [:octicons-arrow-right-24: View Python API Docs](python/index.html) <br>
    <!-- Assuming your Python API docs are in a 'python' subdirectory -->
    <!-- Or link to an external ReadTheDocs, etc. -->
    <!-- [:octicons-arrow-right-24: View Python API Docs](python/index.html) -->

<!-- This comment forces a block separation -->

-   :fontawesome-brands-java:{ .lg .middle } **Java API Reference**

    ---
    Access the comprehensive Javadoc for the Java Agent Development Kit. This reference provides detailed specifications for all packages, classes, interfaces, and methods, enabling you to develop robust AI agents using Java.

    [:octicons-arrow-right-24: View Java API Docs](java/index.html) <br>
    <!-- Assuming your Java API docs (Javadocs) are in a 'java' subdirectory -->
    <!-- Or link to an external Javadoc hosting site -->
    <!-- [:octicons-arrow-right-24: View Java API Docs](java/index.html) -->

</div>
