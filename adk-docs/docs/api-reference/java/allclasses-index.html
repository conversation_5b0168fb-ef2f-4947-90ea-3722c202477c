<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>All Classes and Interfaces (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="class index">
<meta name="generator" content="javadoc/AllClassesIndexWriter">
<link rel="stylesheet" type="text/css" href="resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="script-files/script.js"></script>
<script type="text/javascript" src="script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-files/jquery-ui.min.js"></script>
</head>
<body class="all-classes-index-page">
<script type="text/javascript">const pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list"></ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="All Classes and Interfaces" class="title">All Classes and Interfaces</h1>
</div>
<div id="all-classes-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="all-classes-table-tab0" role="tab" aria-selected="true" aria-controls="all-classes-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="all-classes-table-tab1" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab1', 2)" class="table-tab">Interfaces</button><button id="all-classes-table-tab2" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab2', 2)" class="table-tab">Classes</button><button id="all-classes-table-tab3" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab3', 2)" class="table-tab">Enum Classes</button><button id="all-classes-table-tab5" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab5', 2)" class="table-tab">Exception Classes</button><button id="all-classes-table-tab6" role="tab" aria-selected="false" aria-controls="all-classes-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('all-classes-table', 'all-classes-table-tab6', 2)" class="table-tab">Annotation Interfaces</button></div>
<div id="all-classes-table.tabpanel" role="tabpanel" aria-labelledby="all-classes-table-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">AgentTool implements a tool that allows an agent to call another agent.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/AgentTransfer.html" title="class in com.google.adk.flows.llmflows">AgentTransfer</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block"><code>RequestProcessor</code> that handles agent transfer for LLM flow.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/Annotations.html" title="class in com.google.adk.tools">Annotations</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Annotations for tools.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab6"><a href="com/google/adk/tools/Annotations.Schema.html" title="annotation interface in com.google.adk.tools">Annotations.Schema</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab6">
<div class="block">The annotation for binding the 'Schema' input.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/network/ApiResponse.html" title="class in com.google.adk.network">ApiResponse</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The API response contains a response to a call to the GenAI APIs.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/ApiResponse.html" title="class in com.google.adk.sessions">ApiResponse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The API response contains a response to a call to the GenAI APIs.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/AutoFlow.html" title="class in com.google.adk.flows.llmflows">AutoFlow</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for all agents.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Base interface for artifact services.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/examples/BaseExampleProvider.html" title="interface in com.google.adk.examples">BaseExampleProvider</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">An interface that provides examples for a given query.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/flows/BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for the execution flows to run a group of agents.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Abstract base class for Large Language Models (LLMs).</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">The base class for a live model connection.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A basic flow that calls the LLM in a loop until a final response is generated.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html" title="class in com.google.adk.tools.retrieval">BaseRetrievalTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base class for retrieval tools.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">Defines the contract for managing <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a>s and their associated <a href="com/google/adk/events/Event.html" title="class in com.google.adk.events"><code>Event</code></a>s.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The base class for all ADK tools.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/Basic.html" title="class in com.google.adk.flows.llmflows">Basic</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block"><code>RequestProcessor</code> that handles basic information to build the LLM request.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/BuiltInCodeExecutionTool.html" title="class in com.google.adk.tools">BuiltInCodeExecutionTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A built-in code execution tool that is automatically invoked by Gemini 2 models.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The context of various callbacks for an agent invocation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/Callbacks.html" title="class in com.google.adk.agents">Callbacks</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.AfterAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.AfterAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.AfterModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.AfterToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.BeforeAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.BeforeAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.BeforeModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/agents/Callbacks.BeforeToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/Claude.html" title="class in com.google.adk.models">Claude</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the Claude Generative AI model by Anthropic.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/CollectionUtils.html" title="class in com.google.adk">CollectionUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Frequently used code snippets for collections.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/Contents.html" title="class in com.google.adk.flows.llmflows">Contents</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block"><code>RequestProcessor</code> that populates content in request for LLM flows.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/mcp/ConversionUtils.html" title="class in com.google.adk.tools.mcp">ConversionUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for converting between different representations of MCP tools.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents an event in a session.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/events/Event.html" title="class in com.google.adk.events"><code>Event</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the actions attached to an event.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events"><code>EventActions</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/events/EventStream.html" title="class in com.google.adk.events">EventStream</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples">Example</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents an few-shot example.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/examples/Example.Builder.html" title="class in com.google.adk.examples">Example.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for constructing <a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples"><code>Example</code></a> instances.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/Examples.html" title="class in com.google.adk.flows.llmflows">Examples</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block"><code>RequestProcessor</code> that populates examples in LLM request.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/examples/ExampleUtils.html" title="class in com.google.adk.examples">ExampleUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for examples.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/ExitLoopTool.html" title="class in com.google.adk.tools">ExitLoopTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Exits the loop.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/FunctionCallingUtils.html" title="class in com.google.adk.tools">FunctionCallingUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for function calling.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/Functions.html" title="class in com.google.adk.flows.llmflows">Functions</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for handling function calls.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/FunctionTool.html" title="class in com.google.adk.tools">FunctionTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">FunctionTool implements a customized function calling tool.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/artifacts/GcsArtifactService.html" title="class in com.google.adk.artifacts">GcsArtifactService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An artifact service implementation using Google Cloud Storage (GCS).</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models">Gemini</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents the Gemini Generative AI model.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models"><code>Gemini</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/GeminiLlmConnection.html" title="class in com.google.adk.models">GeminiLlmConnection</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Manages a persistent, bidirectional connection to the Gemini model via WebSockets for real-time
 interaction.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Configuration for getting a session.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/GetSessionConfig.Builder.html" title="class in com.google.adk.sessions">GetSessionConfig.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/sessions/GetSessionConfig.html" title="class in com.google.adk.sessions"><code>GetSessionConfig</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/GoogleSearchTool.html" title="class in com.google.adk.tools">GoogleSearchTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A built-in tool that is automatically invoked by Gemini 2 models to retrieve search results from
 Google Search.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/HttpApiClient.html" title="class in com.google.adk.sessions">HttpApiClient</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Base client for the HTTP APIs.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/network/HttpApiResponse.html" title="class in com.google.adk.network">HttpApiResponse</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wraps a real HTTP response to expose the methods needed by the GenAI SDK.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/HttpApiResponse.html" title="class in com.google.adk.sessions">HttpApiResponse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Wraps a real HTTP response to expose the methods needed by the GenAI SDK.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/Identity.html" title="class in com.google.adk.flows.llmflows">Identity</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block"><code>RequestProcessor</code> that gives the agent identity from the framework</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/artifacts/InMemoryArtifactService.html" title="class in com.google.adk.artifacts">InMemoryArtifactService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An in-memory implementation of the <a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts"><code>BaseArtifactService</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/runner/InMemoryRunner.html" title="class in com.google.adk.runner">InMemoryRunner</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The class for the in-memory GenAi runner, using in-memory artifact and session services.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">An in-memory implementation of <a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions"><code>BaseSessionService</code></a> assuming <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> objects are
 mutable regarding their state map, events list, and last update time.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/Instructions.html" title="class in com.google.adk.flows.llmflows">Instructions</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block"><code>RequestProcessor</code> that handles instructions and global instructions for LLM flows.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">The context for an agent invocation.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The base class for the types that needs JSON serialization/deserialization capability.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/artifacts/ListArtifactsResponse.html" title="class in com.google.adk.artifacts">ListArtifactsResponse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Response for listing artifacts.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactsResponse.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/artifacts/ListArtifactsResponse.html" title="class in com.google.adk.artifacts"><code>ListArtifactsResponse</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Response for listing artifact versions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" title="class in com.google.adk.artifacts"><code>ListArtifactVersionsResponse</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Response for listing events.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/ListEventsResponse.Builder.html" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/sessions/ListEventsResponse.html" title="class in com.google.adk.sessions"><code>ListEventsResponse</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Response for listing sessions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/ListSessionsResponse.Builder.html" title="class in com.google.adk.sessions">ListSessionsResponse.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/sessions/ListSessionsResponse.html" title="class in com.google.adk.sessions"><code>ListSessionsResponse</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents a request to be sent to a live connection to the LLM model.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for constructing <a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents"><code>LiveRequest</code></a> instances.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A queue of live requests to be sent to the model.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The LLM-based agent.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab3"><a href="com/google/adk/agents/LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab3">
<div class="block">Enum to define if contents of previous events should be included in requests to the underlying
 LLM.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="com/google/adk/exceptions/LlmCallsLimitExceededException.html" title="class in com.google.adk.exceptions">LlmCallsLimitExceededException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">An error indicating that the limit for calls to the LLM has been exceeded.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/LlmRegistry.html" title="class in com.google.adk.models">LlmRegistry</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/models/LlmRegistry.LlmFactory.html" title="interface in com.google.adk.models">LlmRegistry.LlmFactory</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab1">
<div class="block">The factory interface for creating LLM instances.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents a request to be sent to the LLM.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for constructing <a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models"><code>LlmRequest</code></a> instances.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents a response received from the LLM.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for constructing <a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models"><code>LlmResponse</code></a> instances.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/LoadArtifactsTool.html" title="class in com.google.adk.tools">LoadArtifactsTool</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A tool that loads artifacts and adds them to the session.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/LongRunningFunctionTool.html" title="class in com.google.adk.tools">LongRunningFunctionTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A function tool that returns the result asynchronously.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/LoopAgent.html" title="class in com.google.adk.agents">LoopAgent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An agent that runs its sub-agents sequentially in a loop.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/agents/LoopAgent.html" title="class in com.google.adk.agents"><code>LoopAgent</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/mcp/McpSessionManager.html" title="class in com.google.adk.tools.mcp">McpSessionManager</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Manages MCP client sessions.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/mcp/McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">"""Initializes a MCPTool.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Connects to a MCP Server, and retrieves MCP Tools into ADK Tools.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Exception thrown when there's an error during MCP session initialization.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Exception thrown when there's an error during loading tools from the MCP server.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holds the result of loading tools, containing both the tools and the toolset instance.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Base exception for all errors originating from <code>McpToolset</code>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/Model.html" title="class in com.google.adk.models">Model</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Represents a model by name or instance.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/Model.Builder.html" title="class in com.google.adk.models">Model.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/models/Model.html" title="class in com.google.adk.models"><code>Model</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for creating ConcurrentHashMaps.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/ParallelAgent.html" title="class in com.google.adk.agents">ParallelAgent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A shell agent that runs its sub-agents in parallel in isolated manner.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/agents/ParallelAgent.html" title="class in com.google.adk.agents"><code>ParallelAgent</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/ReadonlyContext.html" title="class in com.google.adk.agents">ReadonlyContext</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Provides read-only access to the context of an agent run.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Configuration to modify an agent's LLM's underlying behavior.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents"><code>RunConfig</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab3"><a href="com/google/adk/agents/RunConfig.StreamingMode.html" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab3">
<div class="block">Streaming mode for the runner.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">The main class for the GenAI Agents runner.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/SchemaUtils.html" title="class in com.google.adk">SchemaUtils</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for validating schemas.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/SequentialAgent.html" title="class in com.google.adk.agents">SequentialAgent</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">An agent that runs its sub-agents sequentially.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/agents/SequentialAgent.html" title="class in com.google.adk.agents"><code>SequentialAgent</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">A <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> object that encapsulates the <a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions"><code>State</code></a> and <a href="com/google/adk/events/Event.html" title="class in com.google.adk.events"><code>Event</code></a>s of a session.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a>.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab5"><a href="com/google/adk/sessions/SessionException.html" title="class in com.google.adk.sessions">SessionException</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab5">
<div class="block">Represents a general error that occurred during session management operations.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab5"><a href="com/google/adk/sessions/SessionNotFoundException.html" title="class in com.google.adk.sessions">SessionNotFoundException</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab5">
<div class="block">Indicates that a requested session could not be found.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/SessionUtils.html" title="class in com.google.adk.sessions">SessionUtils</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility functions for session service.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/SingleFlow.html" title="class in com.google.adk.flows.llmflows">SingleFlow</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">&nbsp;</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab1"><a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html" title="interface in com.google.adk.flows.llmflows.audio">SpeechClientInterface</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab1">
<div class="block">Interface for a speech-to-text client.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Parameters for establishing a MCP Server-Sent Events (SSE) connection.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp"><code>SseServerParameters</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">A <a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions"><code>State</code></a> object that also keeps track of the changes to the state.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/Telemetry.html" title="class in com.google.adk">Telemetry</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Utility class for capturing and reporting telemetry data within the ADK.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">ToolContext object provides a structured context for executing tools or functions.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/tools/ToolContext.Builder.html" title="class in com.google.adk.tools">ToolContext.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools"><code>ToolContext</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/Version.html" title="class in com.google.adk">Version</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Holding class for the version of the Java ADK.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">TODO: Use the genai HttpApiClient and ApiResponse methods once they are public.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Credentials for accessing Gemini models through Vertex.</div>
</div>
<div class="col-first even-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></div>
<div class="col-last even-row-color all-classes-table all-classes-table-tab2">
<div class="block">Builder for <a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models"><code>VertexCredentials</code></a>.</div>
</div>
<div class="col-first odd-row-color all-classes-table all-classes-table-tab2"><a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html" title="class in com.google.adk.flows.llmflows.audio">VertexSpeechClient</a></div>
<div class="col-last odd-row-color all-classes-table all-classes-table-tab2">
<div class="block">Implementation of SpeechClientInterface using Vertex AI SpeechClient.</div>
</div>
</div>
</div>
</div>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
