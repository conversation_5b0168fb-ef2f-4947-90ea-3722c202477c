<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Telemetry (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk, class: Telemetry">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/Telemetry.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk</a></li>
<li><a href="Telemetry.html" class="current-selection">Telemetry</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#traceToolCall(java.util.Map)" tabindex="0">traceToolCall(Map)</a></li>
<li><a href="#traceToolResponse(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.events.Event)" tabindex="0">traceToolResponse(InvocationContext, String, Event)</a></li>
<li><a href="#traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" tabindex="0">traceCallLlm(InvocationContext, String, LlmRequest, LlmResponse)</a></li>
<li><a href="#traceSendData(com.google.adk.agents.InvocationContext,java.lang.String,java.util.List)" tabindex="0">traceSendData(InvocationContext, String, List)</a></li>
<li><a href="#getTracer()" tabindex="0">getTracer()</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class Telemetry" class="title">Class Telemetry</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">com.google.adk.Telemetry</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Telemetry</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Utility class for capturing and reporting telemetry data within the ADK. This class provides
 methods to trace various aspects of the agent's execution, including tool calls, tool responses,
 LLM interactions, and data handling. It leverages OpenTelemetry for tracing and logging for
 detailed information. These traces can then be exported through the ADK Dev Server UI.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static io.opentelemetry.api.trace.Tracer</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#getTracer()" class="member-name-link">getTracer</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Gets the tracer.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">traceCallLlm</a><wbr>(<a href="agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Traces a call to the LLM.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#traceSendData(com.google.adk.agents.InvocationContext,java.lang.String,java.util.List)" class="member-name-link">traceSendData</a><wbr>(<a href="agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.genai.types.Content&gt;&nbsp;data)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Traces the sending of data (history or new content) to the agent/model.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#traceToolCall(java.util.Map)" class="member-name-link">traceToolCall</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;args)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Traces tool call arguments.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#traceToolResponse(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.events.Event)" class="member-name-link">traceToolResponse</a><wbr>(<a href="agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionResponseEvent)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Traces tool response event.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="traceToolCall(java.util.Map)">
<h3>traceToolCall</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">traceToolCall</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;args)</span></div>
<div class="block">Traces tool call arguments.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>args</code> - The arguments to the tool call.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="traceToolResponse(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.events.Event)">
<h3>traceToolResponse</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">traceToolResponse</span><wbr><span class="parameters">(<a href="agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionResponseEvent)</span></div>
<div class="block">Traces tool response event.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>invocationContext</code> - The invocation context for the current agent run.</dd>
<dd><code>eventId</code> - The ID of the event.</dd>
<dd><code>functionResponseEvent</code> - The function response event.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)">
<h3>traceCallLlm</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">traceCallLlm</span><wbr><span class="parameters">(<a href="agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</span></div>
<div class="block">Traces a call to the LLM.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>invocationContext</code> - The invocation context.</dd>
<dd><code>eventId</code> - The ID of the event associated with this LLM call/response.</dd>
<dd><code>llmRequest</code> - The LLM request object.</dd>
<dd><code>llmResponse</code> - The LLM response object.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="traceSendData(com.google.adk.agents.InvocationContext,java.lang.String,java.util.List)">
<h3>traceSendData</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">traceSendData</span><wbr><span class="parameters">(<a href="agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.genai.types.Content&gt;&nbsp;data)</span></div>
<div class="block">Traces the sending of data (history or new content) to the agent/model.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>invocationContext</code> - The invocation context.</dd>
<dd><code>eventId</code> - The ID of the event, if applicable.</dd>
<dd><code>data</code> - A list of content objects being sent.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getTracer()">
<h3>getTracer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type">io.opentelemetry.api.trace.Tracer</span>&nbsp;<span class="element-name">getTracer</span>()</div>
<div class="block">Gets the tracer.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>The tracer.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
