<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>LlmAgent.Builder (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.agents, class: LlmAgent, class: Builder">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/LlmAgent.Builder.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.agents</a></li>
<li><a href="LlmAgent.html">LlmAgent</a></li>
<li><a href="LlmAgent.Builder.html" class="current-selection">Builder</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#constructor-summary" tabindex="0">Constructor Summary</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#constructor-detail" tabindex="0">Constructor Details</a>
<ol class="toc-list">
<li><a href="#%3Cinit%3E()" tabindex="0">Builder()</a></li>
</ol>
</li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#name(java.lang.String)" tabindex="0">name(String)</a></li>
<li><a href="#description(java.lang.String)" tabindex="0">description(String)</a></li>
<li><a href="#model(java.lang.String)" tabindex="0">model(String)</a></li>
<li><a href="#model(com.google.adk.models.BaseLlm)" tabindex="0">model(BaseLlm)</a></li>
<li><a href="#instruction(java.lang.String)" tabindex="0">instruction(String)</a></li>
<li><a href="#globalInstruction(java.lang.String)" tabindex="0">globalInstruction(String)</a></li>
<li><a href="#subAgents(java.util.List)" tabindex="0">subAgents(List)</a></li>
<li><a href="#subAgents(com.google.adk.agents.BaseAgent...)" tabindex="0">subAgents(BaseAgent...)</a></li>
<li><a href="#tools(java.util.List)" tabindex="0">tools(List)</a></li>
<li><a href="#tools(com.google.adk.tools.BaseTool...)" tabindex="0">tools(BaseTool...)</a></li>
<li><a href="#generateContentConfig(com.google.genai.types.GenerateContentConfig)" tabindex="0">generateContentConfig(GenerateContentConfig)</a></li>
<li><a href="#exampleProvider(com.google.adk.examples.BaseExampleProvider)" tabindex="0">exampleProvider(BaseExampleProvider)</a></li>
<li><a href="#exampleProvider(java.util.List)" tabindex="0">exampleProvider(List)</a></li>
<li><a href="#exampleProvider(com.google.adk.examples.Example...)" tabindex="0">exampleProvider(Example...)</a></li>
<li><a href="#includeContents(com.google.adk.agents.LlmAgent.IncludeContents)" tabindex="0">includeContents(LlmAgent.IncludeContents)</a></li>
<li><a href="#planning(boolean)" tabindex="0">planning(boolean)</a></li>
<li><a href="#disallowTransferToParent(boolean)" tabindex="0">disallowTransferToParent(boolean)</a></li>
<li><a href="#disallowTransferToPeers(boolean)" tabindex="0">disallowTransferToPeers(boolean)</a></li>
<li><a href="#beforeModelCallback(com.google.adk.agents.Callbacks.BeforeModelCallback)" tabindex="0">beforeModelCallback(Callbacks.BeforeModelCallback)</a></li>
<li><a href="#beforeModelCallbackSync(com.google.adk.agents.Callbacks.BeforeModelCallbackSync)" tabindex="0">beforeModelCallbackSync(Callbacks.BeforeModelCallbackSync)</a></li>
<li><a href="#afterModelCallback(com.google.adk.agents.Callbacks.AfterModelCallback)" tabindex="0">afterModelCallback(Callbacks.AfterModelCallback)</a></li>
<li><a href="#afterModelCallbackSync(com.google.adk.agents.Callbacks.AfterModelCallbackSync)" tabindex="0">afterModelCallbackSync(Callbacks.AfterModelCallbackSync)</a></li>
<li><a href="#beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)" tabindex="0">beforeAgentCallback(Callbacks.BeforeAgentCallback)</a></li>
<li><a href="#beforeAgentCallbackSync(com.google.adk.agents.Callbacks.BeforeAgentCallbackSync)" tabindex="0">beforeAgentCallbackSync(Callbacks.BeforeAgentCallbackSync)</a></li>
<li><a href="#afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)" tabindex="0">afterAgentCallback(Callbacks.AfterAgentCallback)</a></li>
<li><a href="#afterAgentCallbackSync(com.google.adk.agents.Callbacks.AfterAgentCallbackSync)" tabindex="0">afterAgentCallbackSync(Callbacks.AfterAgentCallbackSync)</a></li>
<li><a href="#beforeToolCallback(com.google.adk.agents.Callbacks.BeforeToolCallback)" tabindex="0">beforeToolCallback(Callbacks.BeforeToolCallback)</a></li>
<li><a href="#beforeToolCallbackSync(com.google.adk.agents.Callbacks.BeforeToolCallbackSync)" tabindex="0">beforeToolCallbackSync(Callbacks.BeforeToolCallbackSync)</a></li>
<li><a href="#afterToolCallback(com.google.adk.agents.Callbacks.AfterToolCallback)" tabindex="0">afterToolCallback(Callbacks.AfterToolCallback)</a></li>
<li><a href="#afterToolCallbackSync(com.google.adk.agents.Callbacks.AfterToolCallbackSync)" tabindex="0">afterToolCallbackSync(Callbacks.AfterToolCallbackSync)</a></li>
<li><a href="#inputSchema(com.google.genai.types.Schema)" tabindex="0">inputSchema(Schema)</a></li>
<li><a href="#outputSchema(com.google.genai.types.Schema)" tabindex="0">outputSchema(Schema)</a></li>
<li><a href="#executor(java.util.concurrent.Executor)" tabindex="0">executor(Executor)</a></li>
<li><a href="#outputKey(java.lang.String)" tabindex="0">outputKey(String)</a></li>
<li><a href="#build()" tabindex="0">build()</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class LlmAgent.Builder" class="title">Class LlmAgent.Builder</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">com.google.adk.agents.LlmAgent.Builder</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>Enclosing class:</dt>
<dd><code><a href="LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></code></dd>
</dl>
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public static class </span><span class="element-name type-name-label">LlmAgent.Builder</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></span></div>
<div class="block">Builder for <a href="LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">Builder</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">afterAgentCallback</a><wbr>(<a href="Callbacks.AfterAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a>&nbsp;afterAgentCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterAgentCallbackSync(com.google.adk.agents.Callbacks.AfterAgentCallbackSync)" class="member-name-link">afterAgentCallbackSync</a><wbr>(<a href="Callbacks.AfterAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a>&nbsp;afterAgentCallbackSync)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterModelCallback(com.google.adk.agents.Callbacks.AfterModelCallback)" class="member-name-link">afterModelCallback</a><wbr>(<a href="Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a>&nbsp;afterModelCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterModelCallbackSync(com.google.adk.agents.Callbacks.AfterModelCallbackSync)" class="member-name-link">afterModelCallbackSync</a><wbr>(<a href="Callbacks.AfterModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a>&nbsp;afterModelCallbackSync)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterToolCallback(com.google.adk.agents.Callbacks.AfterToolCallback)" class="member-name-link">afterToolCallback</a><wbr>(<a href="Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a>&nbsp;afterToolCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterToolCallbackSync(com.google.adk.agents.Callbacks.AfterToolCallbackSync)" class="member-name-link">afterToolCallbackSync</a><wbr>(<a href="Callbacks.AfterToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a>&nbsp;afterToolCallbackSync)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)" class="member-name-link">beforeAgentCallback</a><wbr>(<a href="Callbacks.BeforeAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a>&nbsp;beforeAgentCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeAgentCallbackSync(com.google.adk.agents.Callbacks.BeforeAgentCallbackSync)" class="member-name-link">beforeAgentCallbackSync</a><wbr>(<a href="Callbacks.BeforeAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a>&nbsp;beforeAgentCallbackSync)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeModelCallback(com.google.adk.agents.Callbacks.BeforeModelCallback)" class="member-name-link">beforeModelCallback</a><wbr>(<a href="Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a>&nbsp;beforeModelCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeModelCallbackSync(com.google.adk.agents.Callbacks.BeforeModelCallbackSync)" class="member-name-link">beforeModelCallbackSync</a><wbr>(<a href="Callbacks.BeforeModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a>&nbsp;beforeModelCallbackSync)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeToolCallback(com.google.adk.agents.Callbacks.BeforeToolCallback)" class="member-name-link">beforeToolCallback</a><wbr>(<a href="Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a>&nbsp;beforeToolCallback)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeToolCallbackSync(com.google.adk.agents.Callbacks.BeforeToolCallbackSync)" class="member-name-link">beforeToolCallbackSync</a><wbr>(<a href="Callbacks.BeforeToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a>&nbsp;beforeToolCallbackSync)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#build()" class="member-name-link">build</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#description(java.lang.String)" class="member-name-link">description</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#disallowTransferToParent(boolean)" class="member-name-link">disallowTransferToParent</a><wbr>(boolean&nbsp;disallowTransferToParent)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#disallowTransferToPeers(boolean)" class="member-name-link">disallowTransferToPeers</a><wbr>(boolean&nbsp;disallowTransferToPeers)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#exampleProvider(com.google.adk.examples.BaseExampleProvider)" class="member-name-link">exampleProvider</a><wbr>(<a href="../examples/BaseExampleProvider.html" title="interface in com.google.adk.examples">BaseExampleProvider</a>&nbsp;exampleProvider)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#exampleProvider(com.google.adk.examples.Example...)" class="member-name-link">exampleProvider</a><wbr>(<a href="../examples/Example.html" title="class in com.google.adk.examples">Example</a>...&nbsp;examples)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#exampleProvider(java.util.List)" class="member-name-link">exampleProvider</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../examples/Example.html" title="class in com.google.adk.examples">Example</a>&gt;&nbsp;examples)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executor(java.util.concurrent.Executor)" class="member-name-link">executor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/Executor.html" title="class or interface in java.util.concurrent" class="external-link">Executor</a>&nbsp;executor)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateContentConfig(com.google.genai.types.GenerateContentConfig)" class="member-name-link">generateContentConfig</a><wbr>(com.google.genai.types.GenerateContentConfig&nbsp;generateContentConfig)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#globalInstruction(java.lang.String)" class="member-name-link">globalInstruction</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;globalInstruction)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#includeContents(com.google.adk.agents.LlmAgent.IncludeContents)" class="member-name-link">includeContents</a><wbr>(<a href="LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a>&nbsp;includeContents)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#inputSchema(com.google.genai.types.Schema)" class="member-name-link">inputSchema</a><wbr>(com.google.genai.types.Schema&nbsp;inputSchema)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#instruction(java.lang.String)" class="member-name-link">instruction</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;instruction)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#model(com.google.adk.models.BaseLlm)" class="member-name-link">model</a><wbr>(<a href="../models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a>&nbsp;model)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#model(java.lang.String)" class="member-name-link">model</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#name(java.lang.String)" class="member-name-link">name</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#outputKey(java.lang.String)" class="member-name-link">outputKey</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputKey)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#outputSchema(com.google.genai.types.Schema)" class="member-name-link">outputSchema</a><wbr>(com.google.genai.types.Schema&nbsp;outputSchema)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#planning(boolean)" class="member-name-link">planning</a><wbr>(boolean&nbsp;planning)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents</a><wbr>(<a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>...&nbsp;subAgents)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#subAgents(java.util.List)" class="member-name-link">subAgents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#tools(com.google.adk.tools.BaseTool...)" class="member-name-link">tools</a><wbr>(<a href="../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>...&nbsp;tools)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#tools(java.util.List)" class="member-name-link">tools</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>Builder</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Builder</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="name(java.lang.String)">
<h3>name</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">name</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="description(java.lang.String)">
<h3>description</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">description</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="model(java.lang.String)">
<h3>model</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">model</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="model(com.google.adk.models.BaseLlm)">
<h3>model</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">model</span><wbr><span class="parameters">(<a href="../models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a>&nbsp;model)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="instruction(java.lang.String)">
<h3>instruction</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">instruction</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;instruction)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="globalInstruction(java.lang.String)">
<h3>globalInstruction</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">globalInstruction</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;globalInstruction)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="subAgents(java.util.List)">
<h3>subAgents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">subAgents</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="subAgents(com.google.adk.agents.BaseAgent...)">
<h3>subAgents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">subAgents</span><wbr><span class="parameters">(<a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>...&nbsp;subAgents)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="tools(java.util.List)">
<h3>tools</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">tools</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="tools(com.google.adk.tools.BaseTool...)">
<h3>tools</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">tools</span><wbr><span class="parameters">(<a href="../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>...&nbsp;tools)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="generateContentConfig(com.google.genai.types.GenerateContentConfig)">
<h3>generateContentConfig</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">generateContentConfig</span><wbr><span class="parameters">(com.google.genai.types.GenerateContentConfig&nbsp;generateContentConfig)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="exampleProvider(com.google.adk.examples.BaseExampleProvider)">
<h3>exampleProvider</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">exampleProvider</span><wbr><span class="parameters">(<a href="../examples/BaseExampleProvider.html" title="interface in com.google.adk.examples">BaseExampleProvider</a>&nbsp;exampleProvider)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="exampleProvider(java.util.List)">
<h3>exampleProvider</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">exampleProvider</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../examples/Example.html" title="class in com.google.adk.examples">Example</a>&gt;&nbsp;examples)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="exampleProvider(com.google.adk.examples.Example...)">
<h3>exampleProvider</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">exampleProvider</span><wbr><span class="parameters">(<a href="../examples/Example.html" title="class in com.google.adk.examples">Example</a>...&nbsp;examples)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="includeContents(com.google.adk.agents.LlmAgent.IncludeContents)">
<h3>includeContents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">includeContents</span><wbr><span class="parameters">(<a href="LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a>&nbsp;includeContents)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="planning(boolean)">
<h3>planning</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">planning</span><wbr><span class="parameters">(boolean&nbsp;planning)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="disallowTransferToParent(boolean)">
<h3>disallowTransferToParent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">disallowTransferToParent</span><wbr><span class="parameters">(boolean&nbsp;disallowTransferToParent)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="disallowTransferToPeers(boolean)">
<h3>disallowTransferToPeers</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">disallowTransferToPeers</span><wbr><span class="parameters">(boolean&nbsp;disallowTransferToPeers)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeModelCallback(com.google.adk.agents.Callbacks.BeforeModelCallback)">
<h3>beforeModelCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">beforeModelCallback</span><wbr><span class="parameters">(<a href="Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a>&nbsp;beforeModelCallback)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeModelCallbackSync(com.google.adk.agents.Callbacks.BeforeModelCallbackSync)">
<h3>beforeModelCallbackSync</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">beforeModelCallbackSync</span><wbr><span class="parameters">(<a href="Callbacks.BeforeModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a>&nbsp;beforeModelCallbackSync)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterModelCallback(com.google.adk.agents.Callbacks.AfterModelCallback)">
<h3>afterModelCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">afterModelCallback</span><wbr><span class="parameters">(<a href="Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a>&nbsp;afterModelCallback)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterModelCallbackSync(com.google.adk.agents.Callbacks.AfterModelCallbackSync)">
<h3>afterModelCallbackSync</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">afterModelCallbackSync</span><wbr><span class="parameters">(<a href="Callbacks.AfterModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a>&nbsp;afterModelCallbackSync)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)">
<h3>beforeAgentCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">beforeAgentCallback</span><wbr><span class="parameters">(<a href="Callbacks.BeforeAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a>&nbsp;beforeAgentCallback)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeAgentCallbackSync(com.google.adk.agents.Callbacks.BeforeAgentCallbackSync)">
<h3>beforeAgentCallbackSync</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">beforeAgentCallbackSync</span><wbr><span class="parameters">(<a href="Callbacks.BeforeAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a>&nbsp;beforeAgentCallbackSync)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)">
<h3>afterAgentCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">afterAgentCallback</span><wbr><span class="parameters">(<a href="Callbacks.AfterAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a>&nbsp;afterAgentCallback)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterAgentCallbackSync(com.google.adk.agents.Callbacks.AfterAgentCallbackSync)">
<h3>afterAgentCallbackSync</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">afterAgentCallbackSync</span><wbr><span class="parameters">(<a href="Callbacks.AfterAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a>&nbsp;afterAgentCallbackSync)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeToolCallback(com.google.adk.agents.Callbacks.BeforeToolCallback)">
<h3>beforeToolCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">beforeToolCallback</span><wbr><span class="parameters">(<a href="Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a>&nbsp;beforeToolCallback)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeToolCallbackSync(com.google.adk.agents.Callbacks.BeforeToolCallbackSync)">
<h3>beforeToolCallbackSync</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">beforeToolCallbackSync</span><wbr><span class="parameters">(<a href="Callbacks.BeforeToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a>&nbsp;beforeToolCallbackSync)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterToolCallback(com.google.adk.agents.Callbacks.AfterToolCallback)">
<h3>afterToolCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">afterToolCallback</span><wbr><span class="parameters">(<a href="Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a>&nbsp;afterToolCallback)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterToolCallbackSync(com.google.adk.agents.Callbacks.AfterToolCallbackSync)">
<h3>afterToolCallbackSync</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">afterToolCallbackSync</span><wbr><span class="parameters">(<a href="Callbacks.AfterToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a>&nbsp;afterToolCallbackSync)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="inputSchema(com.google.genai.types.Schema)">
<h3>inputSchema</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">inputSchema</span><wbr><span class="parameters">(com.google.genai.types.Schema&nbsp;inputSchema)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="outputSchema(com.google.genai.types.Schema)">
<h3>outputSchema</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">outputSchema</span><wbr><span class="parameters">(com.google.genai.types.Schema&nbsp;outputSchema)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="executor(java.util.concurrent.Executor)">
<h3>executor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">executor</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/Executor.html" title="class or interface in java.util.concurrent" class="external-link">Executor</a>&nbsp;executor)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="outputKey(java.lang.String)">
<h3>outputKey</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">outputKey</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputKey)</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="build()">
<h3>build</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></span>&nbsp;<span class="element-name">build</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
