<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>LlmAgent (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.agents, class: LlmAgent">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/LlmAgent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.agents</a></li>
<li><a href="LlmAgent.html" class="current-selection">LlmAgent</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#nested-class-summary" tabindex="0">Nested Class Summary</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#builder()" tabindex="0">builder()</a></li>
<li><a href="#runAsyncImpl(com.google.adk.agents.InvocationContext)" tabindex="0">runAsyncImpl(InvocationContext)</a></li>
<li><a href="#runLiveImpl(com.google.adk.agents.InvocationContext)" tabindex="0">runLiveImpl(InvocationContext)</a></li>
<li><a href="#instruction()" tabindex="0">instruction()</a></li>
<li><a href="#globalInstruction()" tabindex="0">globalInstruction()</a></li>
<li><a href="#model()" tabindex="0">model()</a></li>
<li><a href="#planning()" tabindex="0">planning()</a></li>
<li><a href="#generateContentConfig()" tabindex="0">generateContentConfig()</a></li>
<li><a href="#exampleProvider()" tabindex="0">exampleProvider()</a></li>
<li><a href="#includeContents()" tabindex="0">includeContents()</a></li>
<li><a href="#tools()" tabindex="0">tools()</a></li>
<li><a href="#disallowTransferToParent()" tabindex="0">disallowTransferToParent()</a></li>
<li><a href="#disallowTransferToPeers()" tabindex="0">disallowTransferToPeers()</a></li>
<li><a href="#beforeModelCallback()" tabindex="0">beforeModelCallback()</a></li>
<li><a href="#afterModelCallback()" tabindex="0">afterModelCallback()</a></li>
<li><a href="#beforeToolCallback()" tabindex="0">beforeToolCallback()</a></li>
<li><a href="#afterToolCallback()" tabindex="0">afterToolCallback()</a></li>
<li><a href="#inputSchema()" tabindex="0">inputSchema()</a></li>
<li><a href="#outputSchema()" tabindex="0">outputSchema()</a></li>
<li><a href="#executor()" tabindex="0">executor()</a></li>
<li><a href="#outputKey()" tabindex="0">outputKey()</a></li>
<li><a href="#resolvedModel()" tabindex="0">resolvedModel()</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class LlmAgent" class="title">Class LlmAgent</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="BaseAgent.html" title="class in com.google.adk.agents">com.google.adk.agents.BaseAgent</a>
<div class="inheritance">com.google.adk.agents.LlmAgent</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">LlmAgent</span>
<span class="extends-implements">extends <a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="block">The LLM-based agent.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="LlmAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-last even-row-color">
<div class="block">Builder for <a href="LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
<div class="col-first odd-row-color"><code>static enum&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="LlmAgent.IncludeContents.html" class="type-name-link" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Enum to define if contents of previous events should be included in requests to the underlying
 LLM.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterModelCallback()" class="member-name-link">afterModelCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#afterToolCallback()" class="member-name-link">afterToolCallback</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeModelCallback()" class="member-name-link">beforeModelCallback</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#beforeToolCallback()" class="member-name-link">beforeToolCallback</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a <a href="LlmAgent.Builder.html" title="class in com.google.adk.agents"><code>LlmAgent.Builder</code></a> for <a href="LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#disallowTransferToParent()" class="member-name-link">disallowTransferToParent</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#disallowTransferToPeers()" class="member-name-link">disallowTransferToPeers</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="../examples/BaseExampleProvider.html" title="interface in com.google.adk.examples">BaseExampleProvider</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#exampleProvider()" class="member-name-link">exampleProvider</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/Executor.html" title="class or interface in java.util.concurrent" class="external-link">Executor</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#executor()" class="member-name-link">executor</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;com.google.genai.types.GenerateContentConfig&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateContentConfig()" class="member-name-link">generateContentConfig</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#globalInstruction()" class="member-name-link">globalInstruction</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#includeContents()" class="member-name-link">includeContents</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;com.google.genai.types.Schema&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#inputSchema()" class="member-name-link">inputSchema</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#instruction()" class="member-name-link">instruction</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="../models/Model.html" title="class in com.google.adk.models">Model</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#model()" class="member-name-link">model</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#outputKey()" class="member-name-link">outputKey</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;com.google.genai.types.Schema&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#outputSchema()" class="member-name-link">outputSchema</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>boolean</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#planning()" class="member-name-link">planning</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="../models/Model.html" title="class in com.google.adk.models">Model</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#resolvedModel()" class="member-name-link">resolvedModel</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#tools()" class="member-name-link">tools</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.google.adk.agents.BaseAgent">Methods inherited from class&nbsp;com.google.adk.agents.<a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></h3>
<code><a href="BaseAgent.html#afterAgentCallback()">afterAgentCallback</a>, <a href="BaseAgent.html#beforeAgentCallback()">beforeAgentCallback</a>, <a href="BaseAgent.html#description()">description</a>, <a href="BaseAgent.html#findAgent(java.lang.String)">findAgent</a>, <a href="BaseAgent.html#findSubAgent(java.lang.String)">findSubAgent</a>, <a href="BaseAgent.html#name()">name</a>, <a href="BaseAgent.html#parentAgent()">parentAgent</a>, <a href="BaseAgent.html#parentAgent(com.google.adk.agents.BaseAgent)">parentAgent</a>, <a href="BaseAgent.html#rootAgent()">rootAgent</a>, <a href="BaseAgent.html#runAsync(com.google.adk.agents.InvocationContext)">runAsync</a>, <a href="BaseAgent.html#runLive(com.google.adk.agents.InvocationContext)">runLive</a>, <a href="BaseAgent.html#subAgents()">subAgents</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="builder()">
<h3>builder</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span>&nbsp;<span class="element-name">builder</span>()</div>
<div class="block">Returns a <a href="LlmAgent.Builder.html" title="class in com.google.adk.agents"><code>LlmAgent.Builder</code></a> for <a href="LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="runAsyncImpl(com.google.adk.agents.InvocationContext)">
<h3>runAsyncImpl</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Flowable&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">runAsyncImpl</span><wbr><span class="parameters">(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)">runAsyncImpl</a></code>&nbsp;in class&nbsp;<code><a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="runLiveImpl(com.google.adk.agents.InvocationContext)">
<h3>runLiveImpl</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Flowable&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">runLiveImpl</span><wbr><span class="parameters">(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)">runLiveImpl</a></code>&nbsp;in class&nbsp;<code><a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="instruction()">
<h3>instruction</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">instruction</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="globalInstruction()">
<h3>globalInstruction</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">globalInstruction</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="model()">
<h3>model</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="../models/Model.html" title="class in com.google.adk.models">Model</a>&gt;</span>&nbsp;<span class="element-name">model</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="planning()">
<h3>planning</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">planning</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="generateContentConfig()">
<h3>generateContentConfig</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;com.google.genai.types.GenerateContentConfig&gt;</span>&nbsp;<span class="element-name">generateContentConfig</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="exampleProvider()">
<h3>exampleProvider</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="../examples/BaseExampleProvider.html" title="interface in com.google.adk.examples">BaseExampleProvider</a>&gt;</span>&nbsp;<span class="element-name">exampleProvider</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="includeContents()">
<h3>includeContents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></span>&nbsp;<span class="element-name">includeContents</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="tools()">
<h3>tools</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;</span>&nbsp;<span class="element-name">tools</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="disallowTransferToParent()">
<h3>disallowTransferToParent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">disallowTransferToParent</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="disallowTransferToPeers()">
<h3>disallowTransferToPeers</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">boolean</span>&nbsp;<span class="element-name">disallowTransferToPeers</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeModelCallback()">
<h3>beforeModelCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a>&gt;</span>&nbsp;<span class="element-name">beforeModelCallback</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterModelCallback()">
<h3>afterModelCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a>&gt;</span>&nbsp;<span class="element-name">afterModelCallback</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="beforeToolCallback()">
<h3>beforeToolCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a>&gt;</span>&nbsp;<span class="element-name">beforeToolCallback</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="afterToolCallback()">
<h3>afterToolCallback</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a>&gt;</span>&nbsp;<span class="element-name">afterToolCallback</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="inputSchema()">
<h3>inputSchema</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;com.google.genai.types.Schema&gt;</span>&nbsp;<span class="element-name">inputSchema</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="outputSchema()">
<h3>outputSchema</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;com.google.genai.types.Schema&gt;</span>&nbsp;<span class="element-name">outputSchema</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="executor()">
<h3>executor</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/Executor.html" title="class or interface in java.util.concurrent" class="external-link">Executor</a>&gt;</span>&nbsp;<span class="element-name">executor</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="outputKey()">
<h3>outputKey</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</span>&nbsp;<span class="element-name">outputKey</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="resolvedModel()">
<h3>resolvedModel</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="../models/Model.html" title="class in com.google.adk.models">Model</a></span>&nbsp;<span class="element-name">resolvedModel</span>()</div>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
