<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>SequentialAgent (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.agents, class: SequentialAgent">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/SequentialAgent.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.agents</a></li>
<li><a href="SequentialAgent.html" class="current-selection">SequentialAgent</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#nested-class-summary" tabindex="0">Nested Class Summary</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#builder()" tabindex="0">builder()</a></li>
<li><a href="#runAsyncImpl(com.google.adk.agents.InvocationContext)" tabindex="0">runAsyncImpl(InvocationContext)</a></li>
<li><a href="#runLiveImpl(com.google.adk.agents.InvocationContext)" tabindex="0">runLiveImpl(InvocationContext)</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class SequentialAgent" class="title">Class SequentialAgent</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="BaseAgent.html" title="class in com.google.adk.agents">com.google.adk.agents.BaseAgent</a>
<div class="inheritance">com.google.adk.agents.SequentialAgent</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">SequentialAgent</span>
<span class="extends-implements">extends <a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="block">An agent that runs its sub-agents sequentially.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="SequentialAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent.Builder</a></code></div>
<div class="col-last even-row-color">
<div class="block">Builder for <a href="SequentialAgent.html" title="class in com.google.adk.agents"><code>SequentialAgent</code></a>.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">&nbsp;</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.google.adk.agents.BaseAgent">Methods inherited from class&nbsp;com.google.adk.agents.<a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></h3>
<code><a href="BaseAgent.html#afterAgentCallback()">afterAgentCallback</a>, <a href="BaseAgent.html#beforeAgentCallback()">beforeAgentCallback</a>, <a href="BaseAgent.html#description()">description</a>, <a href="BaseAgent.html#findAgent(java.lang.String)">findAgent</a>, <a href="BaseAgent.html#findSubAgent(java.lang.String)">findSubAgent</a>, <a href="BaseAgent.html#name()">name</a>, <a href="BaseAgent.html#parentAgent()">parentAgent</a>, <a href="BaseAgent.html#parentAgent(com.google.adk.agents.BaseAgent)">parentAgent</a>, <a href="BaseAgent.html#rootAgent()">rootAgent</a>, <a href="BaseAgent.html#runAsync(com.google.adk.agents.InvocationContext)">runAsync</a>, <a href="BaseAgent.html#runLive(com.google.adk.agents.InvocationContext)">runLive</a>, <a href="BaseAgent.html#subAgents()">subAgents</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="builder()">
<h3>builder</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></span>&nbsp;<span class="element-name">builder</span>()</div>
</div>
</section>
</li>
<li>
<section class="detail" id="runAsyncImpl(com.google.adk.agents.InvocationContext)">
<h3>runAsyncImpl</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Flowable&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">runAsyncImpl</span><wbr><span class="parameters">(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)">runAsyncImpl</a></code>&nbsp;in class&nbsp;<code><a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="runLiveImpl(com.google.adk.agents.InvocationContext)">
<h3>runLiveImpl</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Flowable&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">runLiveImpl</span><wbr><span class="parameters">(<a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)">runLiveImpl</a></code>&nbsp;in class&nbsp;<code><a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
