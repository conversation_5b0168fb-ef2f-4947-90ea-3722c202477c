<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.agents.BaseAgent (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.agents, class: BaseAgent">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../BaseAgent.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.agents</a></li>
<li><a href="../BaseAgent.html" class="current-selection">BaseAgent</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.agents.BaseAgent" class="title">Uses of Class<br>com.google.adk.agents.BaseAgent</h1>
</div>
<div class="caption"><span>Packages that use <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.runner">com.google.adk.runner</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.tools">com.google.adk.tools</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a> in <a href="../package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Subclasses of <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a> in <a href="../package-summary.html">com.google.adk.agents</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../LlmAgent.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent</a></code></div>
<div class="col-last even-row-color">
<div class="block">The LLM-based agent.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../LoopAgent.html" class="type-name-link" title="class in com.google.adk.agents">LoopAgent</a></code></div>
<div class="col-last odd-row-color">
<div class="block">An agent that runs its sub-agents sequentially in a loop.</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../ParallelAgent.html" class="type-name-link" title="class in com.google.adk.agents">ParallelAgent</a></code></div>
<div class="col-last even-row-color">
<div class="block">A shell agent that runs its sub-agents in parallel in isolated manner.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../SequentialAgent.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent</a></code></div>
<div class="col-last odd-row-color">
<div class="block">An agent that runs its sub-agents sequentially.</div>
</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.agents</a> that return <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#agent()" class="member-name-link">agent</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#findAgent(java.lang.String)" class="member-name-link">findAgent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color">
<div class="block">Finds an agent (this or descendant) by name.</div>
</div>
<div class="col-first even-row-color"><code>@Nullable <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#findSubAgent(java.lang.String)" class="member-name-link">findSubAgent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last even-row-color">
<div class="block">Recursively search sub agent by name.</div>
</div>
<div class="col-first odd-row-color"><code><a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#parentAgent()" class="member-name-link">parentAgent</a>()</code></div>
<div class="col-last odd-row-color">
<div class="block">Retrieves the parent agent in the agent tree.</div>
</div>
<div class="col-first even-row-color"><code><a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#rootAgent()" class="member-name-link">rootAgent</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Returns the root agent for this agent by traversing up the parent chain.</div>
</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.agents</a> that return types with arguments of type <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;? extends <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#subAgents()" class="member-name-link">subAgents</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>void</code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#agent(com.google.adk.agents.BaseAgent)" class="member-name-link">agent</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">create</a><wbr>(<a href="../../sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>&nbsp;sessionService,
 <a href="../../artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>&nbsp;artifactService,
 <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a>&nbsp;liveRequestQueue,
 <a href="../RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,java.lang.String,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">create</a><wbr>(<a href="../../sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>&nbsp;sessionService,
 <a href="../../artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>&nbsp;artifactService,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;invocationId,
 <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 com.google.genai.types.Content&nbsp;userContent,
 <a href="../RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected void</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#parentAgent(com.google.adk.agents.BaseAgent)" class="member-name-link">parentAgent</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;parentAgent)</code></div>
<div class="col-last odd-row-color">
<div class="block">Sets the parent agent.</div>
</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>...&nbsp;subAgents)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LoopAgent.Builder.</span><code><a href="../LoopAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>...&nbsp;subAgents)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ParallelAgent.Builder.</span><code><a href="../ParallelAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>...&nbsp;subAgents)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">SequentialAgent.Builder.</span><code><a href="../SequentialAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>...&nbsp;subAgents)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Method parameters in <a href="../package-summary.html">com.google.adk.agents</a> with type arguments of type <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LoopAgent.Builder.</span><code><a href="../LoopAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ParallelAgent.Builder.</span><code><a href="../ParallelAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">SequentialAgent.Builder.</span><code><a href="../SequentialAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Constructor parameters in <a href="../package-summary.html">com.google.adk.agents</a> with type arguments of type <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../BaseAgent.html#%3Cinit%3E(java.lang.String,java.lang.String,java.util.List,com.google.adk.agents.Callbacks.BeforeAgentCallback,com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">BaseAgent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents,
 <a href="../Callbacks.BeforeAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a>&nbsp;beforeAgentCallback,
 <a href="../Callbacks.AfterAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a>&nbsp;afterAgentCallback)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.runner">
<h2>Uses of <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a> in <a href="../../runner/package-summary.html">com.google.adk.runner</a></h2>
<div class="caption"><span>Methods in <a href="../../runner/package-summary.html">com.google.adk.runner</a> that return <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#agent()" class="member-name-link">agent</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Constructors in <a href="../../runner/package-summary.html">com.google.adk.runner</a> with parameters of type <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../../runner/InMemoryRunner.html#%3Cinit%3E(com.google.adk.agents.BaseAgent)" class="member-name-link">InMemoryRunner</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../../runner/InMemoryRunner.html#%3Cinit%3E(com.google.adk.agents.BaseAgent,java.lang.String)" class="member-name-link">InMemoryRunner</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../../runner/Runner.html#%3Cinit%3E(com.google.adk.agents.BaseAgent,java.lang.String,com.google.adk.artifacts.BaseArtifactService,com.google.adk.sessions.BaseSessionService)" class="member-name-link">Runner</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="../../artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>&nbsp;artifactService,
 <a href="../../sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>&nbsp;sessionService)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools">
<h2>Uses of <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a> in <a href="../../tools/package-summary.html">com.google.adk.tools</a></h2>
<div class="caption"><span>Methods in <a href="../../tools/package-summary.html">com.google.adk.tools</a> with parameters of type <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static <a href="../../tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">AgentTool.</span><code><a href="../../tools/AgentTool.html#create(com.google.adk.agents.BaseAgent)" class="member-name-link">create</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../../tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">AgentTool.</span><code><a href="../../tools/AgentTool.html#create(com.google.adk.agents.BaseAgent,boolean)" class="member-name-link">create</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 boolean&nbsp;skipSummarization)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Constructors in <a href="../../tools/package-summary.html">com.google.adk.tools</a> with parameters of type <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected </code></div>
<div class="col-second even-row-color"><code><a href="../../tools/AgentTool.html#%3Cinit%3E(com.google.adk.agents.BaseAgent,boolean)" class="member-name-link">AgentTool</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 boolean&nbsp;skipSummarization)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
