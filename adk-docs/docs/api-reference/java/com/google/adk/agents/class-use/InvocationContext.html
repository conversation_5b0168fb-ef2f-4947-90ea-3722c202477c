<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.agents.InvocationContext (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.agents, class: InvocationContext">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../InvocationContext.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.agents</a></li>
<li><a href="../InvocationContext.html" class="current-selection">InvocationContext</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.agents.InvocationContext" class="title">Uses of Class<br>com.google.adk.agents.InvocationContext</h1>
</div>
<div class="caption"><span>Packages that use <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk">com.google.adk</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.flows">com.google.adk.flows</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.flows.llmflows">com.google.adk.flows.llmflows</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.tools">com.google.adk.tools</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk">
<h2>Uses of <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a> in <a href="../../package-summary.html">com.google.adk</a></h2>
<div class="caption"><span>Methods in <a href="../../package-summary.html">com.google.adk</a> with parameters of type <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static void</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Telemetry.</span><code><a href="../../Telemetry.html#traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">traceCallLlm</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../../models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">
<div class="block">Traces a call to the LLM.</div>
</div>
<div class="col-first odd-row-color"><code>static void</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Telemetry.</span><code><a href="../../Telemetry.html#traceSendData(com.google.adk.agents.InvocationContext,java.lang.String,java.util.List)" class="member-name-link">traceSendData</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.genai.types.Content&gt;&nbsp;data)</code></div>
<div class="col-last odd-row-color">
<div class="block">Traces the sending of data (history or new content) to the agent/model.</div>
</div>
<div class="col-first even-row-color"><code>static void</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Telemetry.</span><code><a href="../../Telemetry.html#traceToolResponse(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.events.Event)" class="member-name-link">traceToolResponse</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionResponseEvent)</code></div>
<div class="col-last even-row-color">
<div class="block">Traces tool response event.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a> in <a href="../package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Fields in <a href="../package-summary.html">com.google.adk.agents</a> declared as <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected final <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ReadonlyContext.</span><code><a href="../ReadonlyContext.html#invocationContext" class="member-name-link">invocationContext</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.agents</a> that return <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#copyOf(com.google.adk.agents.InvocationContext)" class="member-name-link">copyOf</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;other)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">create</a><wbr>(<a href="../../sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>&nbsp;sessionService,
 <a href="../../artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>&nbsp;artifactService,
 <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a>&nbsp;liveRequestQueue,
 <a href="../RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,java.lang.String,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">create</a><wbr>(<a href="../../sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>&nbsp;sessionService,
 <a href="../../artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>&nbsp;artifactService,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;invocationId,
 <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 com.google.genai.types.Content&nbsp;userContent,
 <a href="../RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.AfterToolCallback.</span><code><a href="../Callbacks.AfterToolCallback.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)" class="member-name-link">call</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;response)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.AfterToolCallbackSync.</span><code><a href="../Callbacks.AfterToolCallbackSync.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)" class="member-name-link">call</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;response)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.BeforeToolCallback.</span><code><a href="../Callbacks.BeforeToolCallback.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">call</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.BeforeToolCallbackSync.</span><code><a href="../Callbacks.BeforeToolCallbackSync.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">call</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../InvocationContext.html#copyOf(com.google.adk.agents.InvocationContext)" class="member-name-link">copyOf</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;other)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#runAsync(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsync</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;parentContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected abstract io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.</span><code><a href="../LlmAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LoopAgent.</span><code><a href="../LoopAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">ParallelAgent.</span><code><a href="../ParallelAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">SequentialAgent.</span><code><a href="../SequentialAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;parentContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected abstract io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../BaseAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.</span><code><a href="../LlmAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LoopAgent.</span><code><a href="../LoopAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">ParallelAgent.</span><code><a href="../ParallelAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">SequentialAgent.</span><code><a href="../SequentialAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Constructors in <a href="../package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../CallbackContext.html#%3Cinit%3E(com.google.adk.agents.InvocationContext,com.google.adk.events.EventActions)" class="member-name-link">CallbackContext</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../../events/EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;eventActions)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../ReadonlyContext.html#%3Cinit%3E(com.google.adk.agents.InvocationContext)" class="member-name-link">ReadonlyContext</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows">
<h2>Uses of <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a> in <a href="../../flows/package-summary.html">com.google.adk.flows</a></h2>
<div class="caption"><span>Methods in <a href="../../flows/package-summary.html">com.google.adk.flows</a> with parameters of type <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseFlow.</span><code><a href="../../flows/BaseFlow.html#run(com.google.adk.agents.InvocationContext)" class="member-name-link">run</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">
<div class="block">Run this flow.</div>
</div>
<div class="col-first odd-row-color"><code>default io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseFlow.</span><code><a href="../../flows/BaseFlow.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows.llmflows">
<h2>Uses of <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a> in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></h2>
<div class="caption"><span>Methods in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a> with parameters of type <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Functions.</span><code><a href="../../flows/llmflows/Functions.html#handleFunctionCalls(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,java.util.Map)" class="member-name-link">handleFunctionCalls</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionCallEvent,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.ResponseProcessor.ResponseProcessingResult&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">postprocess</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;baseEventForLlmResponse,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../../models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last odd-row-color">
<div class="block">Post-processes the LLM response after receiving it from the LLM.</div>
</div>
<div class="col-first even-row-color"><code>protected com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#preprocess(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">preprocess</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last even-row-color">
<div class="block">Pre-processes the LLM request before sending it to the LLM.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">AgentTransfer.</span><code><a href="../../flows/llmflows/AgentTransfer.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Basic.</span><code><a href="../../flows/llmflows/Basic.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Contents.</span><code><a href="../../flows/llmflows/Contents.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Examples.</span><code><a href="../../flows/llmflows/Examples.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Identity.</span><code><a href="../../flows/llmflows/Identity.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Instructions.</span><code><a href="../../flows/llmflows/Instructions.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#run(com.google.adk.agents.InvocationContext)" class="member-name-link">run</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools">
<h2>Uses of <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a> in <a href="../../tools/package-summary.html">com.google.adk.tools</a></h2>
<div class="caption"><span>Methods in <a href="../../tools/package-summary.html">com.google.adk.tools</a> with parameters of type <a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static <a href="../../tools/ToolContext.Builder.html" title="class in com.google.adk.tools">ToolContext.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ToolContext.</span><code><a href="../../tools/ToolContext.html#builder(com.google.adk.agents.InvocationContext)" class="member-name-link">builder</a><wbr>(<a href="../InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
