<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.agents.LlmAgent.Builder (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.agents, class: LlmAgent, class: Builder">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../LlmAgent.Builder.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.agents</a></li>
<li><a href="../LlmAgent.html">LlmAgent</a></li>
<li><a href="../LlmAgent.Builder.html" class="current-selection">Builder</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.agents.LlmAgent.Builder" class="title">Uses of Class<br>com.google.adk.agents.LlmAgent.Builder</h1>
</div>
<div class="caption"><span>Packages that use <a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a> in <a href="../package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.agents</a> that return <a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">afterAgentCallback</a><wbr>(<a href="../Callbacks.AfterAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a>&nbsp;afterAgentCallback)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#afterAgentCallbackSync(com.google.adk.agents.Callbacks.AfterAgentCallbackSync)" class="member-name-link">afterAgentCallbackSync</a><wbr>(<a href="../Callbacks.AfterAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a>&nbsp;afterAgentCallbackSync)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#afterModelCallback(com.google.adk.agents.Callbacks.AfterModelCallback)" class="member-name-link">afterModelCallback</a><wbr>(<a href="../Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a>&nbsp;afterModelCallback)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#afterModelCallbackSync(com.google.adk.agents.Callbacks.AfterModelCallbackSync)" class="member-name-link">afterModelCallbackSync</a><wbr>(<a href="../Callbacks.AfterModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a>&nbsp;afterModelCallbackSync)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#afterToolCallback(com.google.adk.agents.Callbacks.AfterToolCallback)" class="member-name-link">afterToolCallback</a><wbr>(<a href="../Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a>&nbsp;afterToolCallback)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#afterToolCallbackSync(com.google.adk.agents.Callbacks.AfterToolCallbackSync)" class="member-name-link">afterToolCallbackSync</a><wbr>(<a href="../Callbacks.AfterToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a>&nbsp;afterToolCallbackSync)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)" class="member-name-link">beforeAgentCallback</a><wbr>(<a href="../Callbacks.BeforeAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a>&nbsp;beforeAgentCallback)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#beforeAgentCallbackSync(com.google.adk.agents.Callbacks.BeforeAgentCallbackSync)" class="member-name-link">beforeAgentCallbackSync</a><wbr>(<a href="../Callbacks.BeforeAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a>&nbsp;beforeAgentCallbackSync)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#beforeModelCallback(com.google.adk.agents.Callbacks.BeforeModelCallback)" class="member-name-link">beforeModelCallback</a><wbr>(<a href="../Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a>&nbsp;beforeModelCallback)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#beforeModelCallbackSync(com.google.adk.agents.Callbacks.BeforeModelCallbackSync)" class="member-name-link">beforeModelCallbackSync</a><wbr>(<a href="../Callbacks.BeforeModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a>&nbsp;beforeModelCallbackSync)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#beforeToolCallback(com.google.adk.agents.Callbacks.BeforeToolCallback)" class="member-name-link">beforeToolCallback</a><wbr>(<a href="../Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a>&nbsp;beforeToolCallback)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#beforeToolCallbackSync(com.google.adk.agents.Callbacks.BeforeToolCallbackSync)" class="member-name-link">beforeToolCallbackSync</a><wbr>(<a href="../Callbacks.BeforeToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a>&nbsp;beforeToolCallbackSync)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static <a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.</span><code><a href="../LlmAgent.html#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Returns a <a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents"><code>LlmAgent.Builder</code></a> for <a href="../LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#description(java.lang.String)" class="member-name-link">description</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;description)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#disallowTransferToParent(boolean)" class="member-name-link">disallowTransferToParent</a><wbr>(boolean&nbsp;disallowTransferToParent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#disallowTransferToPeers(boolean)" class="member-name-link">disallowTransferToPeers</a><wbr>(boolean&nbsp;disallowTransferToPeers)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#exampleProvider(com.google.adk.examples.BaseExampleProvider)" class="member-name-link">exampleProvider</a><wbr>(<a href="../../examples/BaseExampleProvider.html" title="interface in com.google.adk.examples">BaseExampleProvider</a>&nbsp;exampleProvider)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#exampleProvider(com.google.adk.examples.Example...)" class="member-name-link">exampleProvider</a><wbr>(<a href="../../examples/Example.html" title="class in com.google.adk.examples">Example</a>...&nbsp;examples)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#exampleProvider(java.util.List)" class="member-name-link">exampleProvider</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../examples/Example.html" title="class in com.google.adk.examples">Example</a>&gt;&nbsp;examples)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#executor(java.util.concurrent.Executor)" class="member-name-link">executor</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/Executor.html" title="class or interface in java.util.concurrent" class="external-link">Executor</a>&nbsp;executor)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#generateContentConfig(com.google.genai.types.GenerateContentConfig)" class="member-name-link">generateContentConfig</a><wbr>(com.google.genai.types.GenerateContentConfig&nbsp;generateContentConfig)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#globalInstruction(java.lang.String)" class="member-name-link">globalInstruction</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;globalInstruction)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#includeContents(com.google.adk.agents.LlmAgent.IncludeContents)" class="member-name-link">includeContents</a><wbr>(<a href="../LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a>&nbsp;includeContents)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#inputSchema(com.google.genai.types.Schema)" class="member-name-link">inputSchema</a><wbr>(com.google.genai.types.Schema&nbsp;inputSchema)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#instruction(java.lang.String)" class="member-name-link">instruction</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;instruction)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#model(com.google.adk.models.BaseLlm)" class="member-name-link">model</a><wbr>(<a href="../../models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a>&nbsp;model)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#model(java.lang.String)" class="member-name-link">model</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#name(java.lang.String)" class="member-name-link">name</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;name)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#outputKey(java.lang.String)" class="member-name-link">outputKey</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;outputKey)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#outputSchema(com.google.genai.types.Schema)" class="member-name-link">outputSchema</a><wbr>(com.google.genai.types.Schema&nbsp;outputSchema)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#planning(boolean)" class="member-name-link">planning</a><wbr>(boolean&nbsp;planning)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents</a><wbr>(<a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>...&nbsp;subAgents)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&gt;&nbsp;subAgents)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#tools(com.google.adk.tools.BaseTool...)" class="member-name-link">tools</a><wbr>(<a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>...&nbsp;tools)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../LlmAgent.Builder.html#tools(java.util.List)" class="member-name-link">tools</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
