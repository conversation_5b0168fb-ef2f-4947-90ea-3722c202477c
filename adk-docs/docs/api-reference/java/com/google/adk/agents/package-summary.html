<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>com.google.adk.agents (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.agents">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html" class="current-selection">com.google.adk.agents</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#related-package-summary" tabindex="0">Related Packages</a></li>
<li><a href="#class-summary" tabindex="0">Classes and Interfaces</a></li>
</ol>
</nav>
<main role="main">
<div class="header">
<h1 title="Package com.google.adk.agents" class="title">Package com.google.adk.agents</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">com.google.adk.agents</span></div>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">com.google.adk</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab1" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab1', 2)" class="table-tab">Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab3" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab3', 2)" class="table-tab">Enum Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel" aria-labelledby="class-summary-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Base class for all agents.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The context of various callbacks for an agent invocation.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="Callbacks.html" title="class in com.google.adk.agents">Callbacks</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="Callbacks.AfterAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Callbacks.AfterAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Callbacks.AfterModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Callbacks.AfterToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="Callbacks.BeforeAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Callbacks.BeforeAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Callbacks.BeforeModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab1"><a href="Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first even-row-color class-summary class-summary-tab1"><a href="Callbacks.BeforeToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a></div>
<div class="col-last even-row-color class-summary class-summary-tab1">&nbsp;</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The context for an agent invocation.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Represents a request to be sent to a live connection to the LLM model.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Builder for constructing <a href="LiveRequest.html" title="class in com.google.adk.agents"><code>LiveRequest</code></a> instances.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A queue of live requests to be sent to the model.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">The LLM-based agent.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Builder for <a href="LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">Enum to define if contents of previous events should be included in requests to the underlying
 LLM.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="LoopAgent.html" title="class in com.google.adk.agents">LoopAgent</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">An agent that runs its sub-agents sequentially in a loop.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Builder for <a href="LoopAgent.html" title="class in com.google.adk.agents"><code>LoopAgent</code></a>.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ParallelAgent.html" title="class in com.google.adk.agents">ParallelAgent</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">A shell agent that runs its sub-agents in parallel in isolated manner.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Builder for <a href="ParallelAgent.html" title="class in com.google.adk.agents"><code>ParallelAgent</code></a>.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ReadonlyContext.html" title="class in com.google.adk.agents">ReadonlyContext</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Provides read-only access to the context of an agent run.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Configuration to modify an agent's LLM's underlying behavior.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Builder for <a href="RunConfig.html" title="class in com.google.adk.agents"><code>RunConfig</code></a>.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab3"><a href="RunConfig.StreamingMode.html" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab3">
<div class="block">Streaming mode for the runner.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SequentialAgent.html" title="class in com.google.adk.agents">SequentialAgent</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">An agent that runs its sub-agents sequentially.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Builder for <a href="SequentialAgent.html" title="class in com.google.adk.agents"><code>SequentialAgent</code></a>.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
