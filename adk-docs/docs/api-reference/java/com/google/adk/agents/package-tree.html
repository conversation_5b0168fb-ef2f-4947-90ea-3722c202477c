<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>com.google.adk.agents Class Hierarchy (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="tree: package: com.google.adk.agents">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li><a href="package-use.html">Use</a></li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html" class="current-selection">com.google.adk.agents</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package com.google.adk.agents</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">com.google.adk.agents.<a href="BaseAgent.html" class="type-name-link" title="class in com.google.adk.agents">BaseAgent</a>
<ul>
<li class="circle">com.google.adk.agents.<a href="LlmAgent.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent</a></li>
<li class="circle">com.google.adk.agents.<a href="LoopAgent.html" class="type-name-link" title="class in com.google.adk.agents">LoopAgent</a></li>
<li class="circle">com.google.adk.agents.<a href="ParallelAgent.html" class="type-name-link" title="class in com.google.adk.agents">ParallelAgent</a></li>
<li class="circle">com.google.adk.agents.<a href="SequentialAgent.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent</a></li>
</ul>
</li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.html" class="type-name-link" title="class in com.google.adk.agents">Callbacks</a></li>
<li class="circle">com.google.adk.agents.<a href="InvocationContext.html" class="type-name-link" title="class in com.google.adk.agents">InvocationContext</a></li>
<li class="circle">com.google.adk.<a href="../JsonBaseModel.html" class="type-name-link" title="class in com.google.adk">JsonBaseModel</a>
<ul>
<li class="circle">com.google.adk.agents.<a href="LiveRequest.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequest</a></li>
</ul>
</li>
<li class="circle">com.google.adk.agents.<a href="LiveRequest.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequest.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="LiveRequestQueue.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequestQueue</a></li>
<li class="circle">com.google.adk.agents.<a href="LlmAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="LoopAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LoopAgent.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="ParallelAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">ParallelAgent.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="ReadonlyContext.html" class="type-name-link" title="class in com.google.adk.agents">ReadonlyContext</a>
<ul>
<li class="circle">com.google.adk.agents.<a href="CallbackContext.html" class="type-name-link" title="class in com.google.adk.agents">CallbackContext</a></li>
</ul>
</li>
<li class="circle">com.google.adk.agents.<a href="RunConfig.html" class="type-name-link" title="class in com.google.adk.agents">RunConfig</a></li>
<li class="circle">com.google.adk.agents.<a href="RunConfig.Builder.html" class="type-name-link" title="class in com.google.adk.agents">RunConfig.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="SequentialAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent.Builder</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">com.google.adk.agents.<a href="Callbacks.AfterAgentCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.AfterAgentCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.AfterModelCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.AfterModelCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.AfterToolCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.AfterToolCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.BeforeAgentCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.BeforeAgentCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.BeforeModelCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.BeforeModelCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.BeforeToolCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="Callbacks.BeforeToolCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a></li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">com.google.adk.agents.<a href="LlmAgent.IncludeContents.html" class="type-name-link" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></li>
<li class="circle">com.google.adk.agents.<a href="RunConfig.StreamingMode.html" class="type-name-link" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
