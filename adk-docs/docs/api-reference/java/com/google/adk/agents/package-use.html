<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Package com.google.adk.agents (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.agents">
<meta name="generator" content="javadoc/PackageUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-use-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html" class="current-selection">com.google.adk.agents</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Package com.google.adk.agents" class="title">Uses of Package<br>com.google.adk.agents</h1>
</div>
<div class="caption"><span>Packages that use <a href="package-summary.html">com.google.adk.agents</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk">com.google.adk</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.flows">com.google.adk.flows</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.flows.llmflows">com.google.adk.flows.llmflows</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.runner">com.google.adk.runner</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.tools">com.google.adk.tools</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="package-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.agents</a> used by <a href="../package-summary.html">com.google.adk</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/InvocationContext.html#com.google.adk">InvocationContext</a></div>
<div class="col-last even-row-color">
<div class="block">The context for an agent invocation.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.agents">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.agents</a> used by <a href="package-summary.html">com.google.adk.agents</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/BaseAgent.html#com.google.adk.agents">BaseAgent</a></div>
<div class="col-last even-row-color">
<div class="block">Base class for all agents.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/CallbackContext.html#com.google.adk.agents">CallbackContext</a></div>
<div class="col-last odd-row-color">
<div class="block">The context of various callbacks for an agent invocation.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/Callbacks.AfterAgentCallback.html#com.google.adk.agents">Callbacks.AfterAgentCallback</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="class-use/Callbacks.AfterAgentCallbackSync.html#com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="class-use/Callbacks.AfterModelCallback.html#com.google.adk.agents">Callbacks.AfterModelCallback</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="class-use/Callbacks.AfterModelCallbackSync.html#com.google.adk.agents">Callbacks.AfterModelCallbackSync</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="class-use/Callbacks.AfterToolCallback.html#com.google.adk.agents">Callbacks.AfterToolCallback</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="class-use/Callbacks.AfterToolCallbackSync.html#com.google.adk.agents">Callbacks.AfterToolCallbackSync</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="class-use/Callbacks.BeforeAgentCallback.html#com.google.adk.agents">Callbacks.BeforeAgentCallback</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="class-use/Callbacks.BeforeAgentCallbackSync.html#com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="class-use/Callbacks.BeforeModelCallback.html#com.google.adk.agents">Callbacks.BeforeModelCallback</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="class-use/Callbacks.BeforeModelCallbackSync.html#com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="class-use/Callbacks.BeforeToolCallback.html#com.google.adk.agents">Callbacks.BeforeToolCallback</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="class-use/Callbacks.BeforeToolCallbackSync.html#com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="class-use/InvocationContext.html#com.google.adk.agents">InvocationContext</a></div>
<div class="col-last even-row-color">
<div class="block">The context for an agent invocation.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/LiveRequest.html#com.google.adk.agents">LiveRequest</a></div>
<div class="col-last odd-row-color">
<div class="block">Represents a request to be sent to a live connection to the LLM model.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/LiveRequest.Builder.html#com.google.adk.agents">LiveRequest.Builder</a></div>
<div class="col-last even-row-color">
<div class="block">Builder for constructing <a href="LiveRequest.html" title="class in com.google.adk.agents"><code>LiveRequest</code></a> instances.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/LiveRequestQueue.html#com.google.adk.agents">LiveRequestQueue</a></div>
<div class="col-last odd-row-color">
<div class="block">A queue of live requests to be sent to the model.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/LlmAgent.html#com.google.adk.agents">LlmAgent</a></div>
<div class="col-last even-row-color">
<div class="block">The LLM-based agent.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/LlmAgent.Builder.html#com.google.adk.agents">LlmAgent.Builder</a></div>
<div class="col-last odd-row-color">
<div class="block">Builder for <a href="LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/LlmAgent.IncludeContents.html#com.google.adk.agents">LlmAgent.IncludeContents</a></div>
<div class="col-last even-row-color">
<div class="block">Enum to define if contents of previous events should be included in requests to the underlying
 LLM.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/LoopAgent.html#com.google.adk.agents">LoopAgent</a></div>
<div class="col-last odd-row-color">
<div class="block">An agent that runs its sub-agents sequentially in a loop.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/LoopAgent.Builder.html#com.google.adk.agents">LoopAgent.Builder</a></div>
<div class="col-last even-row-color">
<div class="block">Builder for <a href="LoopAgent.html" title="class in com.google.adk.agents"><code>LoopAgent</code></a>.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/ParallelAgent.html#com.google.adk.agents">ParallelAgent</a></div>
<div class="col-last odd-row-color">
<div class="block">A shell agent that runs its sub-agents in parallel in isolated manner.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/ParallelAgent.Builder.html#com.google.adk.agents">ParallelAgent.Builder</a></div>
<div class="col-last even-row-color">
<div class="block">Builder for <a href="ParallelAgent.html" title="class in com.google.adk.agents"><code>ParallelAgent</code></a>.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/ReadonlyContext.html#com.google.adk.agents">ReadonlyContext</a></div>
<div class="col-last odd-row-color">
<div class="block">Provides read-only access to the context of an agent run.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/RunConfig.html#com.google.adk.agents">RunConfig</a></div>
<div class="col-last even-row-color">
<div class="block">Configuration to modify an agent's LLM's underlying behavior.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/RunConfig.Builder.html#com.google.adk.agents">RunConfig.Builder</a></div>
<div class="col-last odd-row-color">
<div class="block">Builder for <a href="RunConfig.html" title="class in com.google.adk.agents"><code>RunConfig</code></a>.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/RunConfig.StreamingMode.html#com.google.adk.agents">RunConfig.StreamingMode</a></div>
<div class="col-last even-row-color">
<div class="block">Streaming mode for the runner.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/SequentialAgent.html#com.google.adk.agents">SequentialAgent</a></div>
<div class="col-last odd-row-color">
<div class="block">An agent that runs its sub-agents sequentially.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/SequentialAgent.Builder.html#com.google.adk.agents">SequentialAgent.Builder</a></div>
<div class="col-last even-row-color">
<div class="block">Builder for <a href="SequentialAgent.html" title="class in com.google.adk.agents"><code>SequentialAgent</code></a>.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.agents</a> used by <a href="../flows/package-summary.html">com.google.adk.flows</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/InvocationContext.html#com.google.adk.flows">InvocationContext</a></div>
<div class="col-last even-row-color">
<div class="block">The context for an agent invocation.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows.llmflows">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.agents</a> used by <a href="../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/InvocationContext.html#com.google.adk.flows.llmflows">InvocationContext</a></div>
<div class="col-last even-row-color">
<div class="block">The context for an agent invocation.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.runner">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.agents</a> used by <a href="../runner/package-summary.html">com.google.adk.runner</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/BaseAgent.html#com.google.adk.runner">BaseAgent</a></div>
<div class="col-last even-row-color">
<div class="block">Base class for all agents.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/LiveRequestQueue.html#com.google.adk.runner">LiveRequestQueue</a></div>
<div class="col-last odd-row-color">
<div class="block">A queue of live requests to be sent to the model.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/RunConfig.html#com.google.adk.runner">RunConfig</a></div>
<div class="col-last even-row-color">
<div class="block">Configuration to modify an agent's LLM's underlying behavior.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.agents</a> used by <a href="../tools/package-summary.html">com.google.adk.tools</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/BaseAgent.html#com.google.adk.tools">BaseAgent</a></div>
<div class="col-last even-row-color">
<div class="block">Base class for all agents.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/CallbackContext.html#com.google.adk.tools">CallbackContext</a></div>
<div class="col-last odd-row-color">
<div class="block">The context of various callbacks for an agent invocation.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/InvocationContext.html#com.google.adk.tools">InvocationContext</a></div>
<div class="col-last even-row-color">
<div class="block">The context for an agent invocation.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/ReadonlyContext.html#com.google.adk.tools">ReadonlyContext</a></div>
<div class="col-last odd-row-color">
<div class="block">Provides read-only access to the context of an agent run.</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
