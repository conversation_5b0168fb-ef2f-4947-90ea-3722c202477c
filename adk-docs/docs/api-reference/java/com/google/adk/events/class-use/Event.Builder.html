<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.events.Event.Builder (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.events, class: Event, class: Builder">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../Event.Builder.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.events</a></li>
<li><a href="../Event.html">Event</a></li>
<li><a href="../Event.Builder.html" class="current-selection">Builder</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.events.Event.Builder" class="title">Uses of Class<br>com.google.adk.events.Event.Builder</h1>
</div>
<div class="caption"><span>Packages that use <a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.events">com.google.adk.events</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.events">
<h2>Uses of <a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a> in <a href="../package-summary.html">com.google.adk.events</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.events</a> that return <a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#actions(com.google.adk.events.EventActions)" class="member-name-link">actions</a><wbr>(<a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#author(java.lang.String)" class="member-name-link">author</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#branch(java.lang.String)" class="member-name-link">branch</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#branch(java.util.Optional)" class="member-name-link">branch</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static <a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.</span><code><a href="../Event.html#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#content(com.google.genai.types.Content)" class="member-name-link">content</a><wbr>(com.google.genai.types.Content&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#content(java.util.Optional)" class="member-name-link">content</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;com.google.genai.types.Content&gt;&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#errorCode(com.google.genai.types.FinishReason)" class="member-name-link">errorCode</a><wbr>(com.google.genai.types.FinishReason&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#errorCode(java.util.Optional)" class="member-name-link">errorCode</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;com.google.genai.types.FinishReason&gt;&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#errorMessage(java.lang.String)" class="member-name-link">errorMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#errorMessage(java.util.Optional)" class="member-name-link">errorMessage</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#groundingMetadata(com.google.genai.types.GroundingMetadata)" class="member-name-link">groundingMetadata</a><wbr>(com.google.genai.types.GroundingMetadata&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#groundingMetadata(java.util.Optional)" class="member-name-link">groundingMetadata</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;com.google.genai.types.GroundingMetadata&gt;&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#id(java.lang.String)" class="member-name-link">id</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#interrupted(java.lang.Boolean)" class="member-name-link">interrupted</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#interrupted(java.util.Optional)" class="member-name-link">interrupted</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&gt;&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#invocationId(java.lang.String)" class="member-name-link">invocationId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#longRunningToolIds(java.util.Optional)" class="member-name-link">longRunningToolIds</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&gt;&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#longRunningToolIds(java.util.Set)" class="member-name-link">longRunningToolIds</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#partial(java.lang.Boolean)" class="member-name-link">partial</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#partial(java.util.Optional)" class="member-name-link">partial</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&gt;&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#timestamp(long)" class="member-name-link">timestamp</a><wbr>(long&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#timestamp(java.util.Optional)" class="member-name-link">timestamp</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Long.html" title="class or interface in java.lang" class="external-link">Long</a>&gt;&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.</span><code><a href="../Event.html#toBuilder()" class="member-name-link">toBuilder</a>()</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#turnComplete(java.lang.Boolean)" class="member-name-link">turnComplete</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#turnComplete(java.util.Optional)" class="member-name-link">turnComplete</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Boolean.html" title="class or interface in java.lang" class="external-link">Boolean</a>&gt;&nbsp;value)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
