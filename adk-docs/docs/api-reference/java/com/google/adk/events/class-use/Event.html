<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.events.Event (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.events, class: Event">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../Event.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.events</a></li>
<li><a href="../Event.html" class="current-selection">Event</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.events.Event" class="title">Uses of Class<br>com.google.adk.events.Event</h1>
</div>
<div class="caption"><span>Packages that use <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk">com.google.adk</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.events">com.google.adk.events</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.flows">com.google.adk.flows</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.flows.llmflows">com.google.adk.flows.llmflows</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.runner">com.google.adk.runner</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.sessions">com.google.adk.sessions</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk">
<h2>Uses of <a href="../Event.html" title="class in com.google.adk.events">Event</a> in <a href="../../package-summary.html">com.google.adk</a></h2>
<div class="caption"><span>Methods in <a href="../../package-summary.html">com.google.adk</a> with parameters of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static void</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Telemetry.</span><code><a href="../../Telemetry.html#traceToolResponse(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.events.Event)" class="member-name-link">traceToolResponse</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionResponseEvent)</code></div>
<div class="col-last even-row-color">
<div class="block">Traces tool response event.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../Event.html" title="class in com.google.adk.events">Event</a> in <a href="../../agents/package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> that return types with arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../../agents/BaseAgent.html#runAsync(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsync</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;parentContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected abstract io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../../agents/BaseAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.</span><code><a href="../../agents/LlmAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LoopAgent.</span><code><a href="../../agents/LoopAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">ParallelAgent.</span><code><a href="../../agents/ParallelAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">SequentialAgent.</span><code><a href="../../agents/SequentialAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../../agents/BaseAgent.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;parentContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected abstract io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseAgent.</span><code><a href="../../agents/BaseAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.</span><code><a href="../../agents/LlmAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LoopAgent.</span><code><a href="../../agents/LoopAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">ParallelAgent.</span><code><a href="../../agents/ParallelAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">SequentialAgent.</span><code><a href="../../agents/SequentialAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.events">
<h2>Uses of <a href="../Event.html" title="class in com.google.adk.events">Event</a> in <a href="../package-summary.html">com.google.adk.events</a></h2>
<div class="caption"><span>Classes in <a href="../package-summary.html">com.google.adk.events</a> that implement interfaces with type arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../EventStream.html" class="type-name-link" title="class in com.google.adk.events">EventStream</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.events</a> that return <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../Event.html" title="class in com.google.adk.events">Event</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#build()" class="member-name-link">build</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../Event.html" title="class in com.google.adk.events">Event</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Event.</span><code><a href="../Event.html#fromJson(java.lang.String)" class="member-name-link">fromJson</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;json)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.events</a> that return types with arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Iterator.html" title="class or interface in java.util" class="external-link">Iterator</a><wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">EventStream.</span><code><a href="../EventStream.html#iterator()" class="member-name-link">iterator</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Constructor parameters in <a href="../package-summary.html">com.google.adk.events</a> with type arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../EventStream.html#%3Cinit%3E(java.util.function.Supplier)" class="member-name-link">EventStream</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/function/Supplier.html" title="class or interface in java.util.function" class="external-link">Supplier</a>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;&nbsp;eventSupplier)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows">
<h2>Uses of <a href="../Event.html" title="class in com.google.adk.events">Event</a> in <a href="../../flows/package-summary.html">com.google.adk.flows</a></h2>
<div class="caption"><span>Methods in <a href="../../flows/package-summary.html">com.google.adk.flows</a> that return types with arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseFlow.</span><code><a href="../../flows/BaseFlow.html#run(com.google.adk.agents.InvocationContext)" class="member-name-link">run</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">
<div class="block">Run this flow.</div>
</div>
<div class="col-first odd-row-color"><code>default io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseFlow.</span><code><a href="../../flows/BaseFlow.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows.llmflows">
<h2>Uses of <a href="../Event.html" title="class in com.google.adk.events">Event</a> in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></h2>
<div class="caption"><span>Methods in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a> that return types with arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Functions.</span><code><a href="../../flows/llmflows/Functions.html#handleFunctionCalls(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,java.util.Map)" class="member-name-link">handleFunctionCalls</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionCallEvent,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#run(com.google.adk.agents.InvocationContext)" class="member-name-link">run</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a> with parameters of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Functions.</span><code><a href="../../flows/llmflows/Functions.html#handleFunctionCalls(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,java.util.Map)" class="member-name-link">handleFunctionCalls</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionCallEvent,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static void</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Functions.</span><code><a href="../../flows/llmflows/Functions.html#populateClientFunctionCallId(com.google.adk.events.Event)" class="member-name-link">populateClientFunctionCallId</a><wbr>(<a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;modelResponseEvent)</code></div>
<div class="col-last odd-row-color">
<div class="block">Populates missing function call IDs in the provided event's content.</div>
</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.ResponseProcessor.ResponseProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">postprocess</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;baseEventForLlmResponse,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../../models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">
<div class="block">Post-processes the LLM response after receiving it from the LLM.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.runner">
<h2>Uses of <a href="../Event.html" title="class in com.google.adk.events">Event</a> in <a href="../../runner/package-summary.html">com.google.adk.runner</a></h2>
<div class="caption"><span>Methods in <a href="../../runner/package-summary.html">com.google.adk.runner</a> that return types with arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runAsync(com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">runAsync</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 com.google.genai.types.Content&nbsp;newMessage,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last even-row-color">
<div class="block">Runs the agent in the standard mode using a provided Session object.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runAsync(java.lang.String,java.lang.String,com.google.genai.types.Content)" class="member-name-link">runAsync</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 com.google.genai.types.Content&nbsp;newMessage)</code></div>
<div class="col-last odd-row-color">
<div class="block">Asynchronously runs the agent for a given user and session, processing a new message and using
 a default <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents"><code>RunConfig</code></a>.</div>
</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runAsync(java.lang.String,java.lang.String,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">runAsync</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 com.google.genai.types.Content&nbsp;newMessage,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last even-row-color">
<div class="block">Runs the agent in the standard mode.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runLive(com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">runLive</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../../agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a>&nbsp;liveRequestQueue,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runLive(java.lang.String,java.lang.String,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">runLive</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="../../agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a>&nbsp;liveRequestQueue,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runWithSessionId(java.lang.String,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">runWithSessionId</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 com.google.genai.types.Content&nbsp;newMessage,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.sessions">
<h2>Uses of <a href="../Event.html" title="class in com.google.adk.events">Event</a> in <a href="../../sessions/package-summary.html">com.google.adk.sessions</a></h2>
<div class="caption"><span>Methods in <a href="../../sessions/package-summary.html">com.google.adk.sessions</a> that return <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../Event.html" title="class in com.google.adk.events">Event</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">VertexAiSessionService.</span><code><a href="../../sessions/VertexAiSessionService.html#fromApiEvent(java.util.Map)" class="member-name-link">fromApiEvent</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;apiEvent)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../sessions/package-summary.html">com.google.adk.sessions</a> that return types with arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>default io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseSessionService.</span><code><a href="../../sessions/BaseSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color">
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">InMemorySessionService.</span><code><a href="../../sessions/InMemorySessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">VertexAiSessionService.</span><code><a href="../../sessions/VertexAiSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>abstract com.google.common.collect.ImmutableList<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">ListEventsResponse.</span><code><a href="../../sessions/ListEventsResponse.html#events()" class="member-name-link">events</a>()</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Session.</span><code><a href="../../sessions/Session.html#events()" class="member-name-link">events</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../sessions/package-summary.html">com.google.adk.sessions</a> with parameters of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>default io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseSessionService.</span><code><a href="../../sessions/BaseSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color">
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">InMemorySessionService.</span><code><a href="../../sessions/InMemorySessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">VertexAiSessionService.</span><code><a href="../../sessions/VertexAiSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../../sessions/Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">VertexAiSessionService.</span><code><a href="../../sessions/VertexAiSessionService.html#convertEventToJson(com.google.adk.events.Event)" class="member-name-link">convertEventToJson</a><wbr>(<a href="../Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Method parameters in <a href="../../sessions/package-summary.html">com.google.adk.sessions</a> with type arguments of type <a href="../Event.html" title="class in com.google.adk.events">Event</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>abstract <a href="../../sessions/ListEventsResponse.Builder.html" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ListEventsResponse.Builder.</span><code><a href="../../sessions/ListEventsResponse.Builder.html#events(java.util.List)" class="member-name-link">events</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;&nbsp;events)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../../sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Session.Builder.</span><code><a href="../../sessions/Session.Builder.html#events(java.util.List)" class="member-name-link">events</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../Event.html" title="class in com.google.adk.events">Event</a>&gt;&nbsp;events)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
