<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.events.EventActions (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.events, class: EventActions">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../EventActions.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.events</a></li>
<li><a href="../EventActions.html" class="current-selection">EventActions</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.events.EventActions" class="title">Uses of Class<br>com.google.adk.events.EventActions</h1>
</div>
<div class="caption"><span>Packages that use <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.events">com.google.adk.events</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.tools">com.google.adk.tools</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a> in <a href="../../agents/package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Fields in <a href="../../agents/package-summary.html">com.google.adk.agents</a> declared as <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">CallbackContext.</span><code><a href="../../agents/CallbackContext.html#eventActions" class="member-name-link">eventActions</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> that return <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">CallbackContext.</span><code><a href="../../agents/CallbackContext.html#eventActions()" class="member-name-link">eventActions</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Returns the EventActions associated with this context.</div>
</div>
</div>
<div class="caption"><span>Constructors in <a href="../../agents/package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier</div>
<div class="table-header col-second">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../../agents/CallbackContext.html#%3Cinit%3E(com.google.adk.agents.InvocationContext,com.google.adk.events.EventActions)" class="member-name-link">CallbackContext</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;eventActions)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.events">
<h2>Uses of <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a> in <a href="../package-summary.html">com.google.adk.events</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.events</a> that return <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.</span><code><a href="../Event.html#actions()" class="member-name-link">actions</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">EventActions.Builder.</span><code><a href="../EventActions.Builder.html#build()" class="member-name-link">build</a>()</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.events</a> with parameters of type <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.Builder.</span><code><a href="../Event.Builder.html#actions(com.google.adk.events.EventActions)" class="member-name-link">actions</a><wbr>(<a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;value)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="../EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">EventActions.Builder.</span><code><a href="../EventActions.Builder.html#merge(com.google.adk.events.EventActions)" class="member-name-link">merge</a><wbr>(<a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;other)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>void</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Event.</span><code><a href="../Event.html#setActions(com.google.adk.events.EventActions)" class="member-name-link">setActions</a><wbr>(<a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;actions)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools">
<h2>Uses of <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a> in <a href="../../tools/package-summary.html">com.google.adk.tools</a></h2>
<div class="caption"><span>Methods in <a href="../../tools/package-summary.html">com.google.adk.tools</a> that return <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ToolContext.</span><code><a href="../../tools/ToolContext.html#actions()" class="member-name-link">actions</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../tools/package-summary.html">com.google.adk.tools</a> with parameters of type <a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../../tools/ToolContext.Builder.html" title="class in com.google.adk.tools">ToolContext.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ToolContext.Builder.</span><code><a href="../../tools/ToolContext.Builder.html#actions(com.google.adk.events.EventActions)" class="member-name-link">actions</a><wbr>(<a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;actions)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>void</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">ToolContext.</span><code><a href="../../tools/ToolContext.html#setActions(com.google.adk.events.EventActions)" class="member-name-link">setActions</a><wbr>(<a href="../EventActions.html" title="class in com.google.adk.events">EventActions</a>&nbsp;actions)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
