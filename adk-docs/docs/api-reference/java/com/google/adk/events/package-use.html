<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Package com.google.adk.events (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.events">
<meta name="generator" content="javadoc/PackageUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-use-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html" class="current-selection">com.google.adk.events</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Package com.google.adk.events" class="title">Uses of Package<br>com.google.adk.events</h1>
</div>
<div class="caption"><span>Packages that use <a href="package-summary.html">com.google.adk.events</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk">com.google.adk</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.events">com.google.adk.events</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.flows">com.google.adk.flows</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.flows.llmflows">com.google.adk.flows.llmflows</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.runner">com.google.adk.runner</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.sessions">com.google.adk.sessions</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.tools">com.google.adk.tools</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="package-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="../package-summary.html">com.google.adk</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/Event.html#com.google.adk">Event</a></div>
<div class="col-last even-row-color">
<div class="block">Represents an event in a session.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.agents">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="../agents/package-summary.html">com.google.adk.agents</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/Event.html#com.google.adk.agents">Event</a></div>
<div class="col-last even-row-color">
<div class="block">Represents an event in a session.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/EventActions.html#com.google.adk.agents">EventActions</a></div>
<div class="col-last odd-row-color">
<div class="block">Represents the actions attached to an event.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.events">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="package-summary.html">com.google.adk.events</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/Event.html#com.google.adk.events">Event</a></div>
<div class="col-last even-row-color">
<div class="block">Represents an event in a session.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/Event.Builder.html#com.google.adk.events">Event.Builder</a></div>
<div class="col-last odd-row-color">
<div class="block">Builder for <a href="Event.html" title="class in com.google.adk.events"><code>Event</code></a>.</div>
</div>
<div class="col-first even-row-color"><a href="class-use/EventActions.html#com.google.adk.events">EventActions</a></div>
<div class="col-last even-row-color">
<div class="block">Represents the actions attached to an event.</div>
</div>
<div class="col-first odd-row-color"><a href="class-use/EventActions.Builder.html#com.google.adk.events">EventActions.Builder</a></div>
<div class="col-last odd-row-color">
<div class="block">Builder for <a href="EventActions.html" title="class in com.google.adk.events"><code>EventActions</code></a>.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="../flows/package-summary.html">com.google.adk.flows</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/Event.html#com.google.adk.flows">Event</a></div>
<div class="col-last even-row-color">
<div class="block">Represents an event in a session.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows.llmflows">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/Event.html#com.google.adk.flows.llmflows">Event</a></div>
<div class="col-last even-row-color">
<div class="block">Represents an event in a session.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.runner">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="../runner/package-summary.html">com.google.adk.runner</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/Event.html#com.google.adk.runner">Event</a></div>
<div class="col-last even-row-color">
<div class="block">Represents an event in a session.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.sessions">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="../sessions/package-summary.html">com.google.adk.sessions</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/Event.html#com.google.adk.sessions">Event</a></div>
<div class="col-last even-row-color">
<div class="block">Represents an event in a session.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools">
<div class="caption"><span>Classes in <a href="package-summary.html">com.google.adk.events</a> used by <a href="../tools/package-summary.html">com.google.adk.tools</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="class-use/EventActions.html#com.google.adk.tools">EventActions</a></div>
<div class="col-last even-row-color">
<div class="block">Represents the actions attached to an event.</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
