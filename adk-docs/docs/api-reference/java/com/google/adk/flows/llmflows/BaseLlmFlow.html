<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>BaseLlmFlow (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.flows.llmflows, class: BaseLlmFlow">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/BaseLlmFlow.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.flows.llmflows</a></li>
<li><a href="BaseLlmFlow.html" class="current-selection">BaseLlmFlow</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#field-summary" tabindex="0">Field Summary</a></li>
<li><a href="#constructor-summary" tabindex="0">Constructor Summary</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#field-detail" tabindex="0">Field Details</a>
<ol class="toc-list">
<li><a href="#requestProcessors" tabindex="0">requestProcessors</a></li>
<li><a href="#responseProcessors" tabindex="0">responseProcessors</a></li>
</ol>
</li>
<li><a href="#constructor-detail" tabindex="0">Constructor Details</a>
<ol class="toc-list">
<li><a href="#%3Cinit%3E(java.util.List,java.util.List)" tabindex="0">BaseLlmFlow(List, List)</a></li>
</ol>
</li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#preprocess(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" tabindex="0">preprocess(InvocationContext, LlmRequest)</a></li>
<li><a href="#postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" tabindex="0">postprocess(InvocationContext, Event, LlmRequest, LlmResponse)</a></li>
<li><a href="#run(com.google.adk.agents.InvocationContext)" tabindex="0">run(InvocationContext)</a></li>
<li><a href="#runLive(com.google.adk.agents.InvocationContext)" tabindex="0">runLive(InvocationContext)</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class BaseLlmFlow" class="title">Class BaseLlmFlow</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">com.google.adk.flows.llmflows.BaseLlmFlow</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="../BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a></code></dd>
</dl>
<dl class="notes">
<dt>Direct Known Subclasses:</dt>
<dd><code><a href="SingleFlow.html" title="class in com.google.adk.flows.llmflows">SingleFlow</a></code></dd>
</dl>
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public abstract class </span><span class="element-name type-name-label">BaseLlmFlow</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="../BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a></span></div>
<div class="block">A basic flow that calls the LLM in a loop until a final response is generated.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- =========== FIELD SUMMARY =========== -->
<li>
<section class="field-summary" id="field-summary">
<h2>Field Summary</h2>
<div class="caption"><span>Fields</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Field</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected final <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor&gt;</code></div>
<div class="col-second even-row-color"><code><a href="#requestProcessors" class="member-name-link">requestProcessors</a></code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>protected final <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;com.google.adk.flows.llmflows.ResponseProcessor&gt;</code></div>
<div class="col-second odd-row-color"><code><a href="#responseProcessors" class="member-name-link">responseProcessors</a></code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.util.List,java.util.List)" class="member-name-link">BaseLlmFlow</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.adk.flows.llmflows.RequestProcessor&gt;&nbsp;requestProcessors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.adk.flows.llmflows.ResponseProcessor&gt;&nbsp;responseProcessors)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.ResponseProcessor.ResponseProcessingResult&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">postprocess</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;baseEventForLlmResponse,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../../models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Post-processes the LLM response after receiving it from the LLM.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>protected com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#preprocess(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">preprocess</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Pre-processes the LLM request before sending it to the LLM.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#run(com.google.adk.agents.InvocationContext)" class="member-name-link">run</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Run this flow.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">&nbsp;</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ FIELD DETAIL =========== -->
<li>
<section class="field-details" id="field-detail">
<h2>Field Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="requestProcessors">
<h3>requestProcessors</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.adk.flows.llmflows.RequestProcessor&gt;</span>&nbsp;<span class="element-name">requestProcessors</span></div>
</div>
</section>
</li>
<li>
<section class="detail" id="responseProcessors">
<h3>responseProcessors</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected final</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.adk.flows.llmflows.ResponseProcessor&gt;</span>&nbsp;<span class="element-name">responseProcessors</span></div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.util.List,java.util.List)">
<h3>BaseLlmFlow</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">BaseLlmFlow</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.adk.flows.llmflows.RequestProcessor&gt;&nbsp;requestProcessors,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.adk.flows.llmflows.ResponseProcessor&gt;&nbsp;responseProcessors)</span></div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="preprocess(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)">
<h3>preprocess</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult</span>&nbsp;<span class="element-name">preprocess</span><wbr><span class="parameters">(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</span></div>
<div class="block">Pre-processes the LLM request before sending it to the LLM. Executes all registered <code>RequestProcessor</code>.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)">
<h3>postprocess</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">protected</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Single&lt;com.google.adk.flows.llmflows.ResponseProcessor.ResponseProcessingResult&gt;</span>&nbsp;<span class="element-name">postprocess</span><wbr><span class="parameters">(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;baseEventForLlmResponse,
 <a href="../../models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../../models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</span></div>
<div class="block">Post-processes the LLM response after receiving it from the LLM. Executes all registered <code>ResponseProcessor</code> instances. Handles function calls if present in the response.</div>
</div>
</section>
</li>
<li>
<section class="detail" id="run(com.google.adk.agents.InvocationContext)">
<h3>run</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Flowable&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">run</span><wbr><span class="parameters">(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="../BaseFlow.html#run(com.google.adk.agents.InvocationContext)">BaseFlow</a></code></span></div>
<div class="block">Run this flow.

 <p>To implement this method, the flow should follow the below requirements:

 <ol>
   <li>1. `session` should be treated as immutable, DO NOT change it.
   <li>2. The caller who trigger the flow is responsible for updating the session as the events
       being generated. The subclass implementation will assume session is updated after each
       yield event statement.
   <li>3. A flow may spawn sub-agent flows depending on the agent definition.
 </ol></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../BaseFlow.html#run(com.google.adk.agents.InvocationContext)">run</a></code>&nbsp;in interface&nbsp;<code><a href="../BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a></code></dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="runLive(com.google.adk.agents.InvocationContext)">
<h3>runLive</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Flowable&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">runLive</span><wbr><span class="parameters">(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext)</span></div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="../BaseFlow.html#runLive(com.google.adk.agents.InvocationContext)">runLive</a></code>&nbsp;in interface&nbsp;<code><a href="../BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
