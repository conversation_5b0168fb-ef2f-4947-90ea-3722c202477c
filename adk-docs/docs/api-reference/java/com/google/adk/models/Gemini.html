<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title><PERSON> (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.models, class: Gemini">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/Gemini.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.models</a></li>
<li><a href="Gemini.html" class="current-selection">Gemini</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#nested-class-summary" tabindex="0">Nested Class Summary</a></li>
<li><a href="#constructor-summary" tabindex="0">Constructor Summary</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#constructor-detail" tabindex="0">Constructor Details</a>
<ol class="toc-list">
<li><a href="#%3Cinit%3E(java.lang.String,com.google.genai.Client)" tabindex="0">Gemini(String, Client)</a></li>
<li><a href="#%3Cinit%3E(java.lang.String,java.lang.String)" tabindex="0">Gemini(String, String)</a></li>
<li><a href="#%3Cinit%3E(java.lang.String,com.google.adk.models.VertexCredentials)" tabindex="0">Gemini(String, VertexCredentials)</a></li>
</ol>
</li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#builder()" tabindex="0">builder()</a></li>
<li><a href="#generateContent(com.google.adk.models.LlmRequest,boolean)" tabindex="0">generateContent(LlmRequest, boolean)</a></li>
<li><a href="#connect(com.google.adk.models.LlmRequest)" tabindex="0">connect(LlmRequest)</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class Gemini" class="title">Class Gemini</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance"><a href="BaseLlm.html" title="class in com.google.adk.models">com.google.adk.models.BaseLlm</a>
<div class="inheritance">com.google.adk.models.Gemini</div>
</div>
</div>
<section class="class-description" id="class-description">
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">Gemini</span>
<span class="extends-implements">extends <a href="BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></span></div>
<div class="block">Represents the Gemini Generative AI model.

 <p>This class provides methods for interacting with the Gemini model, including standard
 request-response generation and establishing persistent bidirectional connections.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="Gemini.Builder.html" class="type-name-link" title="class in com.google.adk.models">Gemini.Builder</a></code></div>
<div class="col-last even-row-color">
<div class="block">Builder for <a href="Gemini.html" title="class in com.google.adk.models"><code>Gemini</code></a>.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,com.google.adk.models.VertexCredentials)" class="member-name-link">Gemini</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;modelName,
 <a href="VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a>&nbsp;vertexCredentials)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructs a new Gemini instance with a Google Gemini API key.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(java.lang.String,com.google.genai.Client)" class="member-name-link">Gemini</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;modelName,
 com.google.genai.Client&nbsp;apiClient)</code></div>
<div class="col-last odd-row-color">
<div class="block">Constructs a new Gemini instance.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">Gemini</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;modelName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;apiKey)</code></div>
<div class="col-last even-row-color">
<div class="block">Constructs a new Gemini instance with a Google Gemini API key.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Returns a new Builder instance for constructing Gemini objects.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#connect(com.google.adk.models.LlmRequest)" class="member-name-link">connect</a><wbr>(<a href="LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a live connection to the LLM.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent</a><wbr>(<a href="LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Generates one content from the given LLM request and tools.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.google.adk.models.BaseLlm">Methods inherited from class&nbsp;com.google.adk.models.<a href="BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></h3>
<code><a href="BaseLlm.html#model()">model</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,com.google.genai.Client)">
<h3>Gemini</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Gemini</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;modelName,
 com.google.genai.Client&nbsp;apiClient)</span></div>
<div class="block">Constructs a new Gemini instance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>modelName</code> - The name of the Gemini model to use (e.g., "gemini-2.0-flash").</dd>
<dd><code>apiClient</code> - The genai <code>Client</code> instance for making API calls.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,java.lang.String)">
<h3>Gemini</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Gemini</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;modelName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;apiKey)</span></div>
<div class="block">Constructs a new Gemini instance with a Google Gemini API key.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>modelName</code> - The name of the Gemini model to use (e.g., "gemini-2.0-flash").</dd>
<dd><code>apiKey</code> - The Google Gemini API key.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(java.lang.String,com.google.adk.models.VertexCredentials)">
<h3>Gemini</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">Gemini</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;modelName,
 <a href="VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a>&nbsp;vertexCredentials)</span></div>
<div class="block">Constructs a new Gemini instance with a Google Gemini API key.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>modelName</code> - The name of the Gemini model to use (e.g., "gemini-2.0-flash").</dd>
<dd><code>vertexCredentials</code> - The Vertex AI credentials to access the Gemini model.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="builder()">
<h3>builder</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></span>&nbsp;<span class="element-name">builder</span>()</div>
<div class="block">Returns a new Builder instance for constructing Gemini objects. Note that when building a
 Gemini object, at least one of apiKey, vertexCredentials, or an explicit apiClient must be set.
 If multiple are set, the explicit apiClient will take precedence.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>A new <a href="Gemini.Builder.html" title="class in com.google.adk.models"><code>Gemini.Builder</code></a>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="generateContent(com.google.adk.models.LlmRequest,boolean)">
<h3>generateContent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Flowable&lt;<a href="LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</span>&nbsp;<span class="element-name">generateContent</span><wbr><span class="parameters">(<a href="LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="BaseLlm.html#generateContent(com.google.adk.models.LlmRequest,boolean)">BaseLlm</a></code></span></div>
<div class="block">Generates one content from the given LLM request and tools.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseLlm.html#generateContent(com.google.adk.models.LlmRequest,boolean)">generateContent</a></code>&nbsp;in class&nbsp;<code><a href="BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></code></dd>
<dt>Parameters:</dt>
<dd><code>llmRequest</code> - The LLM request containing the input prompt and parameters.</dd>
<dd><code>stream</code> - A boolean flag indicating whether to stream the response.</dd>
<dt>Returns:</dt>
<dd>A Flowable of LlmResponses. For non-streaming calls, it will only yield one
     LlmResponse. For streaming calls, it may yield more than one LlmResponse, but all yielded
     LlmResponses should be treated as one content by merging their parts.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="connect(com.google.adk.models.LlmRequest)">
<h3>connect</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></span>&nbsp;<span class="element-name">connect</span><wbr><span class="parameters">(<a href="LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from class:&nbsp;<code><a href="BaseLlm.html#connect(com.google.adk.models.LlmRequest)">BaseLlm</a></code></span></div>
<div class="block">Creates a live connection to the LLM.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseLlm.html#connect(com.google.adk.models.LlmRequest)">connect</a></code>&nbsp;in class&nbsp;<code><a href="BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
