<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.models.LlmRequest.Builder (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.models, class: LlmRequest, class: Builder">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../LlmRequest.Builder.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.models</a></li>
<li><a href="../LlmRequest.html">LlmRequest</a></li>
<li><a href="../LlmRequest.Builder.html" class="current-selection">Builder</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.models.LlmRequest.Builder" class="title">Uses of Class<br>com.google.adk.models.LlmRequest.Builder</h1>
</div>
<div class="caption"><span>Packages that use <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.models">com.google.adk.models</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.tools">com.google.adk.tools</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.models">
<h2>Uses of <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a> in <a href="../package-summary.html">com.google.adk.models</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.models</a> that return <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>final <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#appendInstructions(java.util.List)" class="member-name-link">appendInstructions</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;&nbsp;instructions)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>final <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#appendTools(java.util.List)" class="member-name-link">appendTools</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../../tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.</span><code><a href="../LlmRequest.html#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>abstract <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#config(com.google.genai.types.GenerateContentConfig)" class="member-name-link">config</a><wbr>(com.google.genai.types.GenerateContentConfig&nbsp;config)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>abstract <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#contents(java.util.List)" class="member-name-link">contents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.genai.types.Content&gt;&nbsp;contents)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>abstract <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#liveConnectConfig(com.google.genai.types.LiveConnectConfig)" class="member-name-link">liveConnectConfig</a><wbr>(com.google.genai.types.LiveConnectConfig&nbsp;liveConnectConfig)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>abstract <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#model(java.lang.String)" class="member-name-link">model</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;model)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>final <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#outputSchema(com.google.genai.types.Schema)" class="member-name-link">outputSchema</a><wbr>(com.google.genai.types.Schema&nbsp;schema)</code></div>
<div class="col-last odd-row-color">
<div class="block">Sets the output schema for the LLM response.</div>
</div>
<div class="col-first even-row-color"><code>abstract <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.</span><code><a href="../LlmRequest.html#toBuilder()" class="member-name-link">toBuilder</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools">
<h2>Uses of <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a> in <a href="../../tools/package-summary.html">com.google.adk.tools</a></h2>
<div class="caption"><span>Methods in <a href="../../tools/package-summary.html">com.google.adk.tools</a> with parameters of type <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LoadArtifactsTool.</span><code><a href="../../tools/LoadArtifactsTool.html#appendArtifactsToLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">appendArtifactsToLlmRequest</a><wbr>(<a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a>&nbsp;llmRequestBuilder,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseTool.</span><code><a href="../../tools/BaseTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest</a><wbr>(<a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a>&nbsp;llmRequestBuilder,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last odd-row-color">
<div class="block">Processes the outgoing <a href="../LlmRequest.Builder.html" title="class in com.google.adk.models"><code>LlmRequest.Builder</code></a>.</div>
</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BuiltInCodeExecutionTool.</span><code><a href="../../tools/BuiltInCodeExecutionTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest</a><wbr>(<a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a>&nbsp;llmRequestBuilder,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">GoogleSearchTool.</span><code><a href="../../tools/GoogleSearchTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest</a><wbr>(<a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a>&nbsp;llmRequestBuilder,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LoadArtifactsTool.</span><code><a href="../../tools/LoadArtifactsTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest</a><wbr>(<a href="../LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a>&nbsp;llmRequestBuilder,
 <a href="../../tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
