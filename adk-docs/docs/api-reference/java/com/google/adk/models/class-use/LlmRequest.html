<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.models.LlmRequest (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.models, class: LlmRequest">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../LlmRequest.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.models</a></li>
<li><a href="../LlmRequest.html" class="current-selection">LlmRequest</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.models.LlmRequest" class="title">Uses of Class<br>com.google.adk.models.LlmRequest</h1>
</div>
<div class="caption"><span>Packages that use <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk">com.google.adk</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.flows.llmflows">com.google.adk.flows.llmflows</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.models">com.google.adk.models</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk">
<h2>Uses of <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a> in <a href="../../package-summary.html">com.google.adk</a></h2>
<div class="caption"><span>Methods in <a href="../../package-summary.html">com.google.adk</a> with parameters of type <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static void</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Telemetry.</span><code><a href="../../Telemetry.html#traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">traceCallLlm</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">
<div class="block">Traces a call to the LLM.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a> in <a href="../../agents/package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.BeforeModelCallback.</span><code><a href="../../agents/Callbacks.BeforeModelCallback.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.BeforeModelCallbackSync.</span><code><a href="../../agents/Callbacks.BeforeModelCallbackSync.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows.llmflows">
<h2>Uses of <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a> in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></h2>
<div class="caption"><span>Methods in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a> with parameters of type <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.ResponseProcessor.ResponseProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">postprocess</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;baseEventForLlmResponse,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">
<div class="block">Post-processes the LLM response after receiving it from the LLM.</div>
</div>
<div class="col-first odd-row-color"><code>protected com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#preprocess(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">preprocess</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last odd-row-color">
<div class="block">Pre-processes the LLM request before sending it to the LLM.</div>
</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">AgentTransfer.</span><code><a href="../../flows/llmflows/AgentTransfer.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Basic.</span><code><a href="../../flows/llmflows/Basic.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Contents.</span><code><a href="../../flows/llmflows/Contents.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Examples.</span><code><a href="../../flows/llmflows/Examples.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Identity.</span><code><a href="../../flows/llmflows/Identity.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.RequestProcessor.RequestProcessingResult&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Instructions.</span><code><a href="../../flows/llmflows/Instructions.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;request)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.models">
<h2>Uses of <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a> in <a href="../package-summary.html">com.google.adk.models</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.models</a> that return <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>abstract <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../LlmRequest.Builder.html#build()" class="member-name-link">build</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.models</a> with parameters of type <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>abstract <a href="../BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlm.</span><code><a href="../BaseLlm.html#connect(com.google.adk.models.LlmRequest)" class="member-name-link">connect</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a live connection to the LLM.</div>
</div>
<div class="col-first odd-row-color"><code><a href="../BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Claude.</span><code><a href="../Claude.html#connect(com.google.adk.models.LlmRequest)" class="member-name-link">connect</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Gemini.</span><code><a href="../Gemini.html#connect(com.google.adk.models.LlmRequest)" class="member-name-link">connect</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>abstract io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseLlm.</span><code><a href="../BaseLlm.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</code></div>
<div class="col-last odd-row-color">
<div class="block">Generates one content from the given LLM request and tools.</div>
</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Claude.</span><code><a href="../Claude.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Gemini.</span><code><a href="../Gemini.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
