<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.models.LlmResponse (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.models, class: LlmResponse">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../LlmResponse.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.models</a></li>
<li><a href="../LlmResponse.html" class="current-selection">LlmResponse</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.models.LlmResponse" class="title">Uses of Class<br>com.google.adk.models.LlmResponse</h1>
</div>
<div class="caption"><span>Packages that use <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk">com.google.adk</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.flows.llmflows">com.google.adk.flows.llmflows</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.models">com.google.adk.models</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk">
<h2>Uses of <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a> in <a href="../../package-summary.html">com.google.adk</a></h2>
<div class="caption"><span>Methods in <a href="../../package-summary.html">com.google.adk</a> with parameters of type <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static void</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Telemetry.</span><code><a href="../../Telemetry.html#traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">traceCallLlm</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;eventId,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">
<div class="block">Traces a call to the LLM.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a> in <a href="../../agents/package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> that return types with arguments of type <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.AfterModelCallback.</span><code><a href="../../agents/Callbacks.AfterModelCallback.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.AfterModelCallbackSync.</span><code><a href="../../agents/Callbacks.AfterModelCallbackSync.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.BeforeModelCallback.</span><code><a href="../../agents/Callbacks.BeforeModelCallback.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.BeforeModelCallbackSync.</span><code><a href="../../agents/Callbacks.BeforeModelCallbackSync.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.AfterModelCallback.</span><code><a href="../../agents/Callbacks.AfterModelCallback.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.AfterModelCallbackSync.</span><code><a href="../../agents/Callbacks.AfterModelCallbackSync.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)" class="member-name-link">call</a><wbr>(<a href="../../agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a>&nbsp;callbackContext,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows.llmflows">
<h2>Uses of <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a> in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></h2>
<div class="caption"><span>Methods in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a> with parameters of type <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>protected io.reactivex.rxjava3.core.Single<wbr>&lt;com.google.adk.flows.llmflows.ResponseProcessor.ResponseProcessingResult&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlmFlow.</span><code><a href="../../flows/llmflows/BaseLlmFlow.html#postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">postprocess</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;context,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;baseEventForLlmResponse,
 <a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&nbsp;llmResponse)</code></div>
<div class="col-last even-row-color">
<div class="block">Post-processes the LLM response after receiving it from the LLM.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.models">
<h2>Uses of <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a> in <a href="../package-summary.html">com.google.adk.models</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.models</a> that return <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmResponse.Builder.</span><code><a href="../LlmResponse.Builder.html#build()" class="member-name-link">build</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">LlmResponse.</span><code><a href="../LlmResponse.html#create(com.google.genai.types.GenerateContentResponse)" class="member-name-link">create</a><wbr>(com.google.genai.types.GenerateContentResponse&nbsp;response)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>static <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmResponse.</span><code><a href="../LlmResponse.html#create(java.util.List)" class="member-name-link">create</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.genai.types.Candidate&gt;&nbsp;candidates)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.models</a> that return types with arguments of type <a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>abstract io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseLlm.</span><code><a href="../BaseLlm.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</code></div>
<div class="col-last even-row-color">
<div class="block">Generates one content from the given LLM request and tools.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Claude.</span><code><a href="../Claude.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Gemini.</span><code><a href="../Gemini.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent</a><wbr>(<a href="../LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a>&nbsp;llmRequest,
 boolean&nbsp;stream)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseLlmConnection.</span><code><a href="../BaseLlmConnection.html#receive()" class="member-name-link">receive</a>()</code></div>
<div class="col-last odd-row-color">
<div class="block">Receives the model responses.</div>
</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">GeminiLlmConnection.</span><code><a href="../GeminiLlmConnection.html#receive()" class="member-name-link">receive</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
