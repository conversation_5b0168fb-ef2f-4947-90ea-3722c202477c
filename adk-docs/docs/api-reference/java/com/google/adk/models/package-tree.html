<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>com.google.adk.models Class Hierarchy (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="tree: package: com.google.adk.models">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li><a href="package-use.html">Use</a></li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html" class="current-selection">com.google.adk.models</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package com.google.adk.models</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">com.google.adk.models.<a href="BaseLlm.html" class="type-name-link" title="class in com.google.adk.models">BaseLlm</a>
<ul>
<li class="circle">com.google.adk.models.<a href="Claude.html" class="type-name-link" title="class in com.google.adk.models">Claude</a></li>
<li class="circle">com.google.adk.models.<a href="Gemini.html" class="type-name-link" title="class in com.google.adk.models">Gemini</a></li>
</ul>
</li>
<li class="circle">com.google.adk.models.<a href="Gemini.Builder.html" class="type-name-link" title="class in com.google.adk.models">Gemini.Builder</a></li>
<li class="circle">com.google.adk.models.<a href="GeminiLlmConnection.html" class="type-name-link" title="class in com.google.adk.models">GeminiLlmConnection</a> (implements com.google.adk.models.<a href="BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a>)</li>
<li class="circle">com.google.adk.<a href="../JsonBaseModel.html" class="type-name-link" title="class in com.google.adk">JsonBaseModel</a>
<ul>
<li class="circle">com.google.adk.models.<a href="LlmRequest.html" class="type-name-link" title="class in com.google.adk.models">LlmRequest</a></li>
<li class="circle">com.google.adk.models.<a href="LlmResponse.html" class="type-name-link" title="class in com.google.adk.models">LlmResponse</a></li>
</ul>
</li>
<li class="circle">com.google.adk.models.<a href="LlmRegistry.html" class="type-name-link" title="class in com.google.adk.models">LlmRegistry</a></li>
<li class="circle">com.google.adk.models.<a href="LlmRequest.Builder.html" class="type-name-link" title="class in com.google.adk.models">LlmRequest.Builder</a></li>
<li class="circle">com.google.adk.models.<a href="LlmResponse.Builder.html" class="type-name-link" title="class in com.google.adk.models">LlmResponse.Builder</a></li>
<li class="circle">com.google.adk.models.<a href="Model.html" class="type-name-link" title="class in com.google.adk.models">Model</a></li>
<li class="circle">com.google.adk.models.<a href="Model.Builder.html" class="type-name-link" title="class in com.google.adk.models">Model.Builder</a></li>
<li class="circle">com.google.adk.models.<a href="VertexCredentials.html" class="type-name-link" title="class in com.google.adk.models">VertexCredentials</a></li>
<li class="circle">com.google.adk.models.<a href="VertexCredentials.Builder.html" class="type-name-link" title="class in com.google.adk.models">VertexCredentials.Builder</a></li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">com.google.adk.models.<a href="BaseLlmConnection.html" class="type-name-link" title="interface in com.google.adk.models">BaseLlmConnection</a></li>
<li class="circle">com.google.adk.models.<a href="LlmRegistry.LlmFactory.html" class="type-name-link" title="interface in com.google.adk.models">LlmRegistry.LlmFactory</a></li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
