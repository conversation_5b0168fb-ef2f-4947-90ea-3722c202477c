<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>BaseSessionService (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.sessions, interface: BaseSessionService">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/BaseSessionService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.sessions</a></li>
<li><a href="BaseSessionService.html" class="current-selection">BaseSessionService</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" tabindex="0">createSession(String, String, ConcurrentMap, String)</a></li>
<li><a href="#createSession(java.lang.String,java.lang.String)" tabindex="0">createSession(String, String)</a></li>
<li><a href="#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" tabindex="0">getSession(String, String, String, Optional)</a></li>
<li><a href="#listSessions(java.lang.String,java.lang.String)" tabindex="0">listSessions(String, String)</a></li>
<li><a href="#deleteSession(java.lang.String,java.lang.String,java.lang.String)" tabindex="0">deleteSession(String, String, String)</a></li>
<li><a href="#listEvents(java.lang.String,java.lang.String,java.lang.String)" tabindex="0">listEvents(String, String, String)</a></li>
<li><a href="#closeSession(com.google.adk.sessions.Session)" tabindex="0">closeSession(Session)</a></li>
<li><a href="#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" tabindex="0">appendEvent(Session, Event)</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Interface BaseSessionService" class="title">Interface BaseSessionService</h1>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Known Implementing Classes:</dt>
<dd><code><a href="InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></code>, <code><a href="VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></code></dd>
</dl>
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public interface </span><span class="element-name type-name-label">BaseSessionService</span></div>
<div class="block">Defines the contract for managing <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a>s and their associated <a href="../events/Event.html" title="class in com.google.adk.events"><code>Event</code></a>s. Provides
 methods for creating, retrieving, listing, and deleting sessions, as well as listing and
 appending events to a session. Implementations of this interface handle the underlying storage
 and retrieval logic.</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab3" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab3', 3)" class="table-tab">Abstract Methods</button><button id="method-summary-table-tab5" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab5', 3)" class="table-tab">Default Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#closeSession(com.google.adk.sessions.Session)" class="member-name-link">closeSession</a><wbr>(<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">Closes a session.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code>default io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5"><code><a href="#createSession(java.lang.String,java.lang.String)" class="member-name-link">createSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab5">
<div class="block">Creates a new session with the specified application name and user ID, using a default state
 (null) and allowing the service to generate a unique session ID.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;state,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Creates a new session with the specified parameters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#deleteSession(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Deletes a specific session.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a>&gt;&nbsp;config)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Retrieves a specific session, optionally filtering the events included.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#listEvents(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listEvents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Lists the events within a specific session.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3"><code><a href="#listSessions(java.lang.String,java.lang.String)" class="member-name-link">listSessions</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab3">
<div class="block">Lists sessions associated with a specific application and user.</div>
</div>
</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)">
<h3>createSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</span>&nbsp;<span class="element-name">createSession</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 @Nullable
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;state,
 @Nullable
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</span></div>
<div class="block">Creates a new session with the specified parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application associated with the session.</dd>
<dd><code>userId</code> - The identifier for the user associated with the session.</dd>
<dd><code>state</code> - An optional map representing the initial state of the session. Can be null or
     empty.</dd>
<dd><code>sessionId</code> - An optional client-provided identifier for the session. If empty or null, the
     service should generate a unique ID.</dd>
<dt>Returns:</dt>
<dd>The newly created <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> instance.</dd>
<dt>Throws:</dt>
<dd><code><a href="SessionException.html" title="class in com.google.adk.sessions">SessionException</a></code> - if creation fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="createSession(java.lang.String,java.lang.String)">
<h3>createSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</span>&nbsp;<span class="element-name">createSession</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId)</span></div>
<div class="block">Creates a new session with the specified application name and user ID, using a default state
 (null) and allowing the service to generate a unique session ID.

 <p>This is a shortcut for <a href="#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)"><code>createSession(String, String, Map, String)</code></a> with null state
 and a null session ID.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application associated with the session.</dd>
<dd><code>userId</code> - The identifier for the user associated with the session.</dd>
<dt>Returns:</dt>
<dd>The newly created <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> instance.</dd>
<dt>Throws:</dt>
<dd><code><a href="SessionException.html" title="class in com.google.adk.sessions">SessionException</a></code> - if creation fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)">
<h3>getSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">io.reactivex.rxjava3.core.Maybe&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</span>&nbsp;<span class="element-name">getSession</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a>&gt;&nbsp;config)</span></div>
<div class="block">Retrieves a specific session, optionally filtering the events included.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user.</dd>
<dd><code>sessionId</code> - The unique identifier of the session to retrieve.</dd>
<dd><code>config</code> - Optional configuration to filter the events returned within the session (e.g.,
     limit number of recent events, filter by timestamp). If empty, default retrieval behavior
     is used (potentially all events or a service-defined limit).</dd>
<dt>Returns:</dt>
<dd>An <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link"><code>Optional</code></a> containing the <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> if found, otherwise <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html#empty()" title="class or interface in java.util" class="external-link"><code>Optional.empty()</code></a>.</dd>
<dt>Throws:</dt>
<dd><code><a href="SessionException.html" title="class in com.google.adk.sessions">SessionException</a></code> - for retrieval errors other than not found.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="listSessions(java.lang.String,java.lang.String)">
<h3>listSessions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a>&gt;</span>&nbsp;<span class="element-name">listSessions</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId)</span></div>
<div class="block">Lists sessions associated with a specific application and user.

 <p>The <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> objects in the response typically contain only metadata (like ID,
 creation time) and not the full event list or state to optimize performance.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user whose sessions are to be listed.</dd>
<dt>Returns:</dt>
<dd>A <a href="ListSessionsResponse.html" title="class in com.google.adk.sessions"><code>ListSessionsResponse</code></a> containing a list of matching sessions.</dd>
<dt>Throws:</dt>
<dd><code><a href="SessionException.html" title="class in com.google.adk.sessions">SessionException</a></code> - if listing fails.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="deleteSession(java.lang.String,java.lang.String,java.lang.String)">
<h3>deleteSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">io.reactivex.rxjava3.core.Completable</span>&nbsp;<span class="element-name">deleteSession</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</span></div>
<div class="block">Deletes a specific session.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user.</dd>
<dd><code>sessionId</code> - The unique identifier of the session to delete.</dd>
<dt>Throws:</dt>
<dd><code><a href="SessionNotFoundException.html" title="class in com.google.adk.sessions">SessionNotFoundException</a></code> - if the session doesn't exist.</dd>
<dd><code><a href="SessionException.html" title="class in com.google.adk.sessions">SessionException</a></code> - for other deletion errors.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="listEvents(java.lang.String,java.lang.String,java.lang.String)">
<h3>listEvents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a>&gt;</span>&nbsp;<span class="element-name">listEvents</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</span></div>
<div class="block">Lists the events within a specific session. Supports pagination via the response object.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user.</dd>
<dd><code>sessionId</code> - The unique identifier of the session whose events are to be listed.</dd>
<dt>Returns:</dt>
<dd>A <a href="ListEventsResponse.html" title="class in com.google.adk.sessions"><code>ListEventsResponse</code></a> containing a list of events and an optional token for
     retrieving the next page.</dd>
<dt>Throws:</dt>
<dd><code><a href="SessionNotFoundException.html" title="class in com.google.adk.sessions">SessionNotFoundException</a></code> - if the session doesn't exist.</dd>
<dd><code><a href="SessionException.html" title="class in com.google.adk.sessions">SessionException</a></code> - for other listing errors.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="closeSession(com.google.adk.sessions.Session)">
<h3>closeSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">default</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Completable</span>&nbsp;<span class="element-name">closeSession</span><wbr><span class="parameters">(<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session)</span></div>
<div class="block">Closes a session. This is currently a placeholder and may involve finalizing session state or
 performing cleanup actions in future implementations. The default implementation does nothing.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>session</code> - The session object to close.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)">
<h3>appendEvent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">default</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">appendEvent</span><wbr><span class="parameters">(<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</span></div>
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.

 <p>This method primarily modifies the passed <code>session</code> object in memory. Persisting these
 changes typically requires a separate call to an update/save method provided by the specific
 service implementation, or might happen implicitly depending on the implementation's design.

 <p>If the event is marked as partial (e.g., <code>event.isPartial() == true</code>), it is returned
 directly without modifying the session state or event list. State delta keys starting with
 <a href="State.html#TEMP_PREFIX"><code>State.TEMP_PREFIX</code></a> are ignored during state updates.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>session</code> - The <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> object to which the event should be appended (will be
     mutated).</dd>
<dd><code>event</code> - The <a href="../events/Event.html" title="class in com.google.adk.events"><code>Event</code></a> to append.</dd>
<dt>Returns:</dt>
<dd>The appended <a href="../events/Event.html" title="class in com.google.adk.events"><code>Event</code></a> instance (or the original event if it was partial).</dd>
<dt>Throws:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/NullPointerException.html" title="class or interface in java.lang" class="external-link">NullPointerException</a></code> - if session or event is null.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
