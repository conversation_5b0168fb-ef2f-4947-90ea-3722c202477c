<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>InMemorySessionService (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.sessions, class: InMemorySessionService">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/InMemorySessionService.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.sessions</a></li>
<li><a href="InMemorySessionService.html" class="current-selection">InMemorySessionService</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#constructor-summary" tabindex="0">Constructor Summary</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#constructor-detail" tabindex="0">Constructor Details</a>
<ol class="toc-list">
<li><a href="#%3Cinit%3E()" tabindex="0">InMemorySessionService()</a></li>
</ol>
</li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" tabindex="0">createSession(String, String, ConcurrentMap, String)</a></li>
<li><a href="#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" tabindex="0">getSession(String, String, String, Optional)</a></li>
<li><a href="#listSessions(java.lang.String,java.lang.String)" tabindex="0">listSessions(String, String)</a></li>
<li><a href="#deleteSession(java.lang.String,java.lang.String,java.lang.String)" tabindex="0">deleteSession(String, String, String)</a></li>
<li><a href="#listEvents(java.lang.String,java.lang.String,java.lang.String)" tabindex="0">listEvents(String, String, String)</a></li>
<li><a href="#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" tabindex="0">appendEvent(Session, Event)</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class InMemorySessionService" class="title">Class InMemorySessionService</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">com.google.adk.sessions.InMemorySessionService</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></code></dd>
</dl>
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public final class </span><span class="element-name type-name-label">InMemorySessionService</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></span></div>
<div class="block">An in-memory implementation of <a href="BaseSessionService.html" title="interface in com.google.adk.sessions"><code>BaseSessionService</code></a> assuming <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> objects are
 mutable regarding their state map, events list, and last update time.

 <p>This implementation stores sessions, user state, and app state directly in memory using
 concurrent maps for basic thread safety. It is suitable for testing or single-node deployments
 where persistence is not required.

 <p>Note: State merging (app/user state prefixed with <code>_app_</code> / <code>_user_</code>) occurs
 during retrieval operations (<code>getSession</code>, <code>createSession</code>).</div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E()" class="member-name-link">InMemorySessionService</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new instance of the in-memory session service with empty storage.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 @Nullable <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;state,
 @Nullable <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Creates a new session with the specified parameters.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#deleteSession(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Deletes a specific session.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a>&gt;&nbsp;configOpt)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Retrieves a specific session, optionally filtering the events included.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#listEvents(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listEvents</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Lists the events within a specific session.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#listSessions(java.lang.String,java.lang.String)" class="member-name-link">listSessions</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Lists sessions associated with a specific application and user.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-com.google.adk.sessions.BaseSessionService">Methods inherited from interface&nbsp;com.google.adk.sessions.<a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></h3>
<code><a href="BaseSessionService.html#closeSession(com.google.adk.sessions.Session)">closeSession</a>, <a href="BaseSessionService.html#createSession(java.lang.String,java.lang.String)">createSession</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;()">
<h3>InMemorySessionService</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">InMemorySessionService</span>()</div>
<div class="block">Creates a new instance of the in-memory session service with empty storage.</div>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)">
<h3>createSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</span>&nbsp;<span class="element-name">createSession</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 @Nullable <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;state,
 @Nullable <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="BaseSessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)">BaseSessionService</a></code></span></div>
<div class="block">Creates a new session with the specified parameters.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseSessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)">createSession</a></code>&nbsp;in interface&nbsp;<code><a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></code></dd>
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application associated with the session.</dd>
<dd><code>userId</code> - The identifier for the user associated with the session.</dd>
<dd><code>state</code> - An optional map representing the initial state of the session. Can be null or
     empty.</dd>
<dd><code>sessionId</code> - An optional client-provided identifier for the session. If empty or null, the
     service should generate a unique ID.</dd>
<dt>Returns:</dt>
<dd>The newly created <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> instance.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)">
<h3>getSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Maybe&lt;<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</span>&nbsp;<span class="element-name">getSession</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a>&gt;&nbsp;configOpt)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="BaseSessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)">BaseSessionService</a></code></span></div>
<div class="block">Retrieves a specific session, optionally filtering the events included.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseSessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)">getSession</a></code>&nbsp;in interface&nbsp;<code><a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></code></dd>
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user.</dd>
<dd><code>sessionId</code> - The unique identifier of the session to retrieve.</dd>
<dd><code>configOpt</code> - Optional configuration to filter the events returned within the session (e.g.,
     limit number of recent events, filter by timestamp). If empty, default retrieval behavior
     is used (potentially all events or a service-defined limit).</dd>
<dt>Returns:</dt>
<dd>An <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link"><code>Optional</code></a> containing the <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> if found, otherwise <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html#empty()" title="class or interface in java.util" class="external-link"><code>Optional.empty()</code></a>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="listSessions(java.lang.String,java.lang.String)">
<h3>listSessions</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a>&gt;</span>&nbsp;<span class="element-name">listSessions</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="BaseSessionService.html#listSessions(java.lang.String,java.lang.String)">BaseSessionService</a></code></span></div>
<div class="block">Lists sessions associated with a specific application and user.

 <p>The <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> objects in the response typically contain only metadata (like ID,
 creation time) and not the full event list or state to optimize performance.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseSessionService.html#listSessions(java.lang.String,java.lang.String)">listSessions</a></code>&nbsp;in interface&nbsp;<code><a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></code></dd>
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user whose sessions are to be listed.</dd>
<dt>Returns:</dt>
<dd>A <a href="ListSessionsResponse.html" title="class in com.google.adk.sessions"><code>ListSessionsResponse</code></a> containing a list of matching sessions.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="deleteSession(java.lang.String,java.lang.String,java.lang.String)">
<h3>deleteSession</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Completable</span>&nbsp;<span class="element-name">deleteSession</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="BaseSessionService.html#deleteSession(java.lang.String,java.lang.String,java.lang.String)">BaseSessionService</a></code></span></div>
<div class="block">Deletes a specific session.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseSessionService.html#deleteSession(java.lang.String,java.lang.String,java.lang.String)">deleteSession</a></code>&nbsp;in interface&nbsp;<code><a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></code></dd>
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user.</dd>
<dd><code>sessionId</code> - The unique identifier of the session to delete.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="listEvents(java.lang.String,java.lang.String,java.lang.String)">
<h3>listEvents</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a>&gt;</span>&nbsp;<span class="element-name">listEvents</span><wbr><span class="parameters">(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="BaseSessionService.html#listEvents(java.lang.String,java.lang.String,java.lang.String)">BaseSessionService</a></code></span></div>
<div class="block">Lists the events within a specific session. Supports pagination via the response object.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseSessionService.html#listEvents(java.lang.String,java.lang.String,java.lang.String)">listEvents</a></code>&nbsp;in interface&nbsp;<code><a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></code></dd>
<dt>Parameters:</dt>
<dd><code>appName</code> - The name of the application.</dd>
<dd><code>userId</code> - The identifier of the user.</dd>
<dd><code>sessionId</code> - The unique identifier of the session whose events are to be listed.</dd>
<dt>Returns:</dt>
<dd>A <a href="ListEventsResponse.html" title="class in com.google.adk.sessions"><code>ListEventsResponse</code></a> containing a list of events and an optional token for
     retrieving the next page.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)">
<h3>appendEvent</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="annotations">@CanIgnoreReturnValue
</span><span class="modifiers">public</span>&nbsp;<span class="return-type">io.reactivex.rxjava3.core.Single&lt;<a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</span>&nbsp;<span class="element-name">appendEvent</span><wbr><span class="parameters">(<a href="Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</span></div>
<div class="block"><span class="description-from-type-label">Description copied from interface:&nbsp;<code><a href="BaseSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)">BaseSessionService</a></code></span></div>
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.

 <p>This method primarily modifies the passed <code>session</code> object in memory. Persisting these
 changes typically requires a separate call to an update/save method provided by the specific
 service implementation, or might happen implicitly depending on the implementation's design.

 <p>If the event is marked as partial (e.g., <code>event.isPartial() == true</code>), it is returned
 directly without modifying the session state or event list. State delta keys starting with
 <a href="State.html#TEMP_PREFIX"><code>State.TEMP_PREFIX</code></a> are ignored during state updates.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="BaseSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)">appendEvent</a></code>&nbsp;in interface&nbsp;<code><a href="BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></code></dd>
<dt>Parameters:</dt>
<dd><code>session</code> - The <a href="Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> object to which the event should be appended (will be
     mutated).</dd>
<dd><code>event</code> - The <a href="../events/Event.html" title="class in com.google.adk.events"><code>Event</code></a> to append.</dd>
<dt>Returns:</dt>
<dd>The appended <a href="../events/Event.html" title="class in com.google.adk.events"><code>Event</code></a> instance (or the original event if it was partial).</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
