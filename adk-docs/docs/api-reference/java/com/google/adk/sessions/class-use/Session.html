<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.sessions.Session (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.sessions, class: Session">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../Session.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.sessions</a></li>
<li><a href="../Session.html" class="current-selection">Session</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.sessions.Session" class="title">Uses of Class<br>com.google.adk.sessions.Session</h1>
</div>
<div class="caption"><span>Packages that use <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.runner">com.google.adk.runner</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.sessions">com.google.adk.sessions</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../Session.html" title="class in com.google.adk.sessions">Session</a> in <a href="../../agents/package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> that return <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../Session.html" title="class in com.google.adk.sessions">Session</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../../agents/InvocationContext.html#session()" class="member-name-link">session</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static <a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../../agents/InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">create</a><wbr>(<a href="../BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>&nbsp;sessionService,
 <a href="../../artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>&nbsp;artifactService,
 <a href="../../agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../../agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a>&nbsp;liveRequestQueue,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">InvocationContext.</span><code><a href="../../agents/InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,java.lang.String,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">create</a><wbr>(<a href="../BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>&nbsp;sessionService,
 <a href="../../artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>&nbsp;artifactService,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;invocationId,
 <a href="../../agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a>&nbsp;agent,
 <a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 com.google.genai.types.Content&nbsp;userContent,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.runner">
<h2>Uses of <a href="../Session.html" title="class in com.google.adk.sessions">Session</a> in <a href="../../runner/package-summary.html">com.google.adk.runner</a></h2>
<div class="caption"><span>Methods in <a href="../../runner/package-summary.html">com.google.adk.runner</a> with parameters of type <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runAsync(com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">runAsync</a><wbr>(<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 com.google.genai.types.Content&nbsp;newMessage,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last even-row-color">
<div class="block">Runs the agent in the standard mode using a provided Session object.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Flowable<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Runner.</span><code><a href="../../runner/Runner.html#runLive(com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">runLive</a><wbr>(<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../../agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a>&nbsp;liveRequestQueue,
 <a href="../../agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a>&nbsp;runConfig)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.sessions">
<h2>Uses of <a href="../Session.html" title="class in com.google.adk.sessions">Session</a> in <a href="../package-summary.html">com.google.adk.sessions</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.sessions</a> that return <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../Session.html" title="class in com.google.adk.sessions">Session</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">Session.Builder.</span><code><a href="../Session.Builder.html#build()" class="member-name-link">build</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Session.</span><code><a href="../Session.html#fromJson(java.lang.String)" class="member-name-link">fromJson</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;json)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.sessions</a> that return types with arguments of type <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>default io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseSessionService.</span><code><a href="../BaseSessionService.html#createSession(java.lang.String,java.lang.String)" class="member-name-link">createSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId)</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new session with the specified application name and user ID, using a default state
 (null) and allowing the service to generate a unique session ID.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseSessionService.</span><code><a href="../BaseSessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;state,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last odd-row-color">
<div class="block">Creates a new session with the specified parameters.</div>
</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">InMemorySessionService.</span><code><a href="../InMemorySessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 @Nullable <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;state,
 @Nullable <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">VertexAiSessionService.</span><code><a href="../VertexAiSessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;state,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseSessionService.</span><code><a href="../BaseSessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="../GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a>&gt;&nbsp;config)</code></div>
<div class="col-last even-row-color">
<div class="block">Retrieves a specific session, optionally filtering the events included.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">InMemorySessionService.</span><code><a href="../InMemorySessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="../GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a>&gt;&nbsp;configOpt)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">VertexAiSessionService.</span><code><a href="../VertexAiSessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;appName,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;userId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;sessionId,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a>&lt;<a href="../GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a>&gt;&nbsp;config)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>abstract com.google.common.collect.ImmutableList<wbr>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">ListSessionsResponse.</span><code><a href="../ListSessionsResponse.html#sessions()" class="member-name-link">sessions</a>()</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.sessions</a> with parameters of type <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>default io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">BaseSessionService.</span><code><a href="../BaseSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color">
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.</div>
</div>
<div class="col-first odd-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">InMemorySessionService.</span><code><a href="../InMemorySessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Single<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">VertexAiSessionService.</span><code><a href="../VertexAiSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent</a><wbr>(<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;event)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>default io.reactivex.rxjava3.core.Completable</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">BaseSessionService.</span><code><a href="../BaseSessionService.html#closeSession(com.google.adk.sessions.Session)" class="member-name-link">closeSession</a><wbr>(<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&nbsp;session)</code></div>
<div class="col-last odd-row-color">
<div class="block">Closes a session.</div>
</div>
</div>
<div class="caption"><span>Method parameters in <a href="../package-summary.html">com.google.adk.sessions</a> with type arguments of type <a href="../Session.html" title="class in com.google.adk.sessions">Session</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>abstract <a href="../ListSessionsResponse.Builder.html" title="class in com.google.adk.sessions">ListSessionsResponse.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">ListSessionsResponse.Builder.</span><code><a href="../ListSessionsResponse.Builder.html#sessions(java.util.List)" class="member-name-link">sessions</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../Session.html" title="class in com.google.adk.sessions">Session</a>&gt;&nbsp;sessions)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
