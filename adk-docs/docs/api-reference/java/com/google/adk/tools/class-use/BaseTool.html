<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.tools.BaseTool (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.tools, class: BaseTool">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="../BaseTool.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.tools</a></li>
<li><a href="../BaseTool.html" class="current-selection">BaseTool</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.tools.BaseTool" class="title">Uses of Class<br>com.google.adk.tools.BaseTool</h1>
</div>
<div class="caption"><span>Packages that use <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.agents">com.google.adk.agents</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.flows.llmflows">com.google.adk.flows.llmflows</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.models">com.google.adk.models</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.tools">com.google.adk.tools</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><a href="#com.google.adk.tools.mcp">com.google.adk.tools.mcp</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="#com.google.adk.tools.retrieval">com.google.adk.tools.retrieval</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.agents">
<h2>Uses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../../agents/package-summary.html">com.google.adk.agents</a></h2>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> that return types with arguments of type <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a><wbr>&lt;<a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.</span><code><a href="../../agents/LlmAgent.html#tools()" class="member-name-link">tools</a>()</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Methods in <a href="../../agents/package-summary.html">com.google.adk.agents</a> with parameters of type <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.AfterToolCallback.</span><code><a href="../../agents/Callbacks.AfterToolCallback.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)" class="member-name-link">call</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;response)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.AfterToolCallbackSync.</span><code><a href="../../agents/Callbacks.AfterToolCallbackSync.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)" class="member-name-link">call</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&nbsp;response)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code>io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Callbacks.BeforeToolCallback.</span><code><a href="../../agents/Callbacks.BeforeToolCallback.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">call</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Optional.html" title="class or interface in java.util" class="external-link">Optional</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Callbacks.BeforeToolCallbackSync.</span><code><a href="../../agents/Callbacks.BeforeToolCallbackSync.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">call</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;baseTool,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;input,
 <a href="../ToolContext.html" title="class in com.google.adk.tools">ToolContext</a>&nbsp;toolContext)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
<div class="col-first even-row-color"><code><a href="../../agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../../agents/LlmAgent.Builder.html#tools(com.google.adk.tools.BaseTool...)" class="member-name-link">tools</a><wbr>(<a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>...&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<div class="caption"><span>Method parameters in <a href="../../agents/package-summary.html">com.google.adk.agents</a> with type arguments of type <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code><a href="../../agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmAgent.Builder.</span><code><a href="../../agents/LlmAgent.Builder.html#tools(java.util.List)" class="member-name-link">tools</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;? extends <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.flows.llmflows">
<h2>Uses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></h2>
<div class="caption"><span>Method parameters in <a href="../../flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a> with type arguments of type <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Set.html" title="class or interface in java.util" class="external-link">Set</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">Functions.</span><code><a href="../../flows/llmflows/Functions.html#getLongRunningFunctionCalls(java.util.List,java.util.Map)" class="member-name-link">getLongRunningFunctionCalls</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;com.google.genai.types.FunctionCall&gt;&nbsp;functionCalls,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><code>static io.reactivex.rxjava3.core.Maybe<wbr>&lt;<a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&gt;</code></div>
<div class="col-second odd-row-color"><span class="type-name-label">Functions.</span><code><a href="../../flows/llmflows/Functions.html#handleFunctionCalls(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,java.util.Map)" class="member-name-link">handleFunctionCalls</a><wbr>(<a href="../../agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a>&nbsp;invocationContext,
 <a href="../../events/Event.html" title="class in com.google.adk.events">Event</a>&nbsp;functionCallEvent,
 <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.models">
<h2>Uses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../../models/package-summary.html">com.google.adk.models</a></h2>
<div class="caption"><span>Methods in <a href="../../models/package-summary.html">com.google.adk.models</a> that return types with arguments of type <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>abstract <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;</code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.</span><code><a href="../../models/LlmRequest.html#tools()" class="member-name-link">tools</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Returns a map of tools available to the LLM.</div>
</div>
</div>
<div class="caption"><span>Method parameters in <a href="../../models/package-summary.html">com.google.adk.models</a> with type arguments of type <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>final <a href="../../models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">LlmRequest.Builder.</span><code><a href="../../models/LlmRequest.Builder.html#appendTools(java.util.List)" class="member-name-link">appendTools</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&gt;&nbsp;tools)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools">
<h2>Uses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../package-summary.html">com.google.adk.tools</a></h2>
<div class="caption"><span>Subclasses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../package-summary.html">com.google.adk.tools</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../AgentTool.html" class="type-name-link" title="class in com.google.adk.tools">AgentTool</a></code></div>
<div class="col-last even-row-color">
<div class="block">AgentTool implements a tool that allows an agent to call another agent.</div>
</div>
<div class="col-first odd-row-color"><code>final class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../BuiltInCodeExecutionTool.html" class="type-name-link" title="class in com.google.adk.tools">BuiltInCodeExecutionTool</a></code></div>
<div class="col-last odd-row-color">
<div class="block">A built-in code execution tool that is automatically invoked by Gemini 2 models.</div>
</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../FunctionTool.html" class="type-name-link" title="class in com.google.adk.tools">FunctionTool</a></code></div>
<div class="col-last even-row-color">
<div class="block">FunctionTool implements a customized function calling tool.</div>
</div>
<div class="col-first odd-row-color"><code>final class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../GoogleSearchTool.html" class="type-name-link" title="class in com.google.adk.tools">GoogleSearchTool</a></code></div>
<div class="col-last odd-row-color">
<div class="block">A built-in tool that is automatically invoked by Gemini 2 models to retrieve search results from
 Google Search.</div>
</div>
<div class="col-first even-row-color"><code>final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../LoadArtifactsTool.html" class="type-name-link" title="class in com.google.adk.tools">LoadArtifactsTool</a></code></div>
<div class="col-last even-row-color">
<div class="block">A tool that loads artifacts and adds them to the session.</div>
</div>
<div class="col-first odd-row-color"><code>class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="../LongRunningFunctionTool.html" class="type-name-link" title="class in com.google.adk.tools">LongRunningFunctionTool</a></code></div>
<div class="col-last odd-row-color">
<div class="block">A function tool that returns the result asynchronously.</div>
</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools.mcp">
<h2>Uses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../mcp/package-summary.html">com.google.adk.tools.mcp</a></h2>
<div class="caption"><span>Subclasses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../mcp/package-summary.html">com.google.adk.tools.mcp</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>final class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../mcp/McpTool.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpTool</a></code></div>
<div class="col-last even-row-color">
<div class="block">"""Initializes a MCPTool.</div>
</div>
</div>
<div class="caption"><span>Methods in <a href="../mcp/package-summary.html">com.google.adk.tools.mcp</a> with parameters of type <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>io.modelcontextprotocol.spec.McpSchema.Tool</code></div>
<div class="col-second even-row-color"><span class="type-name-label">ConversionUtils.</span><code><a href="../mcp/ConversionUtils.html#adkToMcpToolType(com.google.adk.tools.BaseTool)" class="member-name-link">adkToMcpToolType</a><wbr>(<a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a>&nbsp;tool)</code></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
</section>
</li>
<li>
<section class="detail" id="com.google.adk.tools.retrieval">
<h2>Uses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../retrieval/package-summary.html">com.google.adk.tools.retrieval</a></h2>
<div class="caption"><span>Subclasses of <a href="../BaseTool.html" title="class in com.google.adk.tools">BaseTool</a> in <a href="../retrieval/package-summary.html">com.google.adk.tools.retrieval</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="../retrieval/BaseRetrievalTool.html" class="type-name-link" title="class in com.google.adk.tools.retrieval">BaseRetrievalTool</a></code></div>
<div class="col-last even-row-color">
<div class="block">Base class for retrieval tools.</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
