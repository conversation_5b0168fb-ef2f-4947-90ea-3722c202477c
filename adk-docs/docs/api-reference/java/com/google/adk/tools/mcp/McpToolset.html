<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Mcp<PERSON>oolset (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.tools.mcp, class: McpToolset">
<meta name="generator" content="javadoc/ClassWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Class</li>
<li><a href="class-use/McpToolset.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html">com.google.adk.tools.mcp</a></li>
<li><a href="McpToolset.html" class="current-selection">McpToolset</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#nested-class-summary" tabindex="0">Nested Class Summary</a></li>
<li><a href="#constructor-summary" tabindex="0">Constructor Summary</a></li>
<li><a href="#method-summary" tabindex="0">Method Summary</a></li>
<li><a href="#constructor-detail" tabindex="0">Constructor Details</a>
<ol class="toc-list">
<li><a href="#%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" tabindex="0">McpToolset(SseServerParameters, ObjectMapper)</a></li>
<li><a href="#%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" tabindex="0">McpToolset(ServerParameters, ObjectMapper)</a></li>
<li><a href="#%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters)" tabindex="0">McpToolset(SseServerParameters)</a></li>
<li><a href="#%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters)" tabindex="0">McpToolset(ServerParameters)</a></li>
</ol>
</li>
<li><a href="#method-detail" tabindex="0">Method Details</a>
<ol class="toc-list">
<li><a href="#fromServer(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" tabindex="0">fromServer(SseServerParameters, ObjectMapper)</a></li>
<li><a href="#fromServer(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" tabindex="0">fromServer(ServerParameters, ObjectMapper)</a></li>
<li><a href="#fromServer(com.google.adk.tools.mcp.SseServerParameters)" tabindex="0">fromServer(SseServerParameters)</a></li>
<li><a href="#fromServer(io.modelcontextprotocol.client.transport.ServerParameters)" tabindex="0">fromServer(ServerParameters)</a></li>
<li><a href="#loadTools()" tabindex="0">loadTools()</a></li>
<li><a href="#close()" tabindex="0">close()</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<h1 title="Class McpToolset" class="title">Class McpToolset</h1>
</div>
<div class="inheritance" title="Inheritance Tree"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">java.lang.Object</a>
<div class="inheritance">com.google.adk.tools.mcp.McpToolset</div>
</div>
<section class="class-description" id="class-description">
<dl class="notes">
<dt>All Implemented Interfaces:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
</dl>
<hr>
<div class="horizontal-scroll">
<div class="type-signature"><span class="modifiers">public class </span><span class="element-name type-name-label">McpToolset</span>
<span class="extends-implements">extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>
implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></span></div>
<div class="block">Connects to a MCP Server, and retrieves MCP Tools into ADK Tools.

 <p>Attributes:

 <ul>
   <li><code>connectionParams</code>: The connection parameters to the MCP server. Can be either <code>
       ServerParameters</code> or <code>SseServerParameters</code>.
   <li><code>exit_stack</code>: (Python concept) The async exit stack to manage the connection to the
       MCP server. In Java, this is implicitly handled by <code>McpToolset</code> implementing <code>
       AutoCloseable</code>.
   <li><code>session</code>: The MCP session being initialized with the connection.
 </ul></div>
</div>
</section>
<section class="summary">
<ul class="summary-list">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<li>
<section class="nested-class-summary" id="nested-class-summary">
<h2>Nested Class Summary</h2>
<div class="caption"><span>Nested Classes</span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="McpToolset.McpInitializationException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a></code></div>
<div class="col-last even-row-color">
<div class="block">Exception thrown when there's an error during MCP session initialization.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="McpToolset.McpToolLoadingException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Exception thrown when there's an error during loading tools from the MCP server.</div>
</div>
<div class="col-first even-row-color"><code>static class&nbsp;</code></div>
<div class="col-second even-row-color"><code><a href="McpToolset.McpToolsAndToolsetResult.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></code></div>
<div class="col-last even-row-color">
<div class="block">Holds the result of loading tools, containing both the tools and the toolset instance.</div>
</div>
<div class="col-first odd-row-color"><code>static class&nbsp;</code></div>
<div class="col-second odd-row-color"><code><a href="McpToolset.McpToolsetException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a></code></div>
<div class="col-last odd-row-color">
<div class="block">Base exception for all errors originating from <code>McpToolset</code>.</div>
</div>
</div>
</section>
</li>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<li>
<section class="constructor-summary" id="constructor-summary">
<h2>Constructor Summary</h2>
<div class="caption"><span>Constructors</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Constructor</div>
<div class="table-header col-last">Description</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters)" class="member-name-link">McpToolset</a><wbr>(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams)</code></div>
<div class="col-last even-row-color">
<div class="block">Initializes the McpToolset with SSE server parameters, using the ObjectMapper used across the
 ADK.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">McpToolset</a><wbr>(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</code></div>
<div class="col-last odd-row-color">
<div class="block">Initializes the McpToolset with SSE server parameters.</div>
</div>
<div class="col-constructor-name even-row-color"><code><a href="#%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters)" class="member-name-link">McpToolset</a><wbr>(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams)</code></div>
<div class="col-last even-row-color">
<div class="block">Initializes the McpToolset with local server parameters, using the ObjectMapper used across the
 ADK.</div>
</div>
<div class="col-constructor-name odd-row-color"><code><a href="#%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">McpToolset</a><wbr>(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</code></div>
<div class="col-last odd-row-color">
<div class="block">Initializes the McpToolset with local server parameters.</div>
</div>
</div>
</section>
</li>
<!-- ========== METHOD SUMMARY =========== -->
<li>
<section class="method-summary" id="method-summary">
<h2>Method Summary</h2>
<div id="method-summary-table">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="method-summary-table-tab0" role="tab" aria-selected="true" aria-controls="method-summary-table.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table', 3)" class="active-table-tab">All Methods</button><button id="method-summary-table-tab1" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab1', 3)" class="table-tab">Static Methods</button><button id="method-summary-table-tab2" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab2', 3)" class="table-tab">Instance Methods</button><button id="method-summary-table-tab4" role="tab" aria-selected="false" aria-controls="method-summary-table.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('method-summary-table', 'method-summary-table-tab4', 3)" class="table-tab">Concrete Methods</button></div>
<div id="method-summary-table.tabpanel" role="tabpanel" aria-labelledby="method-summary-table-tab0">
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code>void</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#close()" class="member-name-link">close</a>()</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Closes the connection to MCP Server.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a><wbr>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromServer(com.google.adk.tools.mcp.SseServerParameters)" class="member-name-link">fromServer</a><wbr>(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Retrieve all tools from the MCP connection using SSE server parameters and the ObjectMapper
 used across the ADK.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a><wbr>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromServer(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">fromServer</a><wbr>(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Retrieve all tools from the MCP connection using SSE server parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a><wbr>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromServer(io.modelcontextprotocol.client.transport.ServerParameters)" class="member-name-link">fromServer</a><wbr>(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams)</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Retrieve all tools from the MCP connection using local server parameters and the ObjectMapper
 used across the ADK.</div>
</div>
<div class="col-first even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code>static <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a><wbr>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</code></div>
<div class="col-second even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4"><code><a href="#fromServer(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">fromServer</a><wbr>(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</code></div>
<div class="col-last even-row-color method-summary-table method-summary-table-tab1 method-summary-table-tab4">
<div class="block">Retrieve all tools from the MCP connection using local server parameters.</div>
</div>
<div class="col-first odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a><wbr>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a>&gt;&gt;</code></div>
<div class="col-second odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4"><code><a href="#loadTools()" class="member-name-link">loadTools</a>()</code></div>
<div class="col-last odd-row-color method-summary-table method-summary-table-tab2 method-summary-table-tab4">
<div class="block">Loads all tools from the MCP Server.</div>
</div>
</div>
</div>
</div>
<div class="inherited-list">
<h3 id="methods-inherited-from-class-java.lang.Object">Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a></h3>
<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#clone()" title="class or interface in java.lang" class="external-link">clone</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#equals(java.lang.Object)" title="class or interface in java.lang" class="external-link">equals</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#finalize()" title="class or interface in java.lang" class="external-link">finalize</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#getClass()" title="class or interface in java.lang" class="external-link">getClass</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#hashCode()" title="class or interface in java.lang" class="external-link">hashCode</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notify()" title="class or interface in java.lang" class="external-link">notify</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#notifyAll()" title="class or interface in java.lang" class="external-link">notifyAll</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#toString()" title="class or interface in java.lang" class="external-link">toString</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait()" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long)" title="class or interface in java.lang" class="external-link">wait</a>, <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html#wait(long,int)" title="class or interface in java.lang" class="external-link">wait</a></code></div>
</section>
</li>
</ul>
</section>
<section class="details">
<ul class="details-list">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<li>
<section class="constructor-details" id="constructor-detail">
<h2>Constructor Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="&lt;init&gt;(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)">
<h3>McpToolset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">McpToolset</span><wbr><span class="parameters">(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</span></div>
<div class="block">Initializes the McpToolset with SSE server parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The SSE connection parameters to the MCP server.</dd>
<dd><code>objectMapper</code> - An ObjectMapper instance for parsing schemas.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)">
<h3>McpToolset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">McpToolset</span><wbr><span class="parameters">(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</span></div>
<div class="block">Initializes the McpToolset with local server parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The local server connection parameters to the MCP server.</dd>
<dd><code>objectMapper</code> - An ObjectMapper instance for parsing schemas.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(com.google.adk.tools.mcp.SseServerParameters)">
<h3>McpToolset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">McpToolset</span><wbr><span class="parameters">(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams)</span></div>
<div class="block">Initializes the McpToolset with SSE server parameters, using the ObjectMapper used across the
 ADK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The SSE connection parameters to the MCP server.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="&lt;init&gt;(io.modelcontextprotocol.client.transport.ServerParameters)">
<h3>McpToolset</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="element-name">McpToolset</span><wbr><span class="parameters">(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams)</span></div>
<div class="block">Initializes the McpToolset with local server parameters, using the ObjectMapper used across the
 ADK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The local server connection parameters to the MCP server.</dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
<!-- ============ METHOD DETAIL ========== -->
<li>
<section class="method-details" id="method-detail">
<h2>Method Details</h2>
<ul class="member-list">
<li>
<section class="detail" id="fromServer(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)">
<h3>fromServer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</span>&nbsp;<span class="element-name">fromServer</span><wbr><span class="parameters">(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</span></div>
<div class="block">Retrieve all tools from the MCP connection using SSE server parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The SSE connection parameters to the MCP server.</dd>
<dd><code>objectMapper</code> - An ObjectMapper instance for parsing schemas.</dd>
<dt>Returns:</dt>
<dd>A <code>CompletableFuture</code> of <code>McpToolsAndToolsetResult</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fromServer(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)">
<h3>fromServer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</span>&nbsp;<span class="element-name">fromServer</span><wbr><span class="parameters">(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams,
 com.fasterxml.jackson.databind.ObjectMapper&nbsp;objectMapper)</span></div>
<div class="block">Retrieve all tools from the MCP connection using local server parameters.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The local server connection parameters to the MCP server.</dd>
<dd><code>objectMapper</code> - An ObjectMapper instance for parsing schemas.</dd>
<dt>Returns:</dt>
<dd>A <code>CompletableFuture</code> of <code>McpToolsAndToolsetResult</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fromServer(com.google.adk.tools.mcp.SseServerParameters)">
<h3>fromServer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</span>&nbsp;<span class="element-name">fromServer</span><wbr><span class="parameters">(<a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a>&nbsp;connectionParams)</span></div>
<div class="block">Retrieve all tools from the MCP connection using SSE server parameters and the ObjectMapper
 used across the ADK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The SSE connection parameters to the MCP server.</dd>
<dt>Returns:</dt>
<dd>A <code>CompletableFuture</code> of <code>McpToolsAndToolsetResult</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="fromServer(io.modelcontextprotocol.client.transport.ServerParameters)">
<h3>fromServer</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public static</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a>&lt;<a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a>&gt;</span>&nbsp;<span class="element-name">fromServer</span><wbr><span class="parameters">(io.modelcontextprotocol.client.transport.ServerParameters&nbsp;connectionParams)</span></div>
<div class="block">Retrieve all tools from the MCP connection using local server parameters and the ObjectMapper
 used across the ADK.</div>
<dl class="notes">
<dt>Parameters:</dt>
<dd><code>connectionParams</code> - The local server connection parameters to the MCP server.</dd>
<dt>Returns:</dt>
<dd>A <code>CompletableFuture</code> of <code>McpToolsAndToolsetResult</code>.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="loadTools()">
<h3>loadTools</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type"><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/CompletableFuture.html" title="class or interface in java.util.concurrent" class="external-link">CompletableFuture</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/List.html" title="class or interface in java.util" class="external-link">List</a>&lt;<a href="McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a>&gt;&gt;</span>&nbsp;<span class="element-name">loadTools</span>()</div>
<div class="block">Loads all tools from the MCP Server. This method includes retry logic in case of transient
 session issues.</div>
<dl class="notes">
<dt>Returns:</dt>
<dd>A <code>CompletableFuture</code> that completes with a list of <code>McpTool</code>s imported
     from the MCP Server.</dd>
</dl>
</div>
</section>
</li>
<li>
<section class="detail" id="close()">
<h3>close</h3>
<div class="horizontal-scroll">
<div class="member-signature"><span class="modifiers">public</span>&nbsp;<span class="return-type">void</span>&nbsp;<span class="element-name">close</span>()</div>
<div class="block">Closes the connection to MCP Server. This method is part of the <code>AutoCloseable</code>
 interface, allowing <code>McpToolset</code> to be used in a try-with-resources statement for
 automatic resource management.</div>
<dl class="notes">
<dt>Specified by:</dt>
<dd><code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html#close()" title="class or interface in java.lang" class="external-link">close</a></code>&nbsp;in interface&nbsp;<code><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a></code></dd>
</dl>
</div>
</section>
</li>
</ul>
</section>
</li>
</ul>
</section>
<!-- ========= END OF CLASS DATA ========= -->
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
