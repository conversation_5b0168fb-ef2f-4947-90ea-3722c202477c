<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Uses of Class com.google.adk.tools.mcp.SseServerParameters.Builder (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="use: package: com.google.adk.tools.mcp, class: SseServerParameters, class: Builder">
<meta name="generator" content="javadoc/ClassUseWriter">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="class-use-page">
<script type="text/javascript">const pathtoroot = "../../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../../index.html">Overview</a></li>
<li><a href="../SseServerParameters.Builder.html">Class</a></li>
<li class="nav-bar-cell1-rev">Use</li>
<li><a href="../package-tree.html">Tree</a></li>
<li><a href="../../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="../package-summary.html">com.google.adk.tools.mcp</a></li>
<li><a href="../SseServerParameters.html">SseServerParameters</a></li>
<li><a href="../SseServerParameters.Builder.html" class="current-selection">Builder</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Uses of Class com.google.adk.tools.mcp.SseServerParameters.Builder" class="title">Uses of Class<br>com.google.adk.tools.mcp.SseServerParameters.Builder</h1>
</div>
<div class="caption"><span>Packages that use <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="#com.google.adk.tools.mcp">com.google.adk.tools.mcp</a></div>
<div class="col-last even-row-color">&nbsp;</div>
</div>
<section class="class-uses">
<ul class="block-list">
<li>
<section class="detail" id="com.google.adk.tools.mcp">
<h2>Uses of <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a> in <a href="../package-summary.html">com.google.adk.tools.mcp</a></h2>
<div class="caption"><span>Methods in <a href="../package-summary.html">com.google.adk.tools.mcp</a> that return <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Method</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><code>static <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">SseServerParameters.</span><code><a href="../SseServerParameters.html#builder()" class="member-name-link">builder</a>()</code></div>
<div class="col-last even-row-color">
<div class="block">Creates a new builder for <a href="../SseServerParameters.html" title="class in com.google.adk.tools.mcp"><code>SseServerParameters</code></a>.</div>
</div>
<div class="col-first odd-row-color"><code>abstract <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">SseServerParameters.Builder.</span><code><a href="../SseServerParameters.Builder.html#headers(java.util.Map)" class="member-name-link">headers</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/Map.html" title="class or interface in java.util" class="external-link">Map</a>&lt;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>,<wbr><a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" title="class or interface in java.lang" class="external-link">Object</a>&gt;&nbsp;headers)</code></div>
<div class="col-last odd-row-color">
<div class="block">Sets the headers for the SSE connection request.</div>
</div>
<div class="col-first even-row-color"><code>abstract <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">SseServerParameters.Builder.</span><code><a href="../SseServerParameters.Builder.html#sseReadTimeout(java.time.Duration)" class="member-name-link">sseReadTimeout</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Duration.html" title="class or interface in java.time" class="external-link">Duration</a>&nbsp;sseReadTimeout)</code></div>
<div class="col-last even-row-color">
<div class="block">Sets the timeout for reading data from the SSE stream.</div>
</div>
<div class="col-first odd-row-color"><code>abstract <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></code></div>
<div class="col-second odd-row-color"><span class="type-name-label">SseServerParameters.Builder.</span><code><a href="../SseServerParameters.Builder.html#timeout(java.time.Duration)" class="member-name-link">timeout</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/time/Duration.html" title="class or interface in java.time" class="external-link">Duration</a>&nbsp;timeout)</code></div>
<div class="col-last odd-row-color">
<div class="block">Sets the timeout for the initial connection attempt.</div>
</div>
<div class="col-first even-row-color"><code>abstract <a href="../SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></code></div>
<div class="col-second even-row-color"><span class="type-name-label">SseServerParameters.Builder.</span><code><a href="../SseServerParameters.Builder.html#url(java.lang.String)" class="member-name-link">url</a><wbr>(<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a>&nbsp;url)</code></div>
<div class="col-last even-row-color">
<div class="block">Sets the URL of the SSE server.</div>
</div>
</div>
</section>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
