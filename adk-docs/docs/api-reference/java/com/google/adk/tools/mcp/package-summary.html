<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>com.google.adk.tools.mcp (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="declaration: package: com.google.adk.tools.mcp">
<meta name="generator" content="javadoc/PackageWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-declaration-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Package</li>
<li><a href="package-use.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html" class="current-selection">com.google.adk.tools.mcp</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Description</a></li>
<li><a href="#related-package-summary" tabindex="0">Related Packages</a></li>
<li><a href="#class-summary" tabindex="0">Classes and Interfaces</a></li>
</ol>
</nav>
<main role="main">
<div class="header">
<h1 title="Package com.google.adk.tools.mcp" class="title">Package com.google.adk.tools.mcp</h1>
</div>
<hr>
<div class="horizontal-scroll">
<div class="package-signature">package <span class="element-name">com.google.adk.tools.mcp</span></div>
</div>
<section class="summary">
<ul class="summary-list">
<li>
<div id="related-package-summary">
<div class="caption"><span>Related Packages</span></div>
<div class="summary-table two-column-summary">
<div class="table-header col-first">Package</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color"><a href="../package-summary.html">com.google.adk.tools</a></div>
<div class="col-last even-row-color">&nbsp;</div>
<div class="col-first odd-row-color"><a href="../retrieval/package-summary.html">com.google.adk.tools.retrieval</a></div>
<div class="col-last odd-row-color">&nbsp;</div>
</div>
</div>
</li>
<li>
<div id="class-summary">
<div class="table-tabs" role="tablist" aria-orientation="horizontal"><button id="class-summary-tab0" role="tab" aria-selected="true" aria-controls="class-summary.tabpanel" tabindex="0" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary', 2)" class="active-table-tab">All Classes and Interfaces</button><button id="class-summary-tab2" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab2', 2)" class="table-tab">Classes</button><button id="class-summary-tab5" role="tab" aria-selected="false" aria-controls="class-summary.tabpanel" tabindex="-1" onkeydown="switchTab(event)" onclick="show('class-summary', 'class-summary-tab5', 2)" class="table-tab">Exception Classes</button></div>
<div id="class-summary.tabpanel" role="tabpanel" aria-labelledby="class-summary-tab0">
<div class="summary-table two-column-summary">
<div class="table-header col-first">Class</div>
<div class="table-header col-last">Description</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="ConversionUtils.html" title="class in com.google.adk.tools.mcp">ConversionUtils</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Utility class for converting between different representations of MCP tools.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="McpSessionManager.html" title="class in com.google.adk.tools.mcp">McpSessionManager</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Manages MCP client sessions.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">"""Initializes a MCPTool.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Connects to a MCP Server, and retrieves MCP Tools into ADK Tools.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab5"><a href="McpToolset.McpInitializationException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a></div>
<div class="col-last even-row-color class-summary class-summary-tab5">
<div class="block">Exception thrown when there's an error during MCP session initialization.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="McpToolset.McpToolLoadingException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Exception thrown when there's an error during loading tools from the MCP server.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Holds the result of loading tools, containing both the tools and the toolset instance.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab5"><a href="McpToolset.McpToolsetException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab5">
<div class="block">Base exception for all errors originating from <code>McpToolset</code>.</div>
</div>
<div class="col-first even-row-color class-summary class-summary-tab2"><a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></div>
<div class="col-last even-row-color class-summary class-summary-tab2">
<div class="block">Parameters for establishing a MCP Server-Sent Events (SSE) connection.</div>
</div>
<div class="col-first odd-row-color class-summary class-summary-tab2"><a href="SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></div>
<div class="col-last odd-row-color class-summary class-summary-tab2">
<div class="block">Builder for <a href="SseServerParameters.html" title="class in com.google.adk.tools.mcp"><code>SseServerParameters</code></a>.</div>
</div>
</div>
</div>
</div>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
