<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>com.google.adk.tools.mcp Class Hierarchy (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="tree: package: com.google.adk.tools.mcp">
<meta name="generator" content="javadoc/PackageTreeWriter">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="../../../../../resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../../script-files/script.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="../../../../../script-files/jquery-ui.min.js"></script>
</head>
<body class="package-tree-page">
<script type="text/javascript">const pathtoroot = "../../../../../";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="../../../../../index.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li><a href="package-use.html">Use</a></li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="../../../../../index-all.html">Index</a></li>
<li><a href="../../../../../search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list">
<li><a href="package-summary.html" class="current-selection">com.google.adk.tools.mcp</a></li>
</ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For Package com.google.adk.tools.mcp</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="../../../../../overview-tree.html">All Packages</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">com.google.adk.tools.<a href="../BaseTool.html" class="type-name-link" title="class in com.google.adk.tools">BaseTool</a>
<ul>
<li class="circle">com.google.adk.tools.mcp.<a href="McpTool.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpTool</a></li>
</ul>
</li>
<li class="circle">com.google.adk.tools.mcp.<a href="ConversionUtils.html" class="type-name-link" title="class in com.google.adk.tools.mcp">ConversionUtils</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="McpSessionManager.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpSessionManager</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="McpToolset.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a>)</li>
<li class="circle">com.google.adk.tools.mcp.<a href="McpToolset.McpToolsAndToolsetResult.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="SseServerParameters.html" class="type-name-link" title="class in com.google.adk.tools.mcp">SseServerParameters</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="SseServerParameters.Builder.html" class="type-name-link" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/RuntimeException.html" class="type-name-link external-link" title="class or interface in java.lang">RuntimeException</a>
<ul>
<li class="circle">com.google.adk.tools.mcp.<a href="McpToolset.McpToolsetException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a>
<ul>
<li class="circle">com.google.adk.tools.mcp.<a href="McpToolset.McpInitializationException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="McpToolset.McpToolLoadingException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
