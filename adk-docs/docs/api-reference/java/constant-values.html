<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Constant Field Values (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="summary of constants">
<meta name="generator" content="javadoc/ConstantsSummaryWriter">
<link rel="stylesheet" type="text/css" href="resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="script-files/script.js"></script>
<script type="text/javascript" src="script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-files/jquery-ui.min.js"></script>
</head>
<body class="constants-summary-page">
<script type="text/javascript">const pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list"></ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<nav role="navigation" class="toc" aria-label="Table of contents">
<div class="toc-header">Contents&nbsp;
<input type="text" class="filter-input" disabled placeholder="Filter" aria-label="Filter table of contents" autocomplete="off">
<input type="reset" class="reset-filter" disabled value="Reset">
</div>
<button class="hide-sidebar"><span>Hide sidebar&nbsp;</span>&#10094;</button><button class="show-sidebar">&#10095;<span>&nbsp;Show sidebar</span></button>
<ol class="toc-list">
<li><a href="#" tabindex="0">Constant Field Values</a>
<ol class="toc-list">
<li><a href="#com.google" tabindex="0">com.google.*</a></li>
</ol>
</li>
</ol>
</nav>
<main role="main">
<div class="header">
<h1 title="Constant Field Values" class="title">Constant Field Values</h1>
</div>
<section class="constants-summary" id="com.google">
<h2 title="com.google.*">com.google.*</h2>
<ul class="block-list">
<li>
<div class="caption"><span>com.google.adk.<a href="com/google/adk/Version.html" title="class in com.google.adk">Version</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="com.google.adk.Version.JAVA_ADK_VERSION">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="com/google/adk/Version.html#JAVA_ADK_VERSION">JAVA_ADK_VERSION</a></code></div>
<div class="col-last even-row-color"><code>"0.1.0"</code></div>
</div>
</li>
</ul>
<ul class="block-list">
<li>
<div class="caption"><span>com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></span></div>
<div class="summary-table three-column-summary">
<div class="table-header col-first">Modifier and Type</div>
<div class="table-header col-second">Constant Field</div>
<div class="table-header col-last">Value</div>
<div class="col-first even-row-color"><code id="com.google.adk.sessions.State.APP_PREFIX">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="com/google/adk/sessions/State.html#APP_PREFIX">APP_PREFIX</a></code></div>
<div class="col-last even-row-color"><code>"app:"</code></div>
<div class="col-first odd-row-color"><code id="com.google.adk.sessions.State.TEMP_PREFIX">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second odd-row-color"><code><a href="com/google/adk/sessions/State.html#TEMP_PREFIX">TEMP_PREFIX</a></code></div>
<div class="col-last odd-row-color"><code>"temp:"</code></div>
<div class="col-first even-row-color"><code id="com.google.adk.sessions.State.USER_PREFIX">public&nbsp;static&nbsp;final&nbsp;<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/String.html" title="class or interface in java.lang" class="external-link">String</a></code></div>
<div class="col-second even-row-color"><code><a href="com/google/adk/sessions/State.html#USER_PREFIX">USER_PREFIX</a></code></div>
<div class="col-last even-row-color"><code>"user:"</code></div>
</div>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
