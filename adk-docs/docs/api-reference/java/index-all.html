<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Index (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="index">
<meta name="generator" content="javadoc/IndexWriter">
<link rel="stylesheet" type="text/css" href="resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="script-files/script.js"></script>
<script type="text/javascript" src="script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-files/jquery-ui.min.js"></script>
</head>
<body class="index-page">
<script type="text/javascript">const pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li><a href="overview-tree.html">Tree</a></li>
<li class="nav-bar-cell1-rev">Index</li>
<li><a href="search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list"></ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1>Index</h1>
</div>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">All&nbsp;Packages</a><span class="vertical-separator">|</span><a href="constant-values.html">Constant&nbsp;Field&nbsp;Values</a><span class="vertical-separator">|</span><a href="serialized-form.html">Serialized&nbsp;Form</a>
<h2 class="title" id="I:A">A</h2>
<dl class="index">
<dt><a href="com/google/adk/events/Event.html#actions()" class="member-name-link">actions()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ToolContext.html#actions()" class="member-name-link">actions()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#actions(com.google.adk.events.EventActions)" class="member-name-link">actions(EventActions)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ToolContext.Builder.html#actions(com.google.adk.events.EventActions)" class="member-name-link">actions(EventActions)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.Builder.html" title="class in com.google.adk.tools">ToolContext.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/ConversionUtils.html#adkToMcpToolType(com.google.adk.tools.BaseTool)" class="member-name-link">adkToMcpToolType(BaseTool)</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/ConversionUtils.html" title="class in com.google.adk.tools.mcp">ConversionUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#afterAgentCallback()" class="member-name-link">afterAgentCallback()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">afterAgentCallback(Callbacks.AfterAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">afterAgentCallback(Callbacks.AfterAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">afterAgentCallback(Callbacks.AfterAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">afterAgentCallback(Callbacks.AfterAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#afterAgentCallbackSync(com.google.adk.agents.Callbacks.AfterAgentCallbackSync)" class="member-name-link">afterAgentCallbackSync(Callbacks.AfterAgentCallbackSync)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#afterModelCallback()" class="member-name-link">afterModelCallback()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#afterModelCallback(com.google.adk.agents.Callbacks.AfterModelCallback)" class="member-name-link">afterModelCallback(Callbacks.AfterModelCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#afterModelCallbackSync(com.google.adk.agents.Callbacks.AfterModelCallbackSync)" class="member-name-link">afterModelCallbackSync(Callbacks.AfterModelCallbackSync)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.html#afterTimestamp()" class="member-name-link">afterTimestamp()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.Builder.html#afterTimestamp(java.time.Instant)" class="member-name-link">afterTimestamp(Instant)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.Builder.html" title="class in com.google.adk.sessions">GetSessionConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#afterToolCallback()" class="member-name-link">afterToolCallback()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#afterToolCallback(com.google.adk.agents.Callbacks.AfterToolCallback)" class="member-name-link">afterToolCallback(Callbacks.AfterToolCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#afterToolCallbackSync(com.google.adk.agents.Callbacks.AfterToolCallbackSync)" class="member-name-link">afterToolCallbackSync(Callbacks.AfterToolCallbackSync)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#agent()" class="member-name-link">agent()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#agent()" class="member-name-link">agent()</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#agent(com.google.adk.agents.BaseAgent)" class="member-name-link">agent(BaseAgent)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ReadonlyContext.html#agentName()" class="member-name-link">agentName()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ReadonlyContext.html" title="class in com.google.adk.agents">ReadonlyContext</a></dt>
<dd>
<div class="block">Returns the name of the agent currently running.</div>
</dd>
<dt><a href="com/google/adk/tools/AgentTool.html" class="type-name-link" title="class in com.google.adk.tools">AgentTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">AgentTool implements a tool that allows an agent to call another agent.</div>
</dd>
<dt><a href="com/google/adk/tools/AgentTool.html#%3Cinit%3E(com.google.adk.agents.BaseAgent,boolean)" class="member-name-link">AgentTool(BaseAgent, boolean)</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/AgentTransfer.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">AgentTransfer</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block"><code>RequestProcessor</code> that handles agent transfer for LLM flow.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/AgentTransfer.html#%3Cinit%3E()" class="member-name-link">AgentTransfer()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/AgentTransfer.html" title="class in com.google.adk.flows.llmflows">AgentTransfer</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/Annotations.html" class="type-name-link" title="class in com.google.adk.tools">Annotations</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">Annotations for tools.</div>
</dd>
<dt><a href="com/google/adk/tools/Annotations.html#%3Cinit%3E()" class="member-name-link">Annotations()</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/Annotations.html" title="class in com.google.adk.tools">Annotations</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/Annotations.Schema.html" class="type-name-link" title="annotation interface in com.google.adk.tools">Annotations.Schema</a> - Annotation Interface in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">The annotation for binding the 'Schema' input.</div>
</dd>
<dt><a href="com/google/adk/models/Gemini.Builder.html#apiClient(com.google.genai.Client)" class="member-name-link">apiClient(Client)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></dt>
<dd>
<div class="block">Sets the explicit <code>Client</code> instance for making API calls.</div>
</dd>
<dt><a href="com/google/adk/sessions/HttpApiClient.html#apiKey()" class="member-name-link">apiKey()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiClient.html" title="class in com.google.adk.sessions">HttpApiClient</a></dt>
<dd>
<div class="block">Returns the API key for Google AI APIs.</div>
</dd>
<dt><a href="com/google/adk/models/Gemini.Builder.html#apiKey(java.lang.String)" class="member-name-link">apiKey(String)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></dt>
<dd>
<div class="block">Sets the Google Gemini API key.</div>
</dd>
<dt><a href="com/google/adk/network/ApiResponse.html" class="type-name-link" title="class in com.google.adk.network">ApiResponse</a> - Class in <a href="com/google/adk/network/package-summary.html">com.google.adk.network</a></dt>
<dd>
<div class="block">The API response contains a response to a call to the GenAI APIs.</div>
</dd>
<dt><a href="com/google/adk/sessions/ApiResponse.html" class="type-name-link" title="class in com.google.adk.sessions">ApiResponse</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">The API response contains a response to a call to the GenAI APIs.</div>
</dd>
<dt><a href="com/google/adk/network/ApiResponse.html#%3Cinit%3E()" class="member-name-link">ApiResponse()</a> - Constructor for class com.google.adk.network.<a href="com/google/adk/network/ApiResponse.html" title="class in com.google.adk.network">ApiResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ApiResponse.html#%3Cinit%3E()" class="member-name-link">ApiResponse()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/ApiResponse.html" title="class in com.google.adk.sessions">ApiResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#APP_PREFIX" class="member-name-link">APP_PREFIX</a> - Static variable in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/LoadArtifactsTool.html#appendArtifactsToLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">appendArtifactsToLlmRequest(LlmRequest.Builder, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/LoadArtifactsTool.html" title="class in com.google.adk.tools">LoadArtifactsTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent(Session, Event)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Appends an event to an in-memory session object and updates the session's state based on the
 event's state delta, if applicable.</div>
</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent(Session, Event)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)" class="member-name-link">appendEvent(Session, Event)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#appendInstructions(java.util.List)" class="member-name-link">appendInstructions(List&lt;String&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#appendTools(java.util.List)" class="member-name-link">appendTools(List&lt;BaseTool&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#appName()" class="member-name-link">appName()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#appName()" class="member-name-link">appName()</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html#appName()" class="member-name-link">appName()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#appName(java.lang.String)" class="member-name-link">appName(String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#artifactDelta()" class="member-name-link">artifactDelta()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#artifactDelta(java.util.concurrent.ConcurrentMap)" class="member-name-link">artifactDelta(ConcurrentMap&lt;String, Part&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#artifactService()" class="member-name-link">artifactService()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#artifactService()" class="member-name-link">artifactService()</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#author()" class="member-name-link">author()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">The author of the event, it could be the name of the agent or "user" literal.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#author(java.lang.String)" class="member-name-link">author(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/AutoFlow.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">AutoFlow</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/AutoFlow.html#%3Cinit%3E()" class="member-name-link">AutoFlow()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/AutoFlow.html" title="class in com.google.adk.flows.llmflows">AutoFlow</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:B">B</h2>
<dl class="index">
<dt><a href="com/google/adk/agents/BaseAgent.html" class="type-name-link" title="class in com.google.adk.agents">BaseAgent</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Base class for all agents.</div>
</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#%3Cinit%3E(java.lang.String,java.lang.String,java.util.List,com.google.adk.agents.Callbacks.BeforeAgentCallback,com.google.adk.agents.Callbacks.AfterAgentCallback)" class="member-name-link">BaseAgent(String, String, List&lt;? extends BaseAgent&gt;, Callbacks.BeforeAgentCallback, Callbacks.AfterAgentCallback)</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/BaseArtifactService.html" class="type-name-link" title="interface in com.google.adk.artifacts">BaseArtifactService</a> - Interface in <a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a></dt>
<dd>
<div class="block">Base interface for artifact services.</div>
</dd>
<dt><a href="com/google/adk/examples/BaseExampleProvider.html" class="type-name-link" title="interface in com.google.adk.examples">BaseExampleProvider</a> - Interface in <a href="com/google/adk/examples/package-summary.html">com.google.adk.examples</a></dt>
<dd>
<div class="block">An interface that provides examples for a given query.</div>
</dd>
<dt><a href="com/google/adk/flows/BaseFlow.html" class="type-name-link" title="interface in com.google.adk.flows">BaseFlow</a> - Interface in <a href="com/google/adk/flows/package-summary.html">com.google.adk.flows</a></dt>
<dd>
<div class="block">Interface for the execution flows to run a group of agents.</div>
</dd>
<dt><a href="com/google/adk/models/BaseLlm.html" class="type-name-link" title="class in com.google.adk.models">BaseLlm</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Abstract base class for Large Language Models (LLMs).</div>
</dd>
<dt><a href="com/google/adk/models/BaseLlm.html#%3Cinit%3E(java.lang.String)" class="member-name-link">BaseLlm(String)</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlmConnection.html" class="type-name-link" title="interface in com.google.adk.models">BaseLlmConnection</a> - Interface in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">The base class for a live model connection.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block">A basic flow that calls the LLM in a loop until a final response is generated.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html#%3Cinit%3E(java.util.List,java.util.List)" class="member-name-link">BaseLlmFlow(List&lt;RequestProcessor&gt;, List&lt;ResponseProcessor&gt;)</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html" class="type-name-link" title="class in com.google.adk.tools.retrieval">BaseRetrievalTool</a> - Class in <a href="com/google/adk/tools/retrieval/package-summary.html">com.google.adk.tools.retrieval</a></dt>
<dd>
<div class="block">Base class for retrieval tools.</div>
</dd>
<dt><a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">BaseRetrievalTool(String, String)</a> - Constructor for class com.google.adk.tools.retrieval.<a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html" title="class in com.google.adk.tools.retrieval">BaseRetrievalTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean)" class="member-name-link">BaseRetrievalTool(String, String, boolean)</a> - Constructor for class com.google.adk.tools.retrieval.<a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html" title="class in com.google.adk.tools.retrieval">BaseRetrievalTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html" class="type-name-link" title="interface in com.google.adk.sessions">BaseSessionService</a> - Interface in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Defines the contract for managing <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a>s and their associated <a href="com/google/adk/events/Event.html" title="class in com.google.adk.events"><code>Event</code></a>s.</div>
</dd>
<dt><a href="com/google/adk/tools/BaseTool.html" class="type-name-link" title="class in com.google.adk.tools">BaseTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">The base class for all ADK tools.</div>
</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">BaseTool(String, String)</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#%3Cinit%3E(java.lang.String,java.lang.String,boolean)" class="member-name-link">BaseTool(String, String, boolean)</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Basic.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Basic</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block"><code>RequestProcessor</code> that handles basic information to build the LLM request.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/Basic.html#%3Cinit%3E()" class="member-name-link">Basic()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Basic.html" title="class in com.google.adk.flows.llmflows">Basic</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#beforeAgentCallback()" class="member-name-link">beforeAgentCallback()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)" class="member-name-link">beforeAgentCallback(Callbacks.BeforeAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)" class="member-name-link">beforeAgentCallback(Callbacks.BeforeAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)" class="member-name-link">beforeAgentCallback(Callbacks.BeforeAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)" class="member-name-link">beforeAgentCallback(Callbacks.BeforeAgentCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#beforeAgentCallbackSync(com.google.adk.agents.Callbacks.BeforeAgentCallbackSync)" class="member-name-link">beforeAgentCallbackSync(Callbacks.BeforeAgentCallbackSync)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#beforeModelCallback()" class="member-name-link">beforeModelCallback()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#beforeModelCallback(com.google.adk.agents.Callbacks.BeforeModelCallback)" class="member-name-link">beforeModelCallback(Callbacks.BeforeModelCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#beforeModelCallbackSync(com.google.adk.agents.Callbacks.BeforeModelCallbackSync)" class="member-name-link">beforeModelCallbackSync(Callbacks.BeforeModelCallbackSync)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#beforeToolCallback()" class="member-name-link">beforeToolCallback()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#beforeToolCallback(com.google.adk.agents.Callbacks.BeforeToolCallback)" class="member-name-link">beforeToolCallback(Callbacks.BeforeToolCallback)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#beforeToolCallbackSync(com.google.adk.agents.Callbacks.BeforeToolCallbackSync)" class="member-name-link">beforeToolCallbackSync(Callbacks.BeforeToolCallbackSync)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.StreamingMode.html#BIDI" class="member-name-link">BIDI</a> - Enum constant in enum class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.StreamingMode.html" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html#blob()" class="member-name-link">blob()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></dt>
<dd>
<div class="block">Returns the blob of the request.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#blob(com.google.genai.types.Blob)" class="member-name-link">blob(Blob)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#blob(java.util.Optional)" class="member-name-link">blob(Optional&lt;Blob&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#branch()" class="member-name-link">branch()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#branch()" class="member-name-link">branch()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">The branch of the event.</div>
</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#branch(java.lang.String)" class="member-name-link">branch(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#branch(java.lang.String)" class="member-name-link">branch(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#branch(java.lang.String)" class="member-name-link">branch(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#branch(java.util.Optional)" class="member-name-link">branch(Optional&lt;String&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#branch(java.util.Optional)" class="member-name-link">branch(Optional&lt;String&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.examples.<a href="com/google/adk/examples/Example.Builder.html" title="class in com.google.adk.examples">Example.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Gemini.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></dt>
<dd>
<div class="block">Builds the <a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models"><code>Gemini</code></a> instance.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Model.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Model.Builder.html" title="class in com.google.adk.models">Model.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.Builder.html" title="class in com.google.adk.sessions">GetSessionConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.Builder.html" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.Builder.html" title="class in com.google.adk.sessions">ListSessionsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></dt>
<dd>
<div class="block">Builds a new <a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp"><code>SseServerParameters</code></a> instance.</div>
</dd>
<dt><a href="com/google/adk/tools/ToolContext.Builder.html#build()" class="member-name-link">build()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.Builder.html" title="class in com.google.adk.tools">ToolContext.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>
<div class="block">Returns a <a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents"><code>LlmAgent.Builder</code></a> for <a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/LoopAgent.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.html" title="class in com.google.adk.agents">LoopAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.html" title="class in com.google.adk.agents">ParallelAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.html" title="class in com.google.adk.agents">SequentialAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.html" title="class in com.google.adk.artifacts">ListArtifactsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.examples.<a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples">Example</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Gemini.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models">Gemini</a></dt>
<dd>
<div class="block">Returns a new Builder instance for constructing Gemini objects.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Model.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/Model.html" title="class in com.google.adk.models">Model</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.html#builder()" class="member-name-link">builder()</a> - Static method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></dt>
<dd>
<div class="block">Creates a new builder for <a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp"><code>SseServerParameters</code></a>.</div>
</dd>
<dt><a href="com/google/adk/tools/ToolContext.html#builder(com.google.adk.agents.InvocationContext)" class="member-name-link">builder(InvocationContext)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#builder(com.google.adk.agents.RunConfig)" class="member-name-link">builder(RunConfig)</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html#builder(java.lang.String)" class="member-name-link">builder(String)</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.examples.<a href="com/google/adk/examples/Example.Builder.html" title="class in com.google.adk.examples">Example.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Model.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/Model.Builder.html" title="class in com.google.adk.models">Model.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.Builder.html" title="class in com.google.adk.sessions">GetSessionConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.Builder.html" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.Builder.html" title="class in com.google.adk.sessions">ListSessionsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html#%3Cinit%3E()" class="member-name-link">Builder()</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#%3Cinit%3E(java.lang.String)" class="member-name-link">Builder(String)</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/ExampleUtils.html#buildExampleSi(com.google.adk.examples.BaseExampleProvider,java.lang.String)" class="member-name-link">buildExampleSi(BaseExampleProvider, String)</a> - Static method in class com.google.adk.examples.<a href="com/google/adk/examples/ExampleUtils.html" title="class in com.google.adk.examples">ExampleUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/FunctionCallingUtils.html#buildSchemaFromType(java.lang.reflect.Type)" class="member-name-link">buildSchemaFromType(Type)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/FunctionCallingUtils.html" title="class in com.google.adk.tools">FunctionCallingUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/BuiltInCodeExecutionTool.html" class="type-name-link" title="class in com.google.adk.tools">BuiltInCodeExecutionTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">A built-in code execution tool that is automatically invoked by Gemini 2 models.</div>
</dd>
<dt><a href="com/google/adk/tools/BuiltInCodeExecutionTool.html#%3Cinit%3E()" class="member-name-link">BuiltInCodeExecutionTool()</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/BuiltInCodeExecutionTool.html" title="class in com.google.adk.tools">BuiltInCodeExecutionTool</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:C">C</h2>
<dl class="index">
<dt><a href="com/google/adk/agents/Callbacks.AfterAgentCallback.html#call(com.google.adk.agents.CallbackContext)" class="member-name-link">call(CallbackContext)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterAgentCallbackSync.html#call(com.google.adk.agents.CallbackContext)" class="member-name-link">call(CallbackContext)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeAgentCallback.html#call(com.google.adk.agents.CallbackContext)" class="member-name-link">call(CallbackContext)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeAgentCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeAgentCallbackSync.html#call(com.google.adk.agents.CallbackContext)" class="member-name-link">call(CallbackContext)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeAgentCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeModelCallback.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)" class="member-name-link">call(CallbackContext, LlmRequest)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeModelCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeModelCallbackSync.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)" class="member-name-link">call(CallbackContext, LlmRequest)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterModelCallback.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)" class="member-name-link">call(CallbackContext, LlmResponse)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterModelCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterModelCallbackSync.html#call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)" class="member-name-link">call(CallbackContext, LlmResponse)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterModelCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeToolCallback.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">call(InvocationContext, BaseTool, Map&lt;String, Object&gt;, ToolContext)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeToolCallback.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeToolCallbackSync.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">call(InvocationContext, BaseTool, Map&lt;String, Object&gt;, ToolContext)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterToolCallback.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)" class="member-name-link">call(InvocationContext, BaseTool, Map&lt;String, Object&gt;, ToolContext, Object)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterToolCallback.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterToolCallbackSync.html#call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)" class="member-name-link">call(InvocationContext, BaseTool, Map&lt;String, Object&gt;, ToolContext, Object)</a> - Method in interface com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterToolCallbackSync.html" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/CallbackContext.html" class="type-name-link" title="class in com.google.adk.agents">CallbackContext</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">The context of various callbacks for an agent invocation.</div>
</dd>
<dt><a href="com/google/adk/agents/CallbackContext.html#%3Cinit%3E(com.google.adk.agents.InvocationContext,com.google.adk.events.EventActions)" class="member-name-link">CallbackContext(InvocationContext, EventActions)</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.html" class="type-name-link" title="class in com.google.adk.agents">Callbacks</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.html#%3Cinit%3E()" class="member-name-link">Callbacks()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.html" title="class in com.google.adk.agents">Callbacks</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterAgentCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterAgentCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterModelCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterModelCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterToolCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.AfterToolCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeAgentCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeAgentCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeModelCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeModelCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeToolCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/Callbacks.BeforeToolCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a> - Interface in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Claude.html" class="type-name-link" title="class in com.google.adk.models">Claude</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Represents the Claude Generative AI model by Anthropic.</div>
</dd>
<dt><a href="com/google/adk/models/Claude.html#%3Cinit%3E(java.lang.String,com.anthropic.client.AnthropicClient)" class="member-name-link">Claude(String, AnthropicClient)</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/Claude.html" title="class in com.google.adk.models">Claude</a></dt>
<dd>
<div class="block">Constructs a new Claude instance.</div>
</dd>
<dt><a href="com/google/adk/sessions/State.html#clear()" class="member-name-link">clear()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></dt>
<dd>
<div class="block">Returns whether the connection should be closed.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequestQueue.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html#close()" class="member-name-link">close()</a> - Method in interface com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html" title="interface in com.google.adk.flows.llmflows.audio">SpeechClientInterface</a></dt>
<dd>
<div class="block">Closes the client and releases any resources.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html" title="class in com.google.adk.flows.llmflows.audio">VertexSpeechClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlmConnection.html#close()" class="member-name-link">close()</a> - Method in interface com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></dt>
<dd>
<div class="block">Closes the connection.</div>
</dd>
<dt><a href="com/google/adk/models/GeminiLlmConnection.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/GeminiLlmConnection.html" title="class in com.google.adk.models">GeminiLlmConnection</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/network/ApiResponse.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.network.<a href="com/google/adk/network/ApiResponse.html" title="class in com.google.adk.network">ApiResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/network/HttpApiResponse.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.network.<a href="com/google/adk/network/HttpApiResponse.html" title="class in com.google.adk.network">HttpApiResponse</a></dt>
<dd>
<div class="block">Closes the Http response.</div>
</dd>
<dt><a href="com/google/adk/sessions/ApiResponse.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ApiResponse.html" title="class in com.google.adk.sessions">ApiResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/HttpApiResponse.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiResponse.html" title="class in com.google.adk.sessions">HttpApiResponse</a></dt>
<dd>
<div class="block">Closes the Http response.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#close()" class="member-name-link">close()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Closes the connection to MCP Server.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#close(java.lang.Boolean)" class="member-name-link">close(Boolean)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlmConnection.html#close(java.lang.Throwable)" class="member-name-link">close(Throwable)</a> - Method in interface com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></dt>
<dd>
<div class="block">Closes the connection with an error.</div>
</dd>
<dt><a href="com/google/adk/models/GeminiLlmConnection.html#close(java.lang.Throwable)" class="member-name-link">close(Throwable)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/GeminiLlmConnection.html" title="class in com.google.adk.models">GeminiLlmConnection</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#close(java.util.Optional)" class="member-name-link">close(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#closeSession(com.google.adk.sessions.Session)" class="member-name-link">closeSession(Session)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Closes a session.</div>
</dd>
<dt><a href="com/google/adk/CollectionUtils.html" class="type-name-link" title="class in com.google.adk">CollectionUtils</a> - Class in <a href="com/google/adk/package-summary.html">com.google.adk</a></dt>
<dd>
<div class="block">Frequently used code snippets for collections.</div>
</dd>
<dt><a href="com/google/adk/package-summary.html">com.google.adk</a> - package com.google.adk</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a> - package com.google.adk.agents</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a> - package com.google.adk.artifacts</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/package-summary.html">com.google.adk.events</a> - package com.google.adk.events</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/package-summary.html">com.google.adk.examples</a> - package com.google.adk.examples</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/exceptions/package-summary.html">com.google.adk.exceptions</a> - package com.google.adk.exceptions</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/package-summary.html">com.google.adk.flows</a> - package com.google.adk.flows</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a> - package com.google.adk.flows.llmflows</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/package-summary.html">com.google.adk.flows.llmflows.audio</a> - package com.google.adk.flows.llmflows.audio</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/package-summary.html">com.google.adk.models</a> - package com.google.adk.models</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/network/package-summary.html">com.google.adk.network</a> - package com.google.adk.network</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/package-summary.html">com.google.adk.runner</a> - package com.google.adk.runner</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a> - package com.google.adk.sessions</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a> - package com.google.adk.tools</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a> - package com.google.adk.tools.mcp</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/retrieval/package-summary.html">com.google.adk.tools.retrieval</a> - package com.google.adk.tools.retrieval</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/utils/package-summary.html">com.google.adk.utils</a> - package com.google.adk.utils</dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#config()" class="member-name-link">config()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#config()" class="member-name-link">config()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>
<div class="block">Returns the configuration for content generation.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#config(com.google.genai.types.GenerateContentConfig)" class="member-name-link">config(GenerateContentConfig)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlm.html#connect(com.google.adk.models.LlmRequest)" class="member-name-link">connect(LlmRequest)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></dt>
<dd>
<div class="block">Creates a live connection to the LLM.</div>
</dd>
<dt><a href="com/google/adk/models/Claude.html#connect(com.google.adk.models.LlmRequest)" class="member-name-link">connect(LlmRequest)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Claude.html" title="class in com.google.adk.models">Claude</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Gemini.html#connect(com.google.adk.models.LlmRequest)" class="member-name-link">connect(LlmRequest)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models">Gemini</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#containsKey(java.lang.Object)" class="member-name-link">containsKey(Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#containsValue(java.lang.Object)" class="member-name-link">containsValue(Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html#content()" class="member-name-link">content()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></dt>
<dd>
<div class="block">Returns the content of the request.</div>
</dd>
<dt><a href="com/google/adk/events/Event.html#content()" class="member-name-link">content()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#content()" class="member-name-link">content()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>
<div class="block">Returns the content of the first candidate in the response, if available.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#content(com.google.genai.types.Content)" class="member-name-link">content(Content)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequestQueue.html#content(com.google.genai.types.Content)" class="member-name-link">content(Content)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#content(com.google.genai.types.Content)" class="member-name-link">content(Content)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#content(com.google.genai.types.Content)" class="member-name-link">content(Content)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html#content(java.util.Optional)" class="member-name-link">content(Optional&lt;Content&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" title="class in com.google.adk.agents">LiveRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#content(java.util.Optional)" class="member-name-link">content(Optional&lt;Content&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#contents()" class="member-name-link">contents()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>
<div class="block">Returns the list of content sent to the LLM.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#contents(java.util.List)" class="member-name-link">contents(List&lt;Content&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Contents.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Contents</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block"><code>RequestProcessor</code> that populates content in request for LLM flows.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/Contents.html#%3Cinit%3E()" class="member-name-link">Contents()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Contents.html" title="class in com.google.adk.flows.llmflows">Contents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/ConversionUtils.html" class="type-name-link" title="class in com.google.adk.tools.mcp">ConversionUtils</a> - Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Utility class for converting between different representations of MCP tools.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#convertEventToJson(com.google.adk.events.Event)" class="member-name-link">convertEventToJson(Event)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#copyOf(com.google.adk.agents.InvocationContext)" class="member-name-link">copyOf(InvocationContext)</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/AgentTool.html#create(com.google.adk.agents.BaseAgent)" class="member-name-link">create(BaseAgent)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/AgentTool.html#create(com.google.adk.agents.BaseAgent,boolean)" class="member-name-link">create(BaseAgent, boolean)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">create(BaseSessionService, BaseArtifactService, BaseAgent, Session, LiveRequestQueue, RunConfig)</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,java.lang.String,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">create(BaseSessionService, BaseArtifactService, String, BaseAgent, Session, Content, RunConfig)</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#create(com.google.genai.types.GenerateContentResponse)" class="member-name-link">create(GenerateContentResponse)</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/FunctionTool.html#create(java.lang.Class,java.lang.String)" class="member-name-link">create(Class&lt;?&gt;, String)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/FunctionTool.html" title="class in com.google.adk.tools">FunctionTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/LongRunningFunctionTool.html#create(java.lang.Class,java.lang.String)" class="member-name-link">create(Class&lt;?&gt;, String)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/LongRunningFunctionTool.html" title="class in com.google.adk.tools">LongRunningFunctionTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/FunctionTool.html#create(java.lang.reflect.Method)" class="member-name-link">create(Method)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/FunctionTool.html" title="class in com.google.adk.tools">FunctionTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/LongRunningFunctionTool.html#create(java.lang.reflect.Method)" class="member-name-link">create(Method)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/LongRunningFunctionTool.html" title="class in com.google.adk.tools">LongRunningFunctionTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRegistry.LlmFactory.html#create(java.lang.String)" class="member-name-link">create(String)</a> - Method in interface com.google.adk.models.<a href="com/google/adk/models/LlmRegistry.LlmFactory.html" title="interface in com.google.adk.models">LlmRegistry.LlmFactory</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#create(java.util.List)" class="member-name-link">create(List&lt;Candidate&gt;)</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpSessionManager.html#createSession()" class="member-name-link">createSession()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpSessionManager.html" title="class in com.google.adk.tools.mcp">McpSessionManager</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#createSession(java.lang.String,java.lang.String)" class="member-name-link">createSession(String, String)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Creates a new session with the specified application name and user ID, using a default state
 (null) and allowing the service to generate a unique session ID.</div>
</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession(String, String, ConcurrentMap&lt;String, Object&gt;, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession(String, String, ConcurrentMap&lt;String, Object&gt;, String)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Creates a new session with the specified parameters.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)" class="member-name-link">createSession(String, String, ConcurrentMap&lt;String, Object&gt;, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.html#credentials()" class="member-name-link">credentials()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:D">D</h2>
<dl class="index">
<dt><a href="com/google/adk/tools/AgentTool.html#declaration()" class="member-name-link">declaration()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#declaration()" class="member-name-link">declaration()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>
<div class="block">Gets the <code>FunctionDeclaration</code> representation of this tool.</div>
</dd>
<dt><a href="com/google/adk/tools/FunctionTool.html#declaration()" class="member-name-link">declaration()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/FunctionTool.html" title="class in com.google.adk.tools">FunctionTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/LoadArtifactsTool.html#declaration()" class="member-name-link">declaration()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/LoadArtifactsTool.html" title="class in com.google.adk.tools">LoadArtifactsTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpTool.html#declaration()" class="member-name-link">declaration()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html#declaration()" class="member-name-link">declaration()</a> - Method in class com.google.adk.tools.retrieval.<a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html" title="class in com.google.adk.tools.retrieval">BaseRetrievalTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/SessionUtils.html#decodeContent(com.google.genai.types.Content)" class="member-name-link">decodeContent(Content)</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionUtils.html" title="class in com.google.adk.sessions">SessionUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.IncludeContents.html#DEFAULT" class="member-name-link">DEFAULT</a> - Enum constant in enum class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/BaseArtifactService.html#deleteArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteArtifact(String, String, String, String)</a> - Method in interface com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a></dt>
<dd>
<div class="block">Deletes an artifact.</div>
</dd>
<dt><a href="com/google/adk/artifacts/GcsArtifactService.html#deleteArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteArtifact(String, String, String, String)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/GcsArtifactService.html" title="class in com.google.adk.artifacts">GcsArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/InMemoryArtifactService.html#deleteArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteArtifact(String, String, String, String)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/InMemoryArtifactService.html" title="class in com.google.adk.artifacts">InMemoryArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#deleteSession(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteSession(String, String, String)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Deletes a specific session.</div>
</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html#deleteSession(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteSession(String, String, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#deleteSession(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">deleteSession(String, String, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#description()" class="member-name-link">description()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>
<div class="block">Gets the one-line description of the agent's capability.</div>
</dd>
<dt><a href="com/google/adk/tools/Annotations.Schema.html#description()" class="member-name-link">description()</a> - Element in annotation interface com.google.adk.tools.<a href="com/google/adk/tools/Annotations.Schema.html" title="annotation interface in com.google.adk.tools">Annotations.Schema</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#description()" class="member-name-link">description()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#description(java.lang.String)" class="member-name-link">description(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#description(java.lang.String)" class="member-name-link">description(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#description(java.lang.String)" class="member-name-link">description(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#description(java.lang.String)" class="member-name-link">description(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#disallowTransferToParent()" class="member-name-link">disallowTransferToParent()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#disallowTransferToParent(boolean)" class="member-name-link">disallowTransferToParent(boolean)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#disallowTransferToPeers()" class="member-name-link">disallowTransferToPeers()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#disallowTransferToPeers(boolean)" class="member-name-link">disallowTransferToPeers(boolean)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:E">E</h2>
<dl class="index">
<dt><a href="com/google/adk/sessions/SessionUtils.html#encodeContent(com.google.genai.types.Content)" class="member-name-link">encodeContent(Content)</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionUtils.html" title="class in com.google.adk.sessions">SessionUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#endInvocation()" class="member-name-link">endInvocation()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#entrySet()" class="member-name-link">entrySet()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#equals(java.lang.Object)" class="member-name-link">equals(Object)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#equals(java.lang.Object)" class="member-name-link">equals(Object)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#equals(java.lang.Object)" class="member-name-link">equals(Object)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#equals(java.lang.Object)" class="member-name-link">equals(Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#errorCode()" class="member-name-link">errorCode()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#errorCode()" class="member-name-link">errorCode()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>
<div class="block">Error code if the response is an error.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#errorCode(com.google.genai.types.FinishReason)" class="member-name-link">errorCode(FinishReason)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#errorCode(com.google.genai.types.FinishReason)" class="member-name-link">errorCode(FinishReason)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#errorCode(java.util.Optional)" class="member-name-link">errorCode(Optional&lt;FinishReason&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#errorCode(java.util.Optional)" class="member-name-link">errorCode(Optional&lt;FinishReason&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#errorMessage()" class="member-name-link">errorMessage()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#errorMessage()" class="member-name-link">errorMessage()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>
<div class="block">Error message if the response is an error.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#errorMessage(java.lang.String)" class="member-name-link">errorMessage(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#errorMessage(java.lang.String)" class="member-name-link">errorMessage(String)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#errorMessage(java.util.Optional)" class="member-name-link">errorMessage(Optional&lt;String&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#errorMessage(java.util.Optional)" class="member-name-link">errorMessage(Optional&lt;String&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#escalate()" class="member-name-link">escalate()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#escalate(boolean)" class="member-name-link">escalate(boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html" class="type-name-link" title="class in com.google.adk.events">Event</a> - Class in <a href="com/google/adk/events/package-summary.html">com.google.adk.events</a></dt>
<dd>
<div class="block">Represents an event in a session.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html" class="type-name-link" title="class in com.google.adk.events">Event.Builder</a> - Class in <a href="com/google/adk/events/package-summary.html">com.google.adk.events</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/events/Event.html" title="class in com.google.adk.events"><code>Event</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/CallbackContext.html#eventActions" class="member-name-link">eventActions</a> - Variable in class com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/CallbackContext.html#eventActions()" class="member-name-link">eventActions()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></dt>
<dd>
<div class="block">Returns the EventActions associated with this context.</div>
</dd>
<dt><a href="com/google/adk/events/EventActions.html" class="type-name-link" title="class in com.google.adk.events">EventActions</a> - Class in <a href="com/google/adk/events/package-summary.html">com.google.adk.events</a></dt>
<dd>
<div class="block">Represents the actions attached to an event.</div>
</dd>
<dt><a href="com/google/adk/events/EventActions.html#%3Cinit%3E()" class="member-name-link">EventActions()</a> - Constructor for class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>
<div class="block">Default constructor for Jackson.</div>
</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html" class="type-name-link" title="class in com.google.adk.events">EventActions.Builder</a> - Class in <a href="com/google/adk/events/package-summary.html">com.google.adk.events</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events"><code>EventActions</code></a>.</div>
</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.html#events()" class="member-name-link">events()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html#events()" class="member-name-link">events()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.Builder.html#events(java.util.List)" class="member-name-link">events(List&lt;Event&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.Builder.html" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#events(java.util.List)" class="member-name-link">events(List&lt;Event&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventStream.html" class="type-name-link" title="class in com.google.adk.events">EventStream</a> - Class in <a href="com/google/adk/events/package-summary.html">com.google.adk.events</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventStream.html#%3Cinit%3E(java.util.function.Supplier)" class="member-name-link">EventStream(Supplier&lt;Event&gt;)</a> - Constructor for class com.google.adk.events.<a href="com/google/adk/events/EventStream.html" title="class in com.google.adk.events">EventStream</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.html" class="type-name-link" title="class in com.google.adk.examples">Example</a> - Class in <a href="com/google/adk/examples/package-summary.html">com.google.adk.examples</a></dt>
<dd>
<div class="block">Represents an few-shot example.</div>
</dd>
<dt><a href="com/google/adk/examples/Example.html#%3Cinit%3E()" class="member-name-link">Example()</a> - Constructor for class com.google.adk.examples.<a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples">Example</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.Builder.html" class="type-name-link" title="class in com.google.adk.examples">Example.Builder</a> - Class in <a href="com/google/adk/examples/package-summary.html">com.google.adk.examples</a></dt>
<dd>
<div class="block">Builder for constructing <a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples"><code>Example</code></a> instances.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#exampleProvider()" class="member-name-link">exampleProvider()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#exampleProvider(com.google.adk.examples.BaseExampleProvider)" class="member-name-link">exampleProvider(BaseExampleProvider)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#exampleProvider(com.google.adk.examples.Example...)" class="member-name-link">exampleProvider(Example...)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#exampleProvider(java.util.List)" class="member-name-link">exampleProvider(List&lt;Example&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Examples.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Examples</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block"><code>RequestProcessor</code> that populates examples in LLM request.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/Examples.html#%3Cinit%3E()" class="member-name-link">Examples()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Examples.html" title="class in com.google.adk.flows.llmflows">Examples</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/ExampleUtils.html" class="type-name-link" title="class in com.google.adk.examples">ExampleUtils</a> - Class in <a href="com/google/adk/examples/package-summary.html">com.google.adk.examples</a></dt>
<dd>
<div class="block">Utility class for examples.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#executor()" class="member-name-link">executor()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#executor(java.util.concurrent.Executor)" class="member-name-link">executor(Executor)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ExitLoopTool.html#exitLoop(com.google.adk.tools.ToolContext)" class="member-name-link">exitLoop(ToolContext)</a> - Static method in class com.google.adk.tools.<a href="com/google/adk/tools/ExitLoopTool.html" title="class in com.google.adk.tools">ExitLoopTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ExitLoopTool.html" class="type-name-link" title="class in com.google.adk.tools">ExitLoopTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">Exits the loop.</div>
</dd>
</dl>
<h2 class="title" id="I:F">F</h2>
<dl class="index">
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.html#filenames()" class="member-name-link">filenames()</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.html" title="class in com.google.adk.artifacts">ListArtifactsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html#filenames(java.util.List)" class="member-name-link">filenames(List&lt;String&gt;)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#finalResponse()" class="member-name-link">finalResponse()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#findAgent(java.lang.String)" class="member-name-link">findAgent(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>
<div class="block">Finds an agent (this or descendant) by name.</div>
</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#findSubAgent(java.lang.String)" class="member-name-link">findSubAgent(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>
<div class="block">Recursively search sub agent by name.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#fromApiEvent(java.util.Map)" class="member-name-link">fromApiEvent(Map&lt;String, Object&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#fromJson(java.lang.String)" class="member-name-link">fromJson(String)</a> - Static method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html#fromJson(java.lang.String)" class="member-name-link">fromJson(String)</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/JsonBaseModel.html#fromJsonNode(com.fasterxml.jackson.databind.JsonNode,java.lang.Class)" class="member-name-link">fromJsonNode(JsonNode, Class&lt;T&gt;)</a> - Static method in class com.google.adk.<a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></dt>
<dd>
<div class="block">Deserializes a JsonNode to an object of the given type.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html#fromJsonString(java.lang.String)" class="member-name-link">fromJsonString(String)</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></dt>
<dd>
<div class="block">Deserializes a Json string to a <a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents"><code>LiveRequest</code></a> object.</div>
</dd>
<dt><a href="com/google/adk/JsonBaseModel.html#fromJsonString(java.lang.String,java.lang.Class)" class="member-name-link">fromJsonString(String, Class&lt;T&gt;)</a> - Static method in class com.google.adk.<a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></dt>
<dd>
<div class="block">Deserializes a Json string to an object of the given type.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#fromServer(com.google.adk.tools.mcp.SseServerParameters)" class="member-name-link">fromServer(SseServerParameters)</a> - Static method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Retrieve all tools from the MCP connection using SSE server parameters and the ObjectMapper
 used across the ADK.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#fromServer(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">fromServer(SseServerParameters, ObjectMapper)</a> - Static method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Retrieve all tools from the MCP connection using SSE server parameters.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#fromServer(io.modelcontextprotocol.client.transport.ServerParameters)" class="member-name-link">fromServer(ServerParameters)</a> - Static method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Retrieve all tools from the MCP connection using local server parameters and the ObjectMapper
 used across the ADK.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#fromServer(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">fromServer(ServerParameters, ObjectMapper)</a> - Static method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Retrieve all tools from the MCP connection using local server parameters.</div>
</dd>
<dt><a href="com/google/adk/tools/ToolContext.html#functionCallId()" class="member-name-link">functionCallId()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ToolContext.Builder.html#functionCallId(java.lang.String)" class="member-name-link">functionCallId(String)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.Builder.html" title="class in com.google.adk.tools">ToolContext.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ToolContext.html#functionCallId(java.lang.String)" class="member-name-link">functionCallId(String)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/FunctionCallingUtils.html" class="type-name-link" title="class in com.google.adk.tools">FunctionCallingUtils</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">Utility class for function calling.</div>
</dd>
<dt><a href="com/google/adk/tools/FunctionCallingUtils.html#%3Cinit%3E()" class="member-name-link">FunctionCallingUtils()</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/FunctionCallingUtils.html" title="class in com.google.adk.tools">FunctionCallingUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#functionCalls()" class="member-name-link">functionCalls()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#functionResponses()" class="member-name-link">functionResponses()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Functions.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Functions</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block">Utility class for handling function calls.</div>
</dd>
<dt><a href="com/google/adk/tools/FunctionTool.html" class="type-name-link" title="class in com.google.adk.tools">FunctionTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">FunctionTool implements a customized function calling tool.</div>
</dd>
<dt><a href="com/google/adk/tools/FunctionTool.html#%3Cinit%3E(java.lang.reflect.Method,boolean)" class="member-name-link">FunctionTool(Method, boolean)</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/FunctionTool.html" title="class in com.google.adk.tools">FunctionTool</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:G">G</h2>
<dl class="index">
<dt><a href="com/google/adk/artifacts/GcsArtifactService.html" class="type-name-link" title="class in com.google.adk.artifacts">GcsArtifactService</a> - Class in <a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a></dt>
<dd>
<div class="block">An artifact service implementation using Google Cloud Storage (GCS).</div>
</dd>
<dt><a href="com/google/adk/artifacts/GcsArtifactService.html#%3Cinit%3E(java.lang.String,com.google.cloud.storage.Storage)" class="member-name-link">GcsArtifactService(String, Storage)</a> - Constructor for class com.google.adk.artifacts.<a href="com/google/adk/artifacts/GcsArtifactService.html" title="class in com.google.adk.artifacts">GcsArtifactService</a></dt>
<dd>
<div class="block">Initializes the GcsArtifactService.</div>
</dd>
<dt><a href="com/google/adk/models/Gemini.html" class="type-name-link" title="class in com.google.adk.models">Gemini</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Represents the Gemini Generative AI model.</div>
</dd>
<dt><a href="com/google/adk/models/Gemini.html#%3Cinit%3E(java.lang.String,com.google.adk.models.VertexCredentials)" class="member-name-link">Gemini(String, VertexCredentials)</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models">Gemini</a></dt>
<dd>
<div class="block">Constructs a new Gemini instance with a Google Gemini API key.</div>
</dd>
<dt><a href="com/google/adk/models/Gemini.html#%3Cinit%3E(java.lang.String,com.google.genai.Client)" class="member-name-link">Gemini(String, Client)</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models">Gemini</a></dt>
<dd>
<div class="block">Constructs a new Gemini instance.</div>
</dd>
<dt><a href="com/google/adk/models/Gemini.html#%3Cinit%3E(java.lang.String,java.lang.String)" class="member-name-link">Gemini(String, String)</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models">Gemini</a></dt>
<dd>
<div class="block">Constructs a new Gemini instance with a Google Gemini API key.</div>
</dd>
<dt><a href="com/google/adk/models/Gemini.Builder.html" class="type-name-link" title="class in com.google.adk.models">Gemini.Builder</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models"><code>Gemini</code></a>.</div>
</dd>
<dt><a href="com/google/adk/models/GeminiLlmConnection.html" class="type-name-link" title="class in com.google.adk.models">GeminiLlmConnection</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Manages a persistent, bidirectional connection to the Gemini model via WebSockets for real-time
 interaction.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/Functions.html#generateClientFunctionCallId()" class="member-name-link">generateClientFunctionCallId()</a> - Static method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Functions.html" title="class in com.google.adk.flows.llmflows">Functions</a></dt>
<dd>
<div class="block">Generates a unique ID for a function call.</div>
</dd>
<dt><a href="com/google/adk/models/BaseLlm.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent(LlmRequest, boolean)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></dt>
<dd>
<div class="block">Generates one content from the given LLM request and tools.</div>
</dd>
<dt><a href="com/google/adk/models/Claude.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent(LlmRequest, boolean)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Claude.html" title="class in com.google.adk.models">Claude</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Gemini.html#generateContent(com.google.adk.models.LlmRequest,boolean)" class="member-name-link">generateContent(LlmRequest, boolean)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.html" title="class in com.google.adk.models">Gemini</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#generateContentConfig()" class="member-name-link">generateContentConfig()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#generateContentConfig(com.google.genai.types.GenerateContentConfig)" class="member-name-link">generateContentConfig(GenerateContentConfig)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#generateEventId()" class="member-name-link">generateEventId()</a> - Static method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequestQueue.html#get()" class="member-name-link">get()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#get(java.lang.Object)" class="member-name-link">get(Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/network/ApiResponse.html#getEntity()" class="member-name-link">getEntity()</a> - Method in class com.google.adk.network.<a href="com/google/adk/network/ApiResponse.html" title="class in com.google.adk.network">ApiResponse</a></dt>
<dd>
<div class="block">Gets the ResponseBody.</div>
</dd>
<dt><a href="com/google/adk/network/HttpApiResponse.html#getEntity()" class="member-name-link">getEntity()</a> - Method in class com.google.adk.network.<a href="com/google/adk/network/HttpApiResponse.html" title="class in com.google.adk.network">HttpApiResponse</a></dt>
<dd>
<div class="block">Returns the ResponseBody from the response.</div>
</dd>
<dt><a href="com/google/adk/examples/BaseExampleProvider.html#getExamples(java.lang.String)" class="member-name-link">getExamples(String)</a> - Method in interface com.google.adk.examples.<a href="com/google/adk/examples/BaseExampleProvider.html" title="interface in com.google.adk.examples">BaseExampleProvider</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#getFirstSystemInstruction()" class="member-name-link">getFirstSystemInstruction()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>
<div class="block">returns the first system instruction text from the request if present.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#getJsonResponse(com.google.adk.sessions.ApiResponse)" class="member-name-link">getJsonResponse(ApiResponse)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html#getLastUpdateTimeAsDouble()" class="member-name-link">getLastUpdateTimeAsDouble()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRegistry.html#getLlm(java.lang.String)" class="member-name-link">getLlm(String)</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/LlmRegistry.html" title="class in com.google.adk.models">LlmRegistry</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Functions.html#getLongRunningFunctionCalls(java.util.List,java.util.Map)" class="member-name-link">getLongRunningFunctionCalls(List&lt;FunctionCall&gt;, Map&lt;String, BaseTool&gt;)</a> - Static method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Functions.html" title="class in com.google.adk.flows.llmflows">Functions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/JsonBaseModel.html#getMapper()" class="member-name-link">getMapper()</a> - Static method in class com.google.adk.<a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ApiResponse.html#getResponseBody()" class="member-name-link">getResponseBody()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ApiResponse.html" title="class in com.google.adk.sessions">ApiResponse</a></dt>
<dd>
<div class="block">Gets the HttpEntity.</div>
</dd>
<dt><a href="com/google/adk/sessions/HttpApiResponse.html#getResponseBody()" class="member-name-link">getResponseBody()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiResponse.html" title="class in com.google.adk.sessions">HttpApiResponse</a></dt>
<dd>
<div class="block">Returns the HttpEntity from the response.</div>
</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession(String, String, String, Optional&lt;GetSessionConfig&gt;)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Retrieves a specific session, optionally filtering the events included.</div>
</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession(String, String, String, Optional&lt;GetSessionConfig&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">getSession(String, String, String, Optional&lt;GetSessionConfig&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.html" class="type-name-link" title="class in com.google.adk.sessions">GetSessionConfig</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Configuration for getting a session.</div>
</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.html#%3Cinit%3E()" class="member-name-link">GetSessionConfig()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">GetSessionConfig.Builder</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/sessions/GetSessionConfig.html" title="class in com.google.adk.sessions"><code>GetSessionConfig</code></a>.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#getSystemInstructions()" class="member-name-link">getSystemInstructions()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>
<div class="block">Returns all system instruction texts from the request as an immutable list.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html#getTools()" class="member-name-link">getTools()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html#getToolset()" class="member-name-link">getToolset()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/Telemetry.html#getTracer()" class="member-name-link">getTracer()</a> - Static method in class com.google.adk.<a href="com/google/adk/Telemetry.html" title="class in com.google.adk">Telemetry</a></dt>
<dd>
<div class="block">Gets the tracer.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#globalInstruction()" class="member-name-link">globalInstruction()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#globalInstruction(java.lang.String)" class="member-name-link">globalInstruction(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/GoogleSearchTool.html" class="type-name-link" title="class in com.google.adk.tools">GoogleSearchTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">A built-in tool that is automatically invoked by Gemini 2 models to retrieve search results from
 Google Search.</div>
</dd>
<dt><a href="com/google/adk/tools/GoogleSearchTool.html#%3Cinit%3E()" class="member-name-link">GoogleSearchTool()</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/GoogleSearchTool.html" title="class in com.google.adk.tools">GoogleSearchTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#groundingMetadata()" class="member-name-link">groundingMetadata()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">The grounding metadata of the event.</div>
</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#groundingMetadata()" class="member-name-link">groundingMetadata()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>
<div class="block">Returns the grounding metadata of the first candidate in the response, if available.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#groundingMetadata(com.google.genai.types.GroundingMetadata)" class="member-name-link">groundingMetadata(GroundingMetadata)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#groundingMetadata(com.google.genai.types.GroundingMetadata)" class="member-name-link">groundingMetadata(GroundingMetadata)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#groundingMetadata(java.util.Optional)" class="member-name-link">groundingMetadata(Optional&lt;GroundingMetadata&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#groundingMetadata(java.util.Optional)" class="member-name-link">groundingMetadata(Optional&lt;GroundingMetadata&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:H">H</h2>
<dl class="index">
<dt><a href="com/google/adk/flows/llmflows/Functions.html#handleFunctionCalls(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,java.util.Map)" class="member-name-link">handleFunctionCalls(InvocationContext, Event, Map&lt;String, BaseTool&gt;)</a> - Static method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Functions.html" title="class in com.google.adk.flows.llmflows">Functions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#hashCode()" class="member-name-link">hashCode()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#hashCode()" class="member-name-link">hashCode()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#hashCode()" class="member-name-link">hashCode()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#hashCode()" class="member-name-link">hashCode()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.html#headers()" class="member-name-link">headers()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></dt>
<dd>
<div class="block">Optional headers to include in the SSE connection request.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html#headers(java.util.Map)" class="member-name-link">headers(Map&lt;String, Object&gt;)</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></dt>
<dd>
<div class="block">Sets the headers for the SSE connection request.</div>
</dd>
<dt><a href="com/google/adk/sessions/HttpApiClient.html" class="type-name-link" title="class in com.google.adk.sessions">HttpApiClient</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Base client for the HTTP APIs.</div>
</dd>
<dt><a href="com/google/adk/network/HttpApiResponse.html" class="type-name-link" title="class in com.google.adk.network">HttpApiResponse</a> - Class in <a href="com/google/adk/network/package-summary.html">com.google.adk.network</a></dt>
<dd>
<div class="block">Wraps a real HTTP response to expose the methods needed by the GenAI SDK.</div>
</dd>
<dt><a href="com/google/adk/sessions/HttpApiResponse.html" class="type-name-link" title="class in com.google.adk.sessions">HttpApiResponse</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Wraps a real HTTP response to expose the methods needed by the GenAI SDK.</div>
</dd>
<dt><a href="com/google/adk/network/HttpApiResponse.html#%3Cinit%3E(okhttp3.Response)" class="member-name-link">HttpApiResponse(Response)</a> - Constructor for class com.google.adk.network.<a href="com/google/adk/network/HttpApiResponse.html" title="class in com.google.adk.network">HttpApiResponse</a></dt>
<dd>
<div class="block">Constructs a HttpApiResponse instance with the response.</div>
</dd>
<dt><a href="com/google/adk/sessions/HttpApiResponse.html#%3Cinit%3E(okhttp3.Response)" class="member-name-link">HttpApiResponse(Response)</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiResponse.html" title="class in com.google.adk.sessions">HttpApiResponse</a></dt>
<dd>
<div class="block">Constructs a HttpApiResponse instance with the response.</div>
</dd>
</dl>
<h2 class="title" id="I:I">I</h2>
<dl class="index">
<dt><a href="com/google/adk/events/Event.html#id()" class="member-name-link">id()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">The event id.</div>
</dd>
<dt><a href="com/google/adk/sessions/Session.html#id()" class="member-name-link">id()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#id(java.lang.String)" class="member-name-link">id(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#id(java.lang.String)" class="member-name-link">id(String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Identity.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Identity</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block"><code>RequestProcessor</code> that gives the agent identity from the framework</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/Identity.html#%3Cinit%3E()" class="member-name-link">Identity()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Identity.html" title="class in com.google.adk.flows.llmflows">Identity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#includeContents()" class="member-name-link">includeContents()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#includeContents(com.google.adk.agents.LlmAgent.IncludeContents)" class="member-name-link">includeContents(LlmAgent.IncludeContents)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#incrementLlmCallsCount()" class="member-name-link">incrementLlmCallsCount()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpSessionManager.html#initializeSession(java.lang.Object)" class="member-name-link">initializeSession(Object)</a> - Static method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpSessionManager.html" title="class in com.google.adk.tools.mcp">McpSessionManager</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/InMemoryArtifactService.html" class="type-name-link" title="class in com.google.adk.artifacts">InMemoryArtifactService</a> - Class in <a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a></dt>
<dd>
<div class="block">An in-memory implementation of the <a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts"><code>BaseArtifactService</code></a>.</div>
</dd>
<dt><a href="com/google/adk/artifacts/InMemoryArtifactService.html#%3Cinit%3E()" class="member-name-link">InMemoryArtifactService()</a> - Constructor for class com.google.adk.artifacts.<a href="com/google/adk/artifacts/InMemoryArtifactService.html" title="class in com.google.adk.artifacts">InMemoryArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/InMemoryRunner.html" class="type-name-link" title="class in com.google.adk.runner">InMemoryRunner</a> - Class in <a href="com/google/adk/runner/package-summary.html">com.google.adk.runner</a></dt>
<dd>
<div class="block">The class for the in-memory GenAi runner, using in-memory artifact and session services.</div>
</dd>
<dt><a href="com/google/adk/runner/InMemoryRunner.html#%3Cinit%3E(com.google.adk.agents.BaseAgent)" class="member-name-link">InMemoryRunner(BaseAgent)</a> - Constructor for class com.google.adk.runner.<a href="com/google/adk/runner/InMemoryRunner.html" title="class in com.google.adk.runner">InMemoryRunner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/InMemoryRunner.html#%3Cinit%3E(com.google.adk.agents.BaseAgent,java.lang.String)" class="member-name-link">InMemoryRunner(BaseAgent, String)</a> - Constructor for class com.google.adk.runner.<a href="com/google/adk/runner/InMemoryRunner.html" title="class in com.google.adk.runner">InMemoryRunner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html" class="type-name-link" title="class in com.google.adk.sessions">InMemorySessionService</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">An in-memory implementation of <a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions"><code>BaseSessionService</code></a> assuming <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> objects are
 mutable regarding their state map, events list, and last update time.</div>
</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html#%3Cinit%3E()" class="member-name-link">InMemorySessionService()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></dt>
<dd>
<div class="block">Creates a new instance of the in-memory session service with empty storage.</div>
</dd>
<dt><a href="com/google/adk/examples/Example.html#input()" class="member-name-link">input()</a> - Method in class com.google.adk.examples.<a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples">Example</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.Builder.html#input(com.google.genai.types.Content)" class="member-name-link">input(Content)</a> - Method in class com.google.adk.examples.<a href="com/google/adk/examples/Example.Builder.html" title="class in com.google.adk.examples">Example.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#inputSchema()" class="member-name-link">inputSchema()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#inputSchema(com.google.genai.types.Schema)" class="member-name-link">inputSchema(Schema)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#instruction()" class="member-name-link">instruction()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#instruction(java.lang.String)" class="member-name-link">instruction(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Instructions.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Instructions</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>
<div class="block"><code>RequestProcessor</code> that handles instructions and global instructions for LLM flows.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/Instructions.html#%3Cinit%3E()" class="member-name-link">Instructions()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Instructions.html" title="class in com.google.adk.flows.llmflows">Instructions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#interrupted()" class="member-name-link">interrupted()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#interrupted()" class="member-name-link">interrupted()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>
<div class="block">Indicates that LLM was interrupted when generating the content.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#interrupted(java.lang.Boolean)" class="member-name-link">interrupted(Boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#interrupted(java.lang.Boolean)" class="member-name-link">interrupted(Boolean)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#interrupted(java.util.Optional)" class="member-name-link">interrupted(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#interrupted(java.util.Optional)" class="member-name-link">interrupted(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ReadonlyContext.html#invocationContext" class="member-name-link">invocationContext</a> - Variable in class com.google.adk.agents.<a href="com/google/adk/agents/ReadonlyContext.html" title="class in com.google.adk.agents">ReadonlyContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html" class="type-name-link" title="class in com.google.adk.agents">InvocationContext</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">The context for an agent invocation.</div>
</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#invocationId()" class="member-name-link">invocationId()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ReadonlyContext.html#invocationId()" class="member-name-link">invocationId()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ReadonlyContext.html" title="class in com.google.adk.agents">ReadonlyContext</a></dt>
<dd>
<div class="block">Returns the ID of the current invocation.</div>
</dd>
<dt><a href="com/google/adk/events/Event.html#invocationId()" class="member-name-link">invocationId()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">Id of the invocation that this event belongs to.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#invocationId(java.lang.String)" class="member-name-link">invocationId(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#isEmpty()" class="member-name-link">isEmpty()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/CollectionUtils.html#isNullOrEmpty(java.lang.Iterable)" class="member-name-link">isNullOrEmpty(Iterable&lt;T&gt;)</a> - Static method in class com.google.adk.<a href="com/google/adk/CollectionUtils.html" title="class in com.google.adk">CollectionUtils</a></dt>
<dd>
<div class="block">Checks if the given iterable is null or empty.</div>
</dd>
<dt><a href="com/google/adk/events/EventStream.html#iterator()" class="member-name-link">iterator()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventStream.html" title="class in com.google.adk.events">EventStream</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:J">J</h2>
<dl class="index">
<dt><a href="com/google/adk/Version.html#JAVA_ADK_VERSION" class="member-name-link">JAVA_ADK_VERSION</a> - Static variable in class com.google.adk.<a href="com/google/adk/Version.html" title="class in com.google.adk">Version</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/JsonBaseModel.html" class="type-name-link" title="class in com.google.adk">JsonBaseModel</a> - Class in <a href="com/google/adk/package-summary.html">com.google.adk</a></dt>
<dd>
<div class="block">The base class for the types that needs JSON serialization/deserialization capability.</div>
</dd>
<dt><a href="com/google/adk/JsonBaseModel.html#%3Cinit%3E()" class="member-name-link">JsonBaseModel()</a> - Constructor for class com.google.adk.<a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:K">K</h2>
<dl class="index">
<dt><a href="com/google/adk/sessions/State.html#keySet()" class="member-name-link">keySet()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:L">L</h2>
<dl class="index">
<dt><a href="com/google/adk/sessions/Session.html#lastUpdateTime()" class="member-name-link">lastUpdateTime()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#lastUpdateTime(java.time.Instant)" class="member-name-link">lastUpdateTime(Instant)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html#lastUpdateTime(java.time.Instant)" class="member-name-link">lastUpdateTime(Instant)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#lastUpdateTimeSeconds(double)" class="member-name-link">lastUpdateTimeSeconds(double)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/BaseArtifactService.html#listArtifactKeys(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listArtifactKeys(String, String, String)</a> - Method in interface com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a></dt>
<dd>
<div class="block">Lists all the artifact filenames within a session.</div>
</dd>
<dt><a href="com/google/adk/artifacts/GcsArtifactService.html#listArtifactKeys(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listArtifactKeys(String, String, String)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/GcsArtifactService.html" title="class in com.google.adk.artifacts">GcsArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/InMemoryArtifactService.html#listArtifactKeys(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listArtifactKeys(String, String, String)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/InMemoryArtifactService.html" title="class in com.google.adk.artifacts">InMemoryArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ToolContext.html#listArtifacts()" class="member-name-link">listArtifacts()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></dt>
<dd>
<div class="block">Lists the filenames of the artifacts attached to the current session.</div>
</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactsResponse</a> - Class in <a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a></dt>
<dd>
<div class="block">Response for listing artifacts.</div>
</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.html#%3Cinit%3E()" class="member-name-link">ListArtifactsResponse()</a> - Constructor for class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.html" title="class in com.google.adk.artifacts">ListArtifactsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactsResponse.Builder</a> - Class in <a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/artifacts/ListArtifactsResponse.html" title="class in com.google.adk.artifacts"><code>ListArtifactsResponse</code></a>.</div>
</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse</a> - Class in <a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a></dt>
<dd>
<div class="block">Response for listing artifact versions.</div>
</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html#%3Cinit%3E()" class="member-name-link">ListArtifactVersionsResponse()</a> - Constructor for class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse.Builder</a> - Class in <a href="com/google/adk/artifacts/package-summary.html">com.google.adk.artifacts</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" title="class in com.google.adk.artifacts"><code>ListArtifactVersionsResponse</code></a>.</div>
</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#listEvents(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listEvents(String, String, String)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Lists the events within a specific session.</div>
</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html#listEvents(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listEvents(String, String, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#listEvents(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listEvents(String, String, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.html" class="type-name-link" title="class in com.google.adk.sessions">ListEventsResponse</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Response for listing events.</div>
</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.html#%3Cinit%3E()" class="member-name-link">ListEventsResponse()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/sessions/ListEventsResponse.html" title="class in com.google.adk.sessions"><code>ListEventsResponse</code></a>.</div>
</dd>
<dt><a href="com/google/adk/sessions/BaseSessionService.html#listSessions(java.lang.String,java.lang.String)" class="member-name-link">listSessions(String, String)</a> - Method in interface com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a></dt>
<dd>
<div class="block">Lists sessions associated with a specific application and user.</div>
</dd>
<dt><a href="com/google/adk/sessions/InMemorySessionService.html#listSessions(java.lang.String,java.lang.String)" class="member-name-link">listSessions(String, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" title="class in com.google.adk.sessions">InMemorySessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#listSessions(java.lang.String,java.lang.String)" class="member-name-link">listSessions(String, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.html" class="type-name-link" title="class in com.google.adk.sessions">ListSessionsResponse</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Response for listing sessions.</div>
</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.html#%3Cinit%3E()" class="member-name-link">ListSessionsResponse()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">ListSessionsResponse.Builder</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/sessions/ListSessionsResponse.html" title="class in com.google.adk.sessions"><code>ListSessionsResponse</code></a>.</div>
</dd>
<dt><a href="com/google/adk/artifacts/BaseArtifactService.html#listVersions(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listVersions(String, String, String, String)</a> - Method in interface com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a></dt>
<dd>
<div class="block">Lists all the versions (as revision IDs) of an artifact.</div>
</dd>
<dt><a href="com/google/adk/artifacts/GcsArtifactService.html#listVersions(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listVersions(String, String, String, String)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/GcsArtifactService.html" title="class in com.google.adk.artifacts">GcsArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/InMemoryArtifactService.html#listVersions(java.lang.String,java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">listVersions(String, String, String, String)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/InMemoryArtifactService.html" title="class in com.google.adk.artifacts">InMemoryArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#liveConnectConfig()" class="member-name-link">liveConnectConfig()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>
<div class="block">Returns the configuration for live connections.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#liveConnectConfig(com.google.genai.types.LiveConnectConfig)" class="member-name-link">liveConnectConfig(LiveConnectConfig)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequest</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Represents a request to be sent to a live connection to the LLM model.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequest.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequest.Builder</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Builder for constructing <a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents"><code>LiveRequest</code></a> instances.</div>
</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#liveRequestQueue()" class="member-name-link">liveRequestQueue()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequestQueue.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequestQueue</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">A queue of live requests to be sent to the model.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequestQueue.html#%3Cinit%3E()" class="member-name-link">LiveRequestQueue()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">The LLM-based agent.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent.Builder</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents"><code>LlmAgent</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.IncludeContents.html" class="type-name-link" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a> - Enum Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Enum to define if contents of previous events should be included in requests to the underlying
 LLM.</div>
</dd>
<dt><a href="com/google/adk/exceptions/LlmCallsLimitExceededException.html" class="type-name-link" title="class in com.google.adk.exceptions">LlmCallsLimitExceededException</a> - Exception Class in <a href="com/google/adk/exceptions/package-summary.html">com.google.adk.exceptions</a></dt>
<dd>
<div class="block">An error indicating that the limit for calls to the LLM has been exceeded.</div>
</dd>
<dt><a href="com/google/adk/exceptions/LlmCallsLimitExceededException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">LlmCallsLimitExceededException(String)</a> - Constructor for exception class com.google.adk.exceptions.<a href="com/google/adk/exceptions/LlmCallsLimitExceededException.html" title="class in com.google.adk.exceptions">LlmCallsLimitExceededException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRegistry.html" class="type-name-link" title="class in com.google.adk.models">LlmRegistry</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRegistry.html#%3Cinit%3E()" class="member-name-link">LlmRegistry()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/LlmRegistry.html" title="class in com.google.adk.models">LlmRegistry</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRegistry.LlmFactory.html" class="type-name-link" title="interface in com.google.adk.models">LlmRegistry.LlmFactory</a> - Interface in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">The factory interface for creating LLM instances.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.html" class="type-name-link" title="class in com.google.adk.models">LlmRequest</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Represents a request to be sent to the LLM.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#%3Cinit%3E()" class="member-name-link">LlmRequest()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html" class="type-name-link" title="class in com.google.adk.models">LlmRequest.Builder</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Builder for constructing <a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models"><code>LlmRequest</code></a> instances.</div>
</dd>
<dt><a href="com/google/adk/models/LlmResponse.html" class="type-name-link" title="class in com.google.adk.models">LlmResponse</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Represents a response received from the LLM.</div>
</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html" class="type-name-link" title="class in com.google.adk.models">LlmResponse.Builder</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Builder for constructing <a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models"><code>LlmResponse</code></a> instances.</div>
</dd>
<dt><a href="com/google/adk/artifacts/BaseArtifactService.html#loadArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">loadArtifact(String, String, String, String, Optional&lt;Integer&gt;)</a> - Method in interface com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a></dt>
<dd>
<div class="block">Gets an artifact.</div>
</dd>
<dt><a href="com/google/adk/artifacts/GcsArtifactService.html#loadArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">loadArtifact(String, String, String, String, Optional&lt;Integer&gt;)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/GcsArtifactService.html" title="class in com.google.adk.artifacts">GcsArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/InMemoryArtifactService.html#loadArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Optional)" class="member-name-link">loadArtifact(String, String, String, String, Optional&lt;Integer&gt;)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/InMemoryArtifactService.html" title="class in com.google.adk.artifacts">InMemoryArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/CallbackContext.html#loadArtifact(java.lang.String,java.util.Optional)" class="member-name-link">loadArtifact(String, Optional&lt;Integer&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></dt>
<dd>
<div class="block">Loads an artifact from the artifact service associated with the current session.</div>
</dd>
<dt><a href="com/google/adk/tools/LoadArtifactsTool.html" class="type-name-link" title="class in com.google.adk.tools">LoadArtifactsTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">A tool that loads artifacts and adds them to the session.</div>
</dd>
<dt><a href="com/google/adk/tools/LoadArtifactsTool.html#%3Cinit%3E()" class="member-name-link">LoadArtifactsTool()</a> - Constructor for class com.google.adk.tools.<a href="com/google/adk/tools/LoadArtifactsTool.html" title="class in com.google.adk.tools">LoadArtifactsTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#loadTools()" class="member-name-link">loadTools()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Loads all tools from the MCP Server.</div>
</dd>
<dt><a href="com/google/adk/models/VertexCredentials.html#location()" class="member-name-link">location()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/HttpApiClient.html#location()" class="member-name-link">location()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiClient.html" title="class in com.google.adk.sessions">HttpApiClient</a></dt>
<dd>
<div class="block">Returns the location for Vertex AI APIs.</div>
</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#longRunning()" class="member-name-link">longRunning()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/LongRunningFunctionTool.html" class="type-name-link" title="class in com.google.adk.tools">LongRunningFunctionTool</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">A function tool that returns the result asynchronously.</div>
</dd>
<dt><a href="com/google/adk/events/Event.html#longRunningToolIds()" class="member-name-link">longRunningToolIds()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">Set of ids of the long running function calls.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#longRunningToolIds(java.util.Optional)" class="member-name-link">longRunningToolIds(Optional&lt;Set&lt;String&gt;&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#longRunningToolIds(java.util.Set)" class="member-name-link">longRunningToolIds(Set&lt;String&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.html" class="type-name-link" title="class in com.google.adk.agents">LoopAgent</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">An agent that runs its sub-agents sequentially in a loop.</div>
</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LoopAgent.Builder</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/agents/LoopAgent.html" title="class in com.google.adk.agents"><code>LoopAgent</code></a>.</div>
</dd>
</dl>
<h2 class="title" id="I:M">M</h2>
<dl class="index">
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#maxIterations(int)" class="member-name-link">maxIterations(int)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#maxIterations(java.util.Optional)" class="member-name-link">maxIterations(Optional&lt;Integer&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#maxLlmCalls()" class="member-name-link">maxLlmCalls()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">McpInitializationException(String)</a> - Constructor for exception class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">McpInitializationException(String, Throwable)</a> - Constructor for exception class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpSessionManager.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpSessionManager</a> - Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Manages MCP client sessions.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpSessionManager.html#%3Cinit%3E(java.lang.Object)" class="member-name-link">McpSessionManager(Object)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpSessionManager.html" title="class in com.google.adk.tools.mcp">McpSessionManager</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpTool.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpTool</a> - Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">"""Initializes a MCPTool.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpTool.html#%3Cinit%3E(io.modelcontextprotocol.spec.McpSchema.Tool,io.modelcontextprotocol.client.McpSyncClient,com.google.adk.tools.mcp.McpSessionManager)" class="member-name-link">McpTool(McpSchema.Tool, McpSyncClient, McpSessionManager)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpTool.html#%3Cinit%3E(io.modelcontextprotocol.spec.McpSchema.Tool,io.modelcontextprotocol.client.McpSyncClient,com.google.adk.tools.mcp.McpSessionManager,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">McpTool(McpSchema.Tool, McpSyncClient, McpSessionManager, ObjectMapper)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">McpToolLoadingException(String)</a> - Constructor for exception class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">McpToolLoadingException(String, Throwable)</a> - Constructor for exception class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html#%3Cinit%3E(java.util.List,com.google.adk.tools.mcp.McpToolset)" class="member-name-link">McpToolsAndToolsetResult(List&lt;McpTool&gt;, McpToolset)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset</a> - Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Connects to a MCP Server, and retrieves MCP Tools into ADK Tools.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters)" class="member-name-link">McpToolset(SseServerParameters)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Initializes the McpToolset with SSE server parameters, using the ObjectMapper used across the
 ADK.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">McpToolset(SseServerParameters, ObjectMapper)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Initializes the McpToolset with SSE server parameters.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters)" class="member-name-link">McpToolset(ServerParameters)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Initializes the McpToolset with local server parameters, using the ObjectMapper used across the
 ADK.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.html#%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)" class="member-name-link">McpToolset(ServerParameters, ObjectMapper)</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" title="class in com.google.adk.tools.mcp">McpToolset</a></dt>
<dd>
<div class="block">Initializes the McpToolset with local server parameters.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a> - Exception Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Exception thrown when there's an error during MCP session initialization.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a> - Exception Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Exception thrown when there's an error during loading tools from the MCP server.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a> - Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Holds the result of loading tools, containing both the tools and the toolset instance.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a> - Exception Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Base exception for all errors originating from <code>McpToolset</code>.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">McpToolsetException(String)</a> - Constructor for exception class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">McpToolsetException(String, Throwable)</a> - Constructor for exception class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/HttpApiClient.html#MEDIA_TYPE_APPLICATION_JSON" class="member-name-link">MEDIA_TYPE_APPLICATION_JSON</a> - Static variable in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiClient.html" title="class in com.google.adk.sessions">HttpApiClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#merge(com.google.adk.events.EventActions)" class="member-name-link">merge(EventActions)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#model()" class="member-name-link">model()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlm.html#model()" class="member-name-link">model()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/BaseLlm.html" title="class in com.google.adk.models">BaseLlm</a></dt>
<dd>
<div class="block">Returns the name of the LLM model.</div>
</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#model()" class="member-name-link">model()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>
<div class="block">Returns the name of the LLM model to be used.</div>
</dd>
<dt><a href="com/google/adk/models/Model.html#model()" class="member-name-link">model()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Model.html" title="class in com.google.adk.models">Model</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#model(com.google.adk.models.BaseLlm)" class="member-name-link">model(BaseLlm)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Model.Builder.html#model(com.google.adk.models.BaseLlm)" class="member-name-link">model(BaseLlm)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Model.Builder.html" title="class in com.google.adk.models">Model.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#model(java.lang.String)" class="member-name-link">model(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#model(java.lang.String)" class="member-name-link">model(String)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Model.html" class="type-name-link" title="class in com.google.adk.models">Model</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Represents a model by name or instance.</div>
</dd>
<dt><a href="com/google/adk/models/Model.html#%3Cinit%3E()" class="member-name-link">Model()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/Model.html" title="class in com.google.adk.models">Model</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Model.Builder.html" class="type-name-link" title="class in com.google.adk.models">Model.Builder</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/models/Model.html" title="class in com.google.adk.models"><code>Model</code></a>.</div>
</dd>
<dt><a href="com/google/adk/models/Model.html#modelName()" class="member-name-link">modelName()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Model.html" title="class in com.google.adk.models">Model</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Gemini.Builder.html#modelName(java.lang.String)" class="member-name-link">modelName(String)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></dt>
<dd>
<div class="block">Sets the name of the Gemini model to use.</div>
</dd>
<dt><a href="com/google/adk/models/Model.Builder.html#modelName(java.lang.String)" class="member-name-link">modelName(String)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Model.Builder.html" title="class in com.google.adk.models">Model.Builder</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:N">N</h2>
<dl class="index">
<dt><a href="com/google/adk/agents/BaseAgent.html#name()" class="member-name-link">name()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>
<div class="block">Gets the agent's unique name.</div>
</dd>
<dt><a href="com/google/adk/tools/Annotations.Schema.html#name()" class="member-name-link">name()</a> - Element in annotation interface com.google.adk.tools.<a href="com/google/adk/tools/Annotations.Schema.html" title="annotation interface in com.google.adk.tools">Annotations.Schema</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#name()" class="member-name-link">name()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#name(java.lang.String)" class="member-name-link">name(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#name(java.lang.String)" class="member-name-link">name(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#name(java.lang.String)" class="member-name-link">name(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#name(java.lang.String)" class="member-name-link">name(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#newInvocationContextId()" class="member-name-link">newInvocationContextId()</a> - Static method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.html#nextPageToken()" class="member-name-link">nextPageToken()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.html" title="class in com.google.adk.sessions">ListEventsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListEventsResponse.Builder.html#nextPageToken(java.lang.String)" class="member-name-link">nextPageToken(String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.Builder.html" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.IncludeContents.html#NONE" class="member-name-link">NONE</a> - Enum constant in enum class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.StreamingMode.html#NONE" class="member-name-link">NONE</a> - Enum constant in enum class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.StreamingMode.html" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.html#numRecentEvents()" class="member-name-link">numRecentEvents()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.html" title="class in com.google.adk.sessions">GetSessionConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/GetSessionConfig.Builder.html#numRecentEvents(int)" class="member-name-link">numRecentEvents(int)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.Builder.html" title="class in com.google.adk.sessions">GetSessionConfig.Builder</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:O">O</h2>
<dl class="index">
<dt><a href="com/google/adk/utils/Pairs.html#of()" class="member-name-link">of()</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new, empty <code>ConcurrentHashMap</code>.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V)" class="member-name-link">of(K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing a single mapping.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V)" class="member-name-link">of(K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing two mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing three mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing four mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing five mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V,K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V, K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing six mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V,K,V,K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V, K, V, K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing seven mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing eight mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing nine mappings.</div>
</dd>
<dt><a href="com/google/adk/utils/Pairs.html#of(K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V)" class="member-name-link">of(K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V)</a> - Static method in class com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" title="class in com.google.adk.utils">Pairs</a></dt>
<dd>
<div class="block">Returns a new <code>ConcurrentHashMap</code> containing ten mappings.</div>
</dd>
<dt><a href="com/google/adk/examples/Example.html#output()" class="member-name-link">output()</a> - Method in class com.google.adk.examples.<a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples">Example</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.Builder.html#output(java.util.List)" class="member-name-link">output(List&lt;Content&gt;)</a> - Method in class com.google.adk.examples.<a href="com/google/adk/examples/Example.Builder.html" title="class in com.google.adk.examples">Example.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#outputAudioTranscription()" class="member-name-link">outputAudioTranscription()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#outputKey()" class="member-name-link">outputKey()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#outputKey(java.lang.String)" class="member-name-link">outputKey(String)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#outputSchema()" class="member-name-link">outputSchema()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#outputSchema(com.google.genai.types.Schema)" class="member-name-link">outputSchema(Schema)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.Builder.html#outputSchema(com.google.genai.types.Schema)" class="member-name-link">outputSchema(Schema)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models">LlmRequest.Builder</a></dt>
<dd>
<div class="block">Sets the output schema for the LLM response.</div>
</dd>
</dl>
<h2 class="title" id="I:P">P</h2>
<dl class="index">
<dt><a href="com/google/adk/utils/Pairs.html" class="type-name-link" title="class in com.google.adk.utils">Pairs</a> - Class in <a href="com/google/adk/utils/package-summary.html">com.google.adk.utils</a></dt>
<dd>
<div class="block">Utility class for creating ConcurrentHashMaps.</div>
</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.html" class="type-name-link" title="class in com.google.adk.agents">ParallelAgent</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">A shell agent that runs its sub-agents in parallel in isolated manner.</div>
</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">ParallelAgent.Builder</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/agents/ParallelAgent.html" title="class in com.google.adk.agents"><code>ParallelAgent</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#parentAgent()" class="member-name-link">parentAgent()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>
<div class="block">Retrieves the parent agent in the agent tree.</div>
</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#parentAgent(com.google.adk.agents.BaseAgent)" class="member-name-link">parentAgent(BaseAgent)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>
<div class="block">Sets the parent agent.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#parseReasoningEngineId(java.lang.String)" class="member-name-link">parseReasoningEngineId(String)</a> - Static method in class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#partial()" class="member-name-link">partial()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">partial is true for incomplete chunks from the LLM streaming response.</div>
</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#partial()" class="member-name-link">partial()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>
<div class="block">Indicates whether the text content is part of a unfinished text stream.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#partial(java.lang.Boolean)" class="member-name-link">partial(Boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#partial(java.lang.Boolean)" class="member-name-link">partial(Boolean)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#partial(java.util.Optional)" class="member-name-link">partial(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#partial(java.util.Optional)" class="member-name-link">partial(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#planning()" class="member-name-link">planning()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#planning(boolean)" class="member-name-link">planning(boolean)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Functions.html#populateClientFunctionCallId(com.google.adk.events.Event)" class="member-name-link">populateClientFunctionCallId(Event)</a> - Static method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Functions.html" title="class in com.google.adk.flows.llmflows">Functions</a></dt>
<dd>
<div class="block">Populates missing function call IDs in the provided event's content.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html#postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">postprocess(InvocationContext, Event, LlmRequest, LlmResponse)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></dt>
<dd>
<div class="block">Post-processes the LLM response after receiving it from the LLM.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html#preprocess(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">preprocess(InvocationContext, LlmRequest)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></dt>
<dd>
<div class="block">Pre-processes the LLM request before sending it to the LLM.</div>
</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest(LlmRequest.Builder, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>
<div class="block">Processes the outgoing <a href="com/google/adk/models/LlmRequest.Builder.html" title="class in com.google.adk.models"><code>LlmRequest.Builder</code></a>.</div>
</dd>
<dt><a href="com/google/adk/tools/BuiltInCodeExecutionTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest(LlmRequest.Builder, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/BuiltInCodeExecutionTool.html" title="class in com.google.adk.tools">BuiltInCodeExecutionTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/GoogleSearchTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest(LlmRequest.Builder, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/GoogleSearchTool.html" title="class in com.google.adk.tools">GoogleSearchTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/LoadArtifactsTool.html#processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)" class="member-name-link">processLlmRequest(LlmRequest.Builder, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/LoadArtifactsTool.html" title="class in com.google.adk.tools">LoadArtifactsTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/AgentTransfer.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest(InvocationContext, LlmRequest)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/AgentTransfer.html" title="class in com.google.adk.flows.llmflows">AgentTransfer</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Basic.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest(InvocationContext, LlmRequest)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Basic.html" title="class in com.google.adk.flows.llmflows">Basic</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Contents.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest(InvocationContext, LlmRequest)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Contents.html" title="class in com.google.adk.flows.llmflows">Contents</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Examples.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest(InvocationContext, LlmRequest)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Examples.html" title="class in com.google.adk.flows.llmflows">Examples</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Identity.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest(InvocationContext, LlmRequest)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Identity.html" title="class in com.google.adk.flows.llmflows">Identity</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/Instructions.html#processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)" class="member-name-link">processRequest(InvocationContext, LlmRequest)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Instructions.html" title="class in com.google.adk.flows.llmflows">Instructions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.html#project()" class="member-name-link">project()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/HttpApiClient.html#project()" class="member-name-link">project()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiClient.html" title="class in com.google.adk.sessions">HttpApiClient</a></dt>
<dd>
<div class="block">Returns the project ID for Vertex AI APIs.</div>
</dd>
<dt><a href="com/google/adk/sessions/State.html#put(java.lang.String,java.lang.Object)" class="member-name-link">put(String, Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#putAll(java.util.Map)" class="member-name-link">putAll(Map&lt;? extends String, ? extends Object&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#putIfAbsent(java.lang.String,java.lang.Object)" class="member-name-link">putIfAbsent(String, Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:R">R</h2>
<dl class="index">
<dt><a href="com/google/adk/agents/ReadonlyContext.html" class="type-name-link" title="class in com.google.adk.agents">ReadonlyContext</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Provides read-only access to the context of an agent run.</div>
</dd>
<dt><a href="com/google/adk/agents/ReadonlyContext.html#%3Cinit%3E(com.google.adk.agents.InvocationContext)" class="member-name-link">ReadonlyContext(InvocationContext)</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/ReadonlyContext.html" title="class in com.google.adk.agents">ReadonlyContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequestQueue.html#realtime(com.google.genai.types.Blob)" class="member-name-link">realtime(Blob)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlmConnection.html#receive()" class="member-name-link">receive()</a> - Method in interface com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></dt>
<dd>
<div class="block">Receives the model responses.</div>
</dd>
<dt><a href="com/google/adk/models/GeminiLlmConnection.html#receive()" class="member-name-link">receive()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/GeminiLlmConnection.html" title="class in com.google.adk.models">GeminiLlmConnection</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html#recognize(com.google.cloud.speech.v1.RecognitionConfig,com.google.cloud.speech.v1.RecognitionAudio)" class="member-name-link">recognize(RecognitionConfig, RecognitionAudio)</a> - Method in interface com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html" title="interface in com.google.adk.flows.llmflows.audio">SpeechClientInterface</a></dt>
<dd>
<div class="block">Performs synchronous speech recognition.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html#recognize(com.google.cloud.speech.v1.RecognitionConfig,com.google.cloud.speech.v1.RecognitionAudio)" class="member-name-link">recognize(RecognitionConfig, RecognitionAudio)</a> - Method in class com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html" title="class in com.google.adk.flows.llmflows.audio">VertexSpeechClient</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRegistry.html#registerLlm(java.lang.String,com.google.adk.models.LlmRegistry.LlmFactory)" class="member-name-link">registerLlm(String, LlmRegistry.LlmFactory)</a> - Static method in class com.google.adk.models.<a href="com/google/adk/models/LlmRegistry.html" title="class in com.google.adk.models">LlmRegistry</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#remove(java.lang.Object)" class="member-name-link">remove(Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#remove(java.lang.Object,java.lang.Object)" class="member-name-link">remove(Object, Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#replace(java.lang.String,java.lang.Object)" class="member-name-link">replace(String, Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#replace(java.lang.String,java.lang.Object,java.lang.Object)" class="member-name-link">replace(String, Object, Object)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/HttpApiClient.html#request(java.lang.String,java.lang.String,java.lang.String)" class="member-name-link">request(String, String, String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiClient.html" title="class in com.google.adk.sessions">HttpApiClient</a></dt>
<dd>
<div class="block">Sends a Http request given the http method, path, and request json string.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/SingleFlow.html#REQUEST_PROCESSORS" class="member-name-link">REQUEST_PROCESSORS</a> - Static variable in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/SingleFlow.html" title="class in com.google.adk.flows.llmflows">SingleFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#requestedAuthConfigs()" class="member-name-link">requestedAuthConfigs()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#requestedAuthConfigs(java.util.concurrent.ConcurrentMap)" class="member-name-link">requestedAuthConfigs(ConcurrentMap&lt;String, ConcurrentMap&lt;String, Object&gt;&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html#requestProcessors" class="member-name-link">requestProcessors</a> - Variable in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#resolvedModel()" class="member-name-link">resolvedModel()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#response(com.google.genai.types.GenerateContentResponse)" class="member-name-link">response(GenerateContentResponse)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/SingleFlow.html#RESPONSE_PROCESSORS" class="member-name-link">RESPONSE_PROCESSORS</a> - Static variable in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/SingleFlow.html" title="class in com.google.adk.flows.llmflows">SingleFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#responseModalities()" class="member-name-link">responseModalities()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html#responseProcessors" class="member-name-link">responseProcessors</a> - Variable in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#rootAgent()" class="member-name-link">rootAgent()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>
<div class="block">Returns the root agent for this agent by traversing up the parent chain.</div>
</dd>
<dt><a href="com/google/adk/flows/BaseFlow.html#run(com.google.adk.agents.InvocationContext)" class="member-name-link">run(InvocationContext)</a> - Method in interface com.google.adk.flows.<a href="com/google/adk/flows/BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a></dt>
<dd>
<div class="block">Run this flow.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html#run(com.google.adk.agents.InvocationContext)" class="member-name-link">run(InvocationContext)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#runAsync(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsync(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#runAsync(com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">runAsync(Session, Content, RunConfig)</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>
<div class="block">Runs the agent in the standard mode using a provided Session object.</div>
</dd>
<dt><a href="com/google/adk/runner/Runner.html#runAsync(java.lang.String,java.lang.String,com.google.genai.types.Content)" class="member-name-link">runAsync(String, String, Content)</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>
<div class="block">Asynchronously runs the agent for a given user and session, processing a new message and using
 a default <a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents"><code>RunConfig</code></a>.</div>
</dd>
<dt><a href="com/google/adk/runner/Runner.html#runAsync(java.lang.String,java.lang.String,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">runAsync(String, String, Content, RunConfig)</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>
<div class="block">Runs the agent in the standard mode.</div>
</dd>
<dt><a href="com/google/adk/tools/AgentTool.html#runAsync(java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">runAsync(Map&lt;String, Object&gt;, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/AgentTool.html" title="class in com.google.adk.tools">AgentTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/BaseTool.html#runAsync(java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">runAsync(Map&lt;String, Object&gt;, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" title="class in com.google.adk.tools">BaseTool</a></dt>
<dd>
<div class="block">Calls a tool.</div>
</dd>
<dt><a href="com/google/adk/tools/FunctionTool.html#runAsync(java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">runAsync(Map&lt;String, Object&gt;, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/FunctionTool.html" title="class in com.google.adk.tools">FunctionTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/LoadArtifactsTool.html#runAsync(java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">runAsync(Map&lt;String, Object&gt;, ToolContext)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/LoadArtifactsTool.html" title="class in com.google.adk.tools">LoadArtifactsTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpTool.html#runAsync(java.util.Map,com.google.adk.tools.ToolContext)" class="member-name-link">runAsync(Map&lt;String, Object&gt;, ToolContext)</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.html" title="class in com.google.adk.agents">LoopAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.html" title="class in com.google.adk.agents">ParallelAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.html#runAsyncImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runAsyncImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.html" title="class in com.google.adk.agents">SequentialAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#runConfig()" class="member-name-link">runConfig()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html" class="type-name-link" title="class in com.google.adk.agents">RunConfig</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Configuration to modify an agent's LLM's underlying behavior.</div>
</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#%3Cinit%3E()" class="member-name-link">RunConfig()</a> - Constructor for class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html" class="type-name-link" title="class in com.google.adk.agents">RunConfig.Builder</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents"><code>RunConfig</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/RunConfig.StreamingMode.html" class="type-name-link" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a> - Enum Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Streaming mode for the runner.</div>
</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/BaseFlow.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive(InvocationContext)</a> - Method in interface com.google.adk.flows.<a href="com/google/adk/flows/BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/BaseLlmFlow.html#runLive(com.google.adk.agents.InvocationContext)" class="member-name-link">runLive(InvocationContext)</a> - Method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#runLive(com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">runLive(Session, LiveRequestQueue, RunConfig)</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#runLive(java.lang.String,java.lang.String,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)" class="member-name-link">runLive(String, String, LiveRequestQueue, RunConfig)</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.html" title="class in com.google.adk.agents">LoopAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.html" title="class in com.google.adk.agents">ParallelAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.html#runLiveImpl(com.google.adk.agents.InvocationContext)" class="member-name-link">runLiveImpl(InvocationContext)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.html" title="class in com.google.adk.agents">SequentialAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html" class="type-name-link" title="class in com.google.adk.runner">Runner</a> - Class in <a href="com/google/adk/runner/package-summary.html">com.google.adk.runner</a></dt>
<dd>
<div class="block">The main class for the GenAI Agents runner.</div>
</dd>
<dt><a href="com/google/adk/runner/Runner.html#%3Cinit%3E(com.google.adk.agents.BaseAgent,java.lang.String,com.google.adk.artifacts.BaseArtifactService,com.google.adk.sessions.BaseSessionService)" class="member-name-link">Runner(BaseAgent, String, BaseArtifactService, BaseSessionService)</a> - Constructor for class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#runWithSessionId(java.lang.String,com.google.genai.types.Content,com.google.adk.agents.RunConfig)" class="member-name-link">runWithSessionId(String, Content, RunConfig)</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:S">S</h2>
<dl class="index">
<dt><a href="com/google/adk/agents/CallbackContext.html#saveArtifact(java.lang.String,com.google.genai.types.Part)" class="member-name-link">saveArtifact(String, Part)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></dt>
<dd>
<div class="block">Saves an artifact and records it as a delta for the current session.</div>
</dd>
<dt><a href="com/google/adk/artifacts/BaseArtifactService.html#saveArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.google.genai.types.Part)" class="member-name-link">saveArtifact(String, String, String, String, Part)</a> - Method in interface com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a></dt>
<dd>
<div class="block">Saves an artifact.</div>
</dd>
<dt><a href="com/google/adk/artifacts/GcsArtifactService.html#saveArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.google.genai.types.Part)" class="member-name-link">saveArtifact(String, String, String, String, Part)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/GcsArtifactService.html" title="class in com.google.adk.artifacts">GcsArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/InMemoryArtifactService.html#saveArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.google.genai.types.Part)" class="member-name-link">saveArtifact(String, String, String, String, Part)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/InMemoryArtifactService.html" title="class in com.google.adk.artifacts">InMemoryArtifactService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#saveInputBlobsAsArtifacts()" class="member-name-link">saveInputBlobsAsArtifacts()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/SchemaUtils.html" class="type-name-link" title="class in com.google.adk">SchemaUtils</a> - Class in <a href="com/google/adk/package-summary.html">com.google.adk</a></dt>
<dd>
<div class="block">Utility class for validating schemas.</div>
</dd>
<dt><a href="com/google/adk/agents/LiveRequestQueue.html#send(com.google.adk.agents.LiveRequest)" class="member-name-link">send(LiveRequest)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequestQueue.html" title="class in com.google.adk.agents">LiveRequestQueue</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlmConnection.html#sendContent(com.google.genai.types.Content)" class="member-name-link">sendContent(Content)</a> - Method in interface com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></dt>
<dd>
<div class="block">Sends a user content to the model.</div>
</dd>
<dt><a href="com/google/adk/models/GeminiLlmConnection.html#sendContent(com.google.genai.types.Content)" class="member-name-link">sendContent(Content)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/GeminiLlmConnection.html" title="class in com.google.adk.models">GeminiLlmConnection</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlmConnection.html#sendHistory(java.util.List)" class="member-name-link">sendHistory(List&lt;Content&gt;)</a> - Method in interface com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></dt>
<dd>
<div class="block">Sends the conversation history to the model.</div>
</dd>
<dt><a href="com/google/adk/models/GeminiLlmConnection.html#sendHistory(java.util.List)" class="member-name-link">sendHistory(List&lt;Content&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/GeminiLlmConnection.html" title="class in com.google.adk.models">GeminiLlmConnection</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/BaseLlmConnection.html#sendRealtime(com.google.genai.types.Blob)" class="member-name-link">sendRealtime(Blob)</a> - Method in interface com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a></dt>
<dd>
<div class="block">Sends a chunk of audio or a frame of video to the model in realtime.</div>
</dd>
<dt><a href="com/google/adk/models/GeminiLlmConnection.html#sendRealtime(com.google.genai.types.Blob)" class="member-name-link">sendRealtime(Blob)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/GeminiLlmConnection.html" title="class in com.google.adk.models">GeminiLlmConnection</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">An agent that runs its sub-agents sequentially.</div>
</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent.Builder</a> - Class in <a href="com/google/adk/agents/package-summary.html">com.google.adk.agents</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/agents/SequentialAgent.html" title="class in com.google.adk.agents"><code>SequentialAgent</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#session()" class="member-name-link">session()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html" class="type-name-link" title="class in com.google.adk.sessions">Session</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">A <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a> object that encapsulates the <a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions"><code>State</code></a> and <a href="com/google/adk/events/Event.html" title="class in com.google.adk.events"><code>Event</code></a>s of a session.</div>
</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">Session.Builder</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions"><code>Session</code></a>.</div>
</dd>
<dt><a href="com/google/adk/sessions/SessionException.html" class="type-name-link" title="class in com.google.adk.sessions">SessionException</a> - Exception Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Represents a general error that occurred during session management operations.</div>
</dd>
<dt><a href="com/google/adk/sessions/SessionException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">SessionException(String)</a> - Constructor for exception class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionException.html" title="class in com.google.adk.sessions">SessionException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/SessionException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">SessionException(String, Throwable)</a> - Constructor for exception class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionException.html" title="class in com.google.adk.sessions">SessionException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/SessionException.html#%3Cinit%3E(java.lang.Throwable)" class="member-name-link">SessionException(Throwable)</a> - Constructor for exception class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionException.html" title="class in com.google.adk.sessions">SessionException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.html#sessionIds()" class="member-name-link">sessionIds()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/SessionNotFoundException.html" class="type-name-link" title="class in com.google.adk.sessions">SessionNotFoundException</a> - Exception Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Indicates that a requested session could not be found.</div>
</dd>
<dt><a href="com/google/adk/sessions/SessionNotFoundException.html#%3Cinit%3E(java.lang.String)" class="member-name-link">SessionNotFoundException(String)</a> - Constructor for exception class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionNotFoundException.html" title="class in com.google.adk.sessions">SessionNotFoundException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/SessionNotFoundException.html#%3Cinit%3E(java.lang.String,java.lang.Throwable)" class="member-name-link">SessionNotFoundException(String, Throwable)</a> - Constructor for exception class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionNotFoundException.html" title="class in com.google.adk.sessions">SessionNotFoundException</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.html#sessions()" class="member-name-link">sessions()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.html" title="class in com.google.adk.sessions">ListSessionsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/ListSessionsResponse.Builder.html#sessions(java.util.List)" class="member-name-link">sessions(List&lt;Session&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.Builder.html" title="class in com.google.adk.sessions">ListSessionsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#sessionService()" class="member-name-link">sessionService()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/runner/Runner.html#sessionService()" class="member-name-link">sessionService()</a> - Method in class com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" title="class in com.google.adk.runner">Runner</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/SessionUtils.html" class="type-name-link" title="class in com.google.adk.sessions">SessionUtils</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">Utility functions for session service.</div>
</dd>
<dt><a href="com/google/adk/sessions/SessionUtils.html#%3Cinit%3E()" class="member-name-link">SessionUtils()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/SessionUtils.html" title="class in com.google.adk.sessions">SessionUtils</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setActions(com.google.adk.events.EventActions)" class="member-name-link">setActions(EventActions)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ToolContext.html#setActions(com.google.adk.events.EventActions)" class="member-name-link">setActions(EventActions)</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setArtifactDelta(java.util.concurrent.ConcurrentMap)" class="member-name-link">setArtifactDelta(ConcurrentMap&lt;String, Part&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setAuthor(java.lang.String)" class="member-name-link">setAuthor(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setContent(java.util.Optional)" class="member-name-link">setContent(Optional&lt;Content&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#setCredentials(com.google.auth.oauth2.GoogleCredentials)" class="member-name-link">setCredentials(GoogleCredentials)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#setCredentials(java.util.Optional)" class="member-name-link">setCredentials(Optional&lt;GoogleCredentials&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setErrorCode(java.util.Optional)" class="member-name-link">setErrorCode(Optional&lt;FinishReason&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setErrorMessage(java.util.Optional)" class="member-name-link">setErrorMessage(Optional&lt;String&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setEscalate(boolean)" class="member-name-link">setEscalate(boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setEscalate(java.util.Optional)" class="member-name-link">setEscalate(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setGroundingMetadata(java.util.Optional)" class="member-name-link">setGroundingMetadata(Optional&lt;GroundingMetadata&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setId(java.lang.String)" class="member-name-link">setId(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setInterrupted(java.util.Optional)" class="member-name-link">setInterrupted(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setInvocationId(java.lang.String)" class="member-name-link">setInvocationId(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#setLocation(java.lang.String)" class="member-name-link">setLocation(String)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#setLocation(java.util.Optional)" class="member-name-link">setLocation(Optional&lt;String&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setLongRunningToolIds(java.util.Optional)" class="member-name-link">setLongRunningToolIds(Optional&lt;Set&lt;String&gt;&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#setMaxLlmCalls(int)" class="member-name-link">setMaxLlmCalls(int)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#setOutputAudioTranscription(com.google.genai.types.AudioTranscriptionConfig)" class="member-name-link">setOutputAudioTranscription(AudioTranscriptionConfig)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setPartial(java.util.Optional)" class="member-name-link">setPartial(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#setProject(java.lang.String)" class="member-name-link">setProject(String)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html#setProject(java.util.Optional)" class="member-name-link">setProject(Optional&lt;String&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" title="class in com.google.adk.models">VertexCredentials.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setRequestedAuthConfigs(java.util.concurrent.ConcurrentMap)" class="member-name-link">setRequestedAuthConfigs(ConcurrentMap&lt;String, ConcurrentMap&lt;String, Object&gt;&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#setResponseModalities(java.lang.Iterable)" class="member-name-link">setResponseModalities(Iterable&lt;Modality&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#setSaveInputBlobsAsArtifacts(boolean)" class="member-name-link">setSaveInputBlobsAsArtifacts(boolean)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setSkipSummarization(boolean)" class="member-name-link">setSkipSummarization(boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setSkipSummarization(java.lang.Boolean)" class="member-name-link">setSkipSummarization(Boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setSkipSummarization(java.util.Optional)" class="member-name-link">setSkipSummarization(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#setSpeechConfig(com.google.genai.types.SpeechConfig)" class="member-name-link">setSpeechConfig(SpeechConfig)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setStateDelta(java.util.concurrent.ConcurrentMap)" class="member-name-link">setStateDelta(ConcurrentMap&lt;String, Object&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.Builder.html#setStreamingMode(com.google.adk.agents.RunConfig.StreamingMode)" class="member-name-link">setStreamingMode(RunConfig.StreamingMode)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" title="class in com.google.adk.agents">RunConfig.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setTimestamp(long)" class="member-name-link">setTimestamp(long)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setTransferToAgent(java.lang.String)" class="member-name-link">setTransferToAgent(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#setTransferToAgent(java.util.Optional)" class="member-name-link">setTransferToAgent(Optional&lt;String&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#setTurnComplete(java.util.Optional)" class="member-name-link">setTurnComplete(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html#shouldClose()" class="member-name-link">shouldClose()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></dt>
<dd>
<div class="block">Extracts boolean value from the close field or returns false if unset.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/SingleFlow.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">SingleFlow</a> - Class in <a href="com/google/adk/flows/llmflows/package-summary.html">com.google.adk.flows.llmflows</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/SingleFlow.html#%3Cinit%3E()" class="member-name-link">SingleFlow()</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/SingleFlow.html" title="class in com.google.adk.flows.llmflows">SingleFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/SingleFlow.html#%3Cinit%3E(java.util.List,java.util.List)" class="member-name-link">SingleFlow(List&lt;RequestProcessor&gt;, List&lt;ResponseProcessor&gt;)</a> - Constructor for class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/SingleFlow.html" title="class in com.google.adk.flows.llmflows">SingleFlow</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#size()" class="member-name-link">size()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#skipSummarization()" class="member-name-link">skipSummarization()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#skipSummarization(boolean)" class="member-name-link">skipSummarization(boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html" class="type-name-link" title="interface in com.google.adk.flows.llmflows.audio">SpeechClientInterface</a> - Interface in <a href="com/google/adk/flows/llmflows/audio/package-summary.html">com.google.adk.flows.llmflows.audio</a></dt>
<dd>
<div class="block">Interface for a speech-to-text client.</div>
</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#speechConfig()" class="member-name-link">speechConfig()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.StreamingMode.html#SSE" class="member-name-link">SSE</a> - Enum constant in enum class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.StreamingMode.html" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.html#sseReadTimeout()" class="member-name-link">sseReadTimeout()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></dt>
<dd>
<div class="block">The timeout for reading data from the SSE stream.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html#sseReadTimeout(java.time.Duration)" class="member-name-link">sseReadTimeout(Duration)</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></dt>
<dd>
<div class="block">Sets the timeout for reading data from the SSE stream.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.html" class="type-name-link" title="class in com.google.adk.tools.mcp">SseServerParameters</a> - Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Parameters for establishing a MCP Server-Sent Events (SSE) connection.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.html#%3Cinit%3E()" class="member-name-link">SseServerParameters()</a> - Constructor for class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" class="type-name-link" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a> - Class in <a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp"><code>SseServerParameters</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/CallbackContext.html#state()" class="member-name-link">state()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></dt>
<dd>
<div class="block">Returns the delta-aware state of the current callback.</div>
</dd>
<dt><a href="com/google/adk/agents/ReadonlyContext.html#state()" class="member-name-link">state()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ReadonlyContext.html" title="class in com.google.adk.agents">ReadonlyContext</a></dt>
<dd>
<div class="block">Returns a read-only view of the state of the current session.</div>
</dd>
<dt><a href="com/google/adk/sessions/Session.html#state()" class="member-name-link">state()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#state(com.google.adk.sessions.State)" class="member-name-link">state(State)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#state(java.util.concurrent.ConcurrentMap)" class="member-name-link">state(ConcurrentMap&lt;String, Object&gt;)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html" class="type-name-link" title="class in com.google.adk.sessions">State</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">A <a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions"><code>State</code></a> object that also keeps track of the changes to the state.</div>
</dd>
<dt><a href="com/google/adk/sessions/State.html#%3Cinit%3E(java.util.concurrent.ConcurrentMap)" class="member-name-link">State(ConcurrentMap&lt;String, Object&gt;)</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/State.html#%3Cinit%3E(java.util.concurrent.ConcurrentMap,java.util.concurrent.ConcurrentMap)" class="member-name-link">State(ConcurrentMap&lt;String, Object&gt;, ConcurrentMap&lt;String, Object&gt;)</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#stateDelta()" class="member-name-link">stateDelta()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#stateDelta(java.util.concurrent.ConcurrentMap)" class="member-name-link">stateDelta(ConcurrentMap&lt;String, Object&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/RunConfig.html#streamingMode()" class="member-name-link">streamingMode()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" title="class in com.google.adk.agents">RunConfig</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#stringifyContent()" class="member-name-link">stringifyContent()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/BaseAgent.html#subAgents()" class="member-name-link">subAgents()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" title="class in com.google.adk.agents">BaseAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents(BaseAgent...)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents(BaseAgent...)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents(BaseAgent...)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#subAgents(com.google.adk.agents.BaseAgent...)" class="member-name-link">subAgents(BaseAgent...)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents(List&lt;? extends BaseAgent&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LoopAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents(List&lt;? extends BaseAgent&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" title="class in com.google.adk.agents">LoopAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/ParallelAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents(List&lt;? extends BaseAgent&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" title="class in com.google.adk.agents">ParallelAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/SequentialAgent.Builder.html#subAgents(java.util.List)" class="member-name-link">subAgents(List&lt;? extends BaseAgent&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" title="class in com.google.adk.agents">SequentialAgent.Builder</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:T">T</h2>
<dl class="index">
<dt><a href="com/google/adk/Telemetry.html" class="type-name-link" title="class in com.google.adk">Telemetry</a> - Class in <a href="com/google/adk/package-summary.html">com.google.adk</a></dt>
<dd>
<div class="block">Utility class for capturing and reporting telemetry data within the ADK.</div>
</dd>
<dt><a href="com/google/adk/sessions/State.html#TEMP_PREFIX" class="member-name-link">TEMP_PREFIX</a> - Static variable in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.html#timeout()" class="member-name-link">timeout()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></dt>
<dd>
<div class="block">The timeout for the initial connection attempt.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html#timeout(java.time.Duration)" class="member-name-link">timeout(Duration)</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></dt>
<dd>
<div class="block">Sets the timeout for the initial connection attempt.</div>
</dd>
<dt><a href="com/google/adk/events/Event.html#timestamp()" class="member-name-link">timestamp()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>
<div class="block">The timestamp of the event.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#timestamp(long)" class="member-name-link">timestamp(long)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#timestamp(java.util.Optional)" class="member-name-link">timestamp(Optional&lt;Long&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LiveRequest.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" title="class in com.google.adk.agents">LiveRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/examples/Example.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.examples.<a href="com/google/adk/examples/Example.html" title="class in com.google.adk.examples">Example</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Model.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Model.html" title="class in com.google.adk.models">Model</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/ToolContext.html#toBuilder()" class="member-name-link">toBuilder()</a> - Method in class com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools">ToolContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/tools/mcp/McpTool.html#toGeminiSchema(io.modelcontextprotocol.spec.McpSchema.JsonSchema)" class="member-name-link">toGeminiSchema(McpSchema.JsonSchema)</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpTool.html" title="class in com.google.adk.tools.mcp">McpTool</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/JsonBaseModel.html#toJson()" class="member-name-link">toJson()</a> - Method in class com.google.adk.<a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/JsonBaseModel.html#toJsonNode(java.lang.Object)" class="member-name-link">toJsonNode(Object)</a> - Static method in class com.google.adk.<a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></dt>
<dd>
<div class="block">Serializes an object to a JsonNode.</div>
</dd>
<dt><a href="com/google/adk/JsonBaseModel.html#toJsonString(java.lang.Object)" class="member-name-link">toJsonString(Object)</a> - Static method in class com.google.adk.<a href="com/google/adk/JsonBaseModel.html" title="class in com.google.adk">JsonBaseModel</a></dt>
<dd>
<div class="block">Serializes an object to a Json string.</div>
</dd>
<dt><a href="com/google/adk/tools/ToolContext.html" class="type-name-link" title="class in com.google.adk.tools">ToolContext</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">ToolContext object provides a structured context for executing tools or functions.</div>
</dd>
<dt><a href="com/google/adk/tools/ToolContext.Builder.html" class="type-name-link" title="class in com.google.adk.tools">ToolContext.Builder</a> - Class in <a href="com/google/adk/tools/package-summary.html">com.google.adk.tools</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/tools/ToolContext.html" title="class in com.google.adk.tools"><code>ToolContext</code></a>.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.html#tools()" class="member-name-link">tools()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" title="class in com.google.adk.agents">LlmAgent</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmRequest.html#tools()" class="member-name-link">tools()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" title="class in com.google.adk.models">LlmRequest</a></dt>
<dd>
<div class="block">Returns a map of tools available to the LLM.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#tools(com.google.adk.tools.BaseTool...)" class="member-name-link">tools(BaseTool...)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/LlmAgent.Builder.html#tools(java.util.List)" class="member-name-link">tools(List&lt;? extends BaseTool&gt;)</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" title="class in com.google.adk.agents">LlmAgent.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/Telemetry.html#traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)" class="member-name-link">traceCallLlm(InvocationContext, String, LlmRequest, LlmResponse)</a> - Static method in class com.google.adk.<a href="com/google/adk/Telemetry.html" title="class in com.google.adk">Telemetry</a></dt>
<dd>
<div class="block">Traces a call to the LLM.</div>
</dd>
<dt><a href="com/google/adk/Telemetry.html#traceSendData(com.google.adk.agents.InvocationContext,java.lang.String,java.util.List)" class="member-name-link">traceSendData(InvocationContext, String, List&lt;Content&gt;)</a> - Static method in class com.google.adk.<a href="com/google/adk/Telemetry.html" title="class in com.google.adk">Telemetry</a></dt>
<dd>
<div class="block">Traces the sending of data (history or new content) to the agent/model.</div>
</dd>
<dt><a href="com/google/adk/Telemetry.html#traceToolCall(java.util.Map)" class="member-name-link">traceToolCall(Map&lt;String, Object&gt;)</a> - Static method in class com.google.adk.<a href="com/google/adk/Telemetry.html" title="class in com.google.adk">Telemetry</a></dt>
<dd>
<div class="block">Traces tool call arguments.</div>
</dd>
<dt><a href="com/google/adk/Telemetry.html#traceToolResponse(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.events.Event)" class="member-name-link">traceToolResponse(InvocationContext, String, Event)</a> - Static method in class com.google.adk.<a href="com/google/adk/Telemetry.html" title="class in com.google.adk">Telemetry</a></dt>
<dd>
<div class="block">Traces tool response event.</div>
</dd>
<dt><a href="com/google/adk/events/EventActions.html#transferToAgent()" class="member-name-link">transferToAgent()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.html" title="class in com.google.adk.events">EventActions</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/EventActions.Builder.html#transferToAgent(java.lang.String)" class="member-name-link">transferToAgent(String)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" title="class in com.google.adk.events">EventActions.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/flows/llmflows/AgentTransfer.html#transferToAgent(java.lang.String,com.google.adk.tools.ToolContext)" class="member-name-link">transferToAgent(String, ToolContext)</a> - Static method in class com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/AgentTransfer.html" title="class in com.google.adk.flows.llmflows">AgentTransfer</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.html#turnComplete()" class="member-name-link">turnComplete()</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.html" title="class in com.google.adk.events">Event</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.html#turnComplete()" class="member-name-link">turnComplete()</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" title="class in com.google.adk.models">LlmResponse</a></dt>
<dd>
<div class="block">Indicates whether the response from the model is complete.</div>
</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#turnComplete(java.lang.Boolean)" class="member-name-link">turnComplete(Boolean)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#turnComplete(java.lang.Boolean)" class="member-name-link">turnComplete(Boolean)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/events/Event.Builder.html#turnComplete(java.util.Optional)" class="member-name-link">turnComplete(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" title="class in com.google.adk.events">Event.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/LlmResponse.Builder.html#turnComplete(java.util.Optional)" class="member-name-link">turnComplete(Optional&lt;Boolean&gt;)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" title="class in com.google.adk.models">LlmResponse.Builder</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:U">U</h2>
<dl class="index">
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.html#url()" class="member-name-link">url()</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.html" title="class in com.google.adk.tools.mcp">SseServerParameters</a></dt>
<dd>
<div class="block">The URL of the SSE server.</div>
</dd>
<dt><a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html#url(java.lang.String)" class="member-name-link">url(String)</a> - Method in class com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></dt>
<dd>
<div class="block">Sets the URL of the SSE server.</div>
</dd>
<dt><a href="com/google/adk/sessions/State.html#USER_PREFIX" class="member-name-link">USER_PREFIX</a> - Static variable in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/CallbackContext.html#userContent()" class="member-name-link">userContent()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" title="class in com.google.adk.agents">CallbackContext</a></dt>
<dd>
<div class="block">Returns the user content that initiated this invocation.</div>
</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#userContent()" class="member-name-link">userContent()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/agents/InvocationContext.html#userId()" class="member-name-link">userId()</a> - Method in class com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" title="class in com.google.adk.agents">InvocationContext</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.html#userId()" class="member-name-link">userId()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" title="class in com.google.adk.sessions">Session</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/Session.Builder.html#userId(java.lang.String)" class="member-name-link">userId(String)</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" title="class in com.google.adk.sessions">Session.Builder</a></dt>
<dd>&nbsp;</dd>
</dl>
<h2 class="title" id="I:V">V</h2>
<dl class="index">
<dt><a href="com/google/adk/SchemaUtils.html#validateMapOnSchema(java.util.Map,com.google.genai.types.Schema,java.lang.Boolean)" class="member-name-link">validateMapOnSchema(Map&lt;String, Object&gt;, Schema, Boolean)</a> - Static method in class com.google.adk.<a href="com/google/adk/SchemaUtils.html" title="class in com.google.adk">SchemaUtils</a></dt>
<dd>
<div class="block">Validates a map against a schema.</div>
</dd>
<dt><a href="com/google/adk/SchemaUtils.html#validateOutputSchema(java.lang.String,com.google.genai.types.Schema)" class="member-name-link">validateOutputSchema(String, Schema)</a> - Static method in class com.google.adk.<a href="com/google/adk/SchemaUtils.html" title="class in com.google.adk">SchemaUtils</a></dt>
<dd>
<div class="block">Validates an output string against a schema.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.IncludeContents.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - Static method in enum class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/google/adk/agents/RunConfig.StreamingMode.html#valueOf(java.lang.String)" class="member-name-link">valueOf(String)</a> - Static method in enum class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.StreamingMode.html" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></dt>
<dd>
<div class="block">Returns the enum constant of this class with the specified name.</div>
</dd>
<dt><a href="com/google/adk/agents/LlmAgent.IncludeContents.html#values()" class="member-name-link">values()</a> - Static method in enum class com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.IncludeContents.html" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/google/adk/agents/RunConfig.StreamingMode.html#values()" class="member-name-link">values()</a> - Static method in enum class com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.StreamingMode.html" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></dt>
<dd>
<div class="block">Returns an array containing the constants of this enum class, in
the order they are declared.</div>
</dd>
<dt><a href="com/google/adk/sessions/State.html#values()" class="member-name-link">values()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" title="class in com.google.adk.sessions">State</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/Version.html" class="type-name-link" title="class in com.google.adk">Version</a> - Class in <a href="com/google/adk/package-summary.html">com.google.adk</a></dt>
<dd>
<div class="block">Holding class for the version of the Java ADK.</div>
</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html#versions()" class="member-name-link">versions()</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html#versions(java.util.List)" class="member-name-link">versions(List&lt;Part&gt;)</a> - Method in class com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse.Builder</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/HttpApiClient.html#vertexAI()" class="member-name-link">vertexAI()</a> - Method in class com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiClient.html" title="class in com.google.adk.sessions">HttpApiClient</a></dt>
<dd>
<div class="block">Returns whether the client is using Vertex AI APIs.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html" class="type-name-link" title="class in com.google.adk.sessions">VertexAiSessionService</a> - Class in <a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></dt>
<dd>
<div class="block">TODO: Use the genai HttpApiClient and ApiResponse methods once they are public.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#%3Cinit%3E()" class="member-name-link">VertexAiSessionService()</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#%3Cinit%3E(java.lang.String,java.lang.String,com.google.adk.sessions.HttpApiClient)" class="member-name-link">VertexAiSessionService(String, String, HttpApiClient)</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>
<div class="block">Creates a new instance of the Vertex AI Session Service with a custom ApiClient for testing.</div>
</dd>
<dt><a href="com/google/adk/sessions/VertexAiSessionService.html#%3Cinit%3E(java.lang.String,java.lang.String,java.util.Optional,java.util.Optional)" class="member-name-link">VertexAiSessionService(String, String, Optional&lt;GoogleCredentials&gt;, Optional&lt;HttpOptions&gt;)</a> - Constructor for class com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" title="class in com.google.adk.sessions">VertexAiSessionService</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/Gemini.Builder.html#vertexCredentials(com.google.adk.models.VertexCredentials)" class="member-name-link">vertexCredentials(VertexCredentials)</a> - Method in class com.google.adk.models.<a href="com/google/adk/models/Gemini.Builder.html" title="class in com.google.adk.models">Gemini.Builder</a></dt>
<dd>
<div class="block">Sets the Vertex AI credentials.</div>
</dd>
<dt><a href="com/google/adk/models/VertexCredentials.html" class="type-name-link" title="class in com.google.adk.models">VertexCredentials</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Credentials for accessing Gemini models through Vertex.</div>
</dd>
<dt><a href="com/google/adk/models/VertexCredentials.html#%3Cinit%3E()" class="member-name-link">VertexCredentials()</a> - Constructor for class com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models">VertexCredentials</a></dt>
<dd>&nbsp;</dd>
<dt><a href="com/google/adk/models/VertexCredentials.Builder.html" class="type-name-link" title="class in com.google.adk.models">VertexCredentials.Builder</a> - Class in <a href="com/google/adk/models/package-summary.html">com.google.adk.models</a></dt>
<dd>
<div class="block">Builder for <a href="com/google/adk/models/VertexCredentials.html" title="class in com.google.adk.models"><code>VertexCredentials</code></a>.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html" class="type-name-link" title="class in com.google.adk.flows.llmflows.audio">VertexSpeechClient</a> - Class in <a href="com/google/adk/flows/llmflows/audio/package-summary.html">com.google.adk.flows.llmflows.audio</a></dt>
<dd>
<div class="block">Implementation of SpeechClientInterface using Vertex AI SpeechClient.</div>
</dd>
<dt><a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html#%3Cinit%3E()" class="member-name-link">VertexSpeechClient()</a> - Constructor for class com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html" title="class in com.google.adk.flows.llmflows.audio">VertexSpeechClient</a></dt>
<dd>
<div class="block">Constructs a VertexSpeechClient, initializing the underlying Google Cloud SpeechClient.</div>
</dd>
</dl>
<a href="#I:A">A</a>&nbsp;<a href="#I:B">B</a>&nbsp;<a href="#I:C">C</a>&nbsp;<a href="#I:D">D</a>&nbsp;<a href="#I:E">E</a>&nbsp;<a href="#I:F">F</a>&nbsp;<a href="#I:G">G</a>&nbsp;<a href="#I:H">H</a>&nbsp;<a href="#I:I">I</a>&nbsp;<a href="#I:J">J</a>&nbsp;<a href="#I:K">K</a>&nbsp;<a href="#I:L">L</a>&nbsp;<a href="#I:M">M</a>&nbsp;<a href="#I:N">N</a>&nbsp;<a href="#I:O">O</a>&nbsp;<a href="#I:P">P</a>&nbsp;<a href="#I:R">R</a>&nbsp;<a href="#I:S">S</a>&nbsp;<a href="#I:T">T</a>&nbsp;<a href="#I:U">U</a>&nbsp;<a href="#I:V">V</a>&nbsp;<br><a href="allclasses-index.html">All&nbsp;Classes&nbsp;and&nbsp;Interfaces</a><span class="vertical-separator">|</span><a href="allpackages-index.html">All&nbsp;Packages</a><span class="vertical-separator">|</span><a href="constant-values.html">Constant&nbsp;Field&nbsp;Values</a><span class="vertical-separator">|</span><a href="serialized-form.html">Serialized&nbsp;Form</a>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
