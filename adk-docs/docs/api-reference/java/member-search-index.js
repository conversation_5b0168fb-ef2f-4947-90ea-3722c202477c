memberSearchIndex = [{"p":"com.google.adk.events","c":"Event","l":"actions()"},{"p":"com.google.adk.tools","c":"ToolContext","l":"actions()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"actions(EventActions)","u":"actions(com.google.adk.events.EventActions)"},{"p":"com.google.adk.tools","c":"ToolContext.Builder","l":"actions(EventActions)","u":"actions(com.google.adk.events.EventActions)"},{"p":"com.google.adk.tools.mcp","c":"ConversionUtils","l":"adkToMcpToolType(BaseTool)","u":"adkToMcpToolType(com.google.adk.tools.BaseTool)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"afterAgentCallback()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"afterAgentCallback(Callbacks.AfterAgentCallback)","u":"afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"afterAgentCallback(Callbacks.AfterAgentCallback)","u":"afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"afterAgentCallback(Callbacks.AfterAgentCallback)","u":"afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"afterAgentCallback(Callbacks.AfterAgentCallback)","u":"afterAgentCallback(com.google.adk.agents.Callbacks.AfterAgentCallback)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"afterAgentCallbackSync(Callbacks.AfterAgentCallbackSync)","u":"afterAgentCallbackSync(com.google.adk.agents.Callbacks.AfterAgentCallbackSync)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"afterModelCallback()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"afterModelCallback(Callbacks.AfterModelCallback)","u":"afterModelCallback(com.google.adk.agents.Callbacks.AfterModelCallback)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"afterModelCallbackSync(Callbacks.AfterModelCallbackSync)","u":"afterModelCallbackSync(com.google.adk.agents.Callbacks.AfterModelCallbackSync)"},{"p":"com.google.adk.sessions","c":"GetSessionConfig","l":"afterTimestamp()"},{"p":"com.google.adk.sessions","c":"GetSessionConfig.Builder","l":"afterTimestamp(Instant)","u":"afterTimestamp(java.time.Instant)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"afterToolCallback()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"afterToolCallback(Callbacks.AfterToolCallback)","u":"afterToolCallback(com.google.adk.agents.Callbacks.AfterToolCallback)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"afterToolCallbackSync(Callbacks.AfterToolCallbackSync)","u":"afterToolCallbackSync(com.google.adk.agents.Callbacks.AfterToolCallbackSync)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"agent()"},{"p":"com.google.adk.runner","c":"Runner","l":"agent()"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"agent(BaseAgent)","u":"agent(com.google.adk.agents.BaseAgent)"},{"p":"com.google.adk.agents","c":"ReadonlyContext","l":"agentName()"},{"p":"com.google.adk.tools","c":"AgentTool","l":"AgentTool(BaseAgent, boolean)","u":"%3Cinit%3E(com.google.adk.agents.BaseAgent,boolean)"},{"p":"com.google.adk.flows.llmflows","c":"AgentTransfer","l":"AgentTransfer()","u":"%3Cinit%3E()"},{"p":"com.google.adk.tools","c":"Annotations","l":"Annotations()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"Gemini.Builder","l":"apiClient(Client)","u":"apiClient(com.google.genai.Client)"},{"p":"com.google.adk.sessions","c":"HttpApiClient","l":"apiKey()"},{"p":"com.google.adk.models","c":"Gemini.Builder","l":"apiKey(String)","u":"apiKey(java.lang.String)"},{"p":"com.google.adk.network","c":"ApiResponse","l":"ApiResponse()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"ApiResponse","l":"ApiResponse()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"State","l":"APP_PREFIX"},{"p":"com.google.adk.tools","c":"LoadArtifactsTool","l":"appendArtifactsToLlmRequest(LlmRequest.Builder, ToolContext)","u":"appendArtifactsToLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"appendEvent(Session, Event)","u":"appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)"},{"p":"com.google.adk.sessions","c":"InMemorySessionService","l":"appendEvent(Session, Event)","u":"appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"appendEvent(Session, Event)","u":"appendEvent(com.google.adk.sessions.Session,com.google.adk.events.Event)"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"appendInstructions(List<String>)","u":"appendInstructions(java.util.List)"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"appendTools(List<BaseTool>)","u":"appendTools(java.util.List)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"appName()"},{"p":"com.google.adk.runner","c":"Runner","l":"appName()"},{"p":"com.google.adk.sessions","c":"Session","l":"appName()"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"appName(String)","u":"appName(java.lang.String)"},{"p":"com.google.adk.events","c":"EventActions","l":"artifactDelta()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"artifactDelta(ConcurrentMap<String, Part>)","u":"artifactDelta(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"artifactService()"},{"p":"com.google.adk.runner","c":"Runner","l":"artifactService()"},{"p":"com.google.adk.events","c":"Event","l":"author()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"author(String)","u":"author(java.lang.String)"},{"p":"com.google.adk.flows.llmflows","c":"AutoFlow","l":"AutoFlow()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"BaseAgent(String, String, List<? extends BaseAgent>, Callbacks.BeforeAgentCallback, Callbacks.AfterAgentCallback)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.util.List,com.google.adk.agents.Callbacks.BeforeAgentCallback,com.google.adk.agents.Callbacks.AfterAgentCallback)"},{"p":"com.google.adk.models","c":"BaseLlm","l":"BaseLlm(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.flows.llmflows","c":"BaseLlmFlow","l":"BaseLlmFlow(List<RequestProcessor>, List<ResponseProcessor>)","u":"%3Cinit%3E(java.util.List,java.util.List)"},{"p":"com.google.adk.tools.retrieval","c":"BaseRetrievalTool","l":"BaseRetrievalTool(String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String)"},{"p":"com.google.adk.tools.retrieval","c":"BaseRetrievalTool","l":"BaseRetrievalTool(String, String, boolean)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean)"},{"p":"com.google.adk.tools","c":"BaseTool","l":"BaseTool(String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String)"},{"p":"com.google.adk.tools","c":"BaseTool","l":"BaseTool(String, String, boolean)","u":"%3Cinit%3E(java.lang.String,java.lang.String,boolean)"},{"p":"com.google.adk.flows.llmflows","c":"Basic","l":"Basic()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"beforeAgentCallback()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"beforeAgentCallback(Callbacks.BeforeAgentCallback)","u":"beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"beforeAgentCallback(Callbacks.BeforeAgentCallback)","u":"beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"beforeAgentCallback(Callbacks.BeforeAgentCallback)","u":"beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"beforeAgentCallback(Callbacks.BeforeAgentCallback)","u":"beforeAgentCallback(com.google.adk.agents.Callbacks.BeforeAgentCallback)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"beforeAgentCallbackSync(Callbacks.BeforeAgentCallbackSync)","u":"beforeAgentCallbackSync(com.google.adk.agents.Callbacks.BeforeAgentCallbackSync)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"beforeModelCallback()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"beforeModelCallback(Callbacks.BeforeModelCallback)","u":"beforeModelCallback(com.google.adk.agents.Callbacks.BeforeModelCallback)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"beforeModelCallbackSync(Callbacks.BeforeModelCallbackSync)","u":"beforeModelCallbackSync(com.google.adk.agents.Callbacks.BeforeModelCallbackSync)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"beforeToolCallback()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"beforeToolCallback(Callbacks.BeforeToolCallback)","u":"beforeToolCallback(com.google.adk.agents.Callbacks.BeforeToolCallback)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"beforeToolCallbackSync(Callbacks.BeforeToolCallbackSync)","u":"beforeToolCallbackSync(com.google.adk.agents.Callbacks.BeforeToolCallbackSync)"},{"p":"com.google.adk.agents","c":"RunConfig.StreamingMode","l":"BIDI"},{"p":"com.google.adk.agents","c":"LiveRequest","l":"blob()"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"blob(Blob)","u":"blob(com.google.genai.types.Blob)"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"blob(Optional<Blob>)","u":"blob(java.util.Optional)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"branch()"},{"p":"com.google.adk.events","c":"Event","l":"branch()"},{"p":"com.google.adk.events","c":"Event","l":"branch(Optional<String>)","u":"branch(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"branch(Optional<String>)","u":"branch(java.util.Optional)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"branch(String)","u":"branch(java.lang.String)"},{"p":"com.google.adk.events","c":"Event","l":"branch(String)","u":"branch(java.lang.String)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"branch(String)","u":"branch(java.lang.String)"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"build()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"build()"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"build()"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"build()"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"build()"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"build()"},{"p":"com.google.adk.artifacts","c":"ListArtifactsResponse.Builder","l":"build()"},{"p":"com.google.adk.artifacts","c":"ListArtifactVersionsResponse.Builder","l":"build()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"build()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"build()"},{"p":"com.google.adk.examples","c":"Example.Builder","l":"build()"},{"p":"com.google.adk.models","c":"Gemini.Builder","l":"build()"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"build()"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"build()"},{"p":"com.google.adk.models","c":"Model.Builder","l":"build()"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"build()"},{"p":"com.google.adk.sessions","c":"GetSessionConfig.Builder","l":"build()"},{"p":"com.google.adk.sessions","c":"ListEventsResponse.Builder","l":"build()"},{"p":"com.google.adk.sessions","c":"ListSessionsResponse.Builder","l":"build()"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"build()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters.Builder","l":"build()"},{"p":"com.google.adk.tools","c":"ToolContext.Builder","l":"build()"},{"p":"com.google.adk.agents","c":"LiveRequest","l":"builder()"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"builder()"},{"p":"com.google.adk.agents","c":"LoopAgent","l":"builder()"},{"p":"com.google.adk.agents","c":"ParallelAgent","l":"builder()"},{"p":"com.google.adk.agents","c":"RunConfig","l":"builder()"},{"p":"com.google.adk.agents","c":"SequentialAgent","l":"builder()"},{"p":"com.google.adk.artifacts","c":"ListArtifactsResponse","l":"builder()"},{"p":"com.google.adk.artifacts","c":"ListArtifactVersionsResponse","l":"builder()"},{"p":"com.google.adk.events","c":"Event","l":"builder()"},{"p":"com.google.adk.events","c":"EventActions","l":"builder()"},{"p":"com.google.adk.examples","c":"Example","l":"builder()"},{"p":"com.google.adk.models","c":"Gemini","l":"builder()"},{"p":"com.google.adk.models","c":"LlmRequest","l":"builder()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"builder()"},{"p":"com.google.adk.models","c":"Model","l":"builder()"},{"p":"com.google.adk.models","c":"VertexCredentials","l":"builder()"},{"p":"com.google.adk.sessions","c":"GetSessionConfig","l":"builder()"},{"p":"com.google.adk.sessions","c":"ListEventsResponse","l":"builder()"},{"p":"com.google.adk.sessions","c":"ListSessionsResponse","l":"builder()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters","l":"builder()"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.artifacts","c":"ListArtifactsResponse.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.artifacts","c":"ListArtifactVersionsResponse.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.examples","c":"Example.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"Model.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"GetSessionConfig.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"ListEventsResponse.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"ListSessionsResponse.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters.Builder","l":"Builder()","u":"%3Cinit%3E()"},{"p":"com.google.adk.tools","c":"ToolContext","l":"builder(InvocationContext)","u":"builder(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"RunConfig","l":"builder(RunConfig)","u":"builder(com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.sessions","c":"Session","l":"builder(String)","u":"builder(java.lang.String)"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"Builder(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.examples","c":"ExampleUtils","l":"buildExampleSi(BaseExampleProvider, String)","u":"buildExampleSi(com.google.adk.examples.BaseExampleProvider,java.lang.String)"},{"p":"com.google.adk.tools","c":"FunctionCallingUtils","l":"buildSchemaFromType(Type)","u":"buildSchemaFromType(java.lang.reflect.Type)"},{"p":"com.google.adk.tools","c":"BuiltInCodeExecutionTool","l":"BuiltInCodeExecutionTool()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"Callbacks.AfterAgentCallback","l":"call(CallbackContext)","u":"call(com.google.adk.agents.CallbackContext)"},{"p":"com.google.adk.agents","c":"Callbacks.AfterAgentCallbackSync","l":"call(CallbackContext)","u":"call(com.google.adk.agents.CallbackContext)"},{"p":"com.google.adk.agents","c":"Callbacks.BeforeAgentCallback","l":"call(CallbackContext)","u":"call(com.google.adk.agents.CallbackContext)"},{"p":"com.google.adk.agents","c":"Callbacks.BeforeAgentCallbackSync","l":"call(CallbackContext)","u":"call(com.google.adk.agents.CallbackContext)"},{"p":"com.google.adk.agents","c":"Callbacks.BeforeModelCallback","l":"call(CallbackContext, LlmRequest)","u":"call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.agents","c":"Callbacks.BeforeModelCallbackSync","l":"call(CallbackContext, LlmRequest)","u":"call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.agents","c":"Callbacks.AfterModelCallback","l":"call(CallbackContext, LlmResponse)","u":"call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)"},{"p":"com.google.adk.agents","c":"Callbacks.AfterModelCallbackSync","l":"call(CallbackContext, LlmResponse)","u":"call(com.google.adk.agents.CallbackContext,com.google.adk.models.LlmResponse)"},{"p":"com.google.adk.agents","c":"Callbacks.BeforeToolCallback","l":"call(InvocationContext, BaseTool, Map<String, Object>, ToolContext)","u":"call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.agents","c":"Callbacks.BeforeToolCallbackSync","l":"call(InvocationContext, BaseTool, Map<String, Object>, ToolContext)","u":"call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.agents","c":"Callbacks.AfterToolCallback","l":"call(InvocationContext, BaseTool, Map<String, Object>, ToolContext, Object)","u":"call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)"},{"p":"com.google.adk.agents","c":"Callbacks.AfterToolCallbackSync","l":"call(InvocationContext, BaseTool, Map<String, Object>, ToolContext, Object)","u":"call(com.google.adk.agents.InvocationContext,com.google.adk.tools.BaseTool,java.util.Map,com.google.adk.tools.ToolContext,java.lang.Object)"},{"p":"com.google.adk.agents","c":"CallbackContext","l":"CallbackContext(InvocationContext, EventActions)","u":"%3Cinit%3E(com.google.adk.agents.InvocationContext,com.google.adk.events.EventActions)"},{"p":"com.google.adk.agents","c":"Callbacks","l":"Callbacks()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"Claude","l":"Claude(String, AnthropicClient)","u":"%3Cinit%3E(java.lang.String,com.anthropic.client.AnthropicClient)"},{"p":"com.google.adk.sessions","c":"State","l":"clear()"},{"p":"com.google.adk.agents","c":"LiveRequest","l":"close()"},{"p":"com.google.adk.agents","c":"LiveRequestQueue","l":"close()"},{"p":"com.google.adk.flows.llmflows.audio","c":"SpeechClientInterface","l":"close()"},{"p":"com.google.adk.flows.llmflows.audio","c":"VertexSpeechClient","l":"close()"},{"p":"com.google.adk.models","c":"BaseLlmConnection","l":"close()"},{"p":"com.google.adk.models","c":"GeminiLlmConnection","l":"close()"},{"p":"com.google.adk.network","c":"ApiResponse","l":"close()"},{"p":"com.google.adk.network","c":"HttpApiResponse","l":"close()"},{"p":"com.google.adk.sessions","c":"ApiResponse","l":"close()"},{"p":"com.google.adk.sessions","c":"HttpApiResponse","l":"close()"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"close()"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"close(Boolean)","u":"close(java.lang.Boolean)"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"close(Optional<Boolean>)","u":"close(java.util.Optional)"},{"p":"com.google.adk.models","c":"BaseLlmConnection","l":"close(Throwable)","u":"close(java.lang.Throwable)"},{"p":"com.google.adk.models","c":"GeminiLlmConnection","l":"close(Throwable)","u":"close(java.lang.Throwable)"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"closeSession(Session)","u":"closeSession(com.google.adk.sessions.Session)"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"config()"},{"p":"com.google.adk.models","c":"LlmRequest","l":"config()"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"config(GenerateContentConfig)","u":"config(com.google.genai.types.GenerateContentConfig)"},{"p":"com.google.adk.models","c":"BaseLlm","l":"connect(LlmRequest)","u":"connect(com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.models","c":"Claude","l":"connect(LlmRequest)","u":"connect(com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.models","c":"Gemini","l":"connect(LlmRequest)","u":"connect(com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.sessions","c":"State","l":"containsKey(Object)","u":"containsKey(java.lang.Object)"},{"p":"com.google.adk.sessions","c":"State","l":"containsValue(Object)","u":"containsValue(java.lang.Object)"},{"p":"com.google.adk.agents","c":"LiveRequest","l":"content()"},{"p":"com.google.adk.events","c":"Event","l":"content()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"content()"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"content(Content)","u":"content(com.google.genai.types.Content)"},{"p":"com.google.adk.agents","c":"LiveRequestQueue","l":"content(Content)","u":"content(com.google.genai.types.Content)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"content(Content)","u":"content(com.google.genai.types.Content)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"content(Content)","u":"content(com.google.genai.types.Content)"},{"p":"com.google.adk.agents","c":"LiveRequest.Builder","l":"content(Optional<Content>)","u":"content(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"content(Optional<Content>)","u":"content(java.util.Optional)"},{"p":"com.google.adk.models","c":"LlmRequest","l":"contents()"},{"p":"com.google.adk.flows.llmflows","c":"Contents","l":"Contents()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"contents(List<Content>)","u":"contents(java.util.List)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"convertEventToJson(Event)","u":"convertEventToJson(com.google.adk.events.Event)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"copyOf(InvocationContext)","u":"copyOf(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.tools","c":"AgentTool","l":"create(BaseAgent)","u":"create(com.google.adk.agents.BaseAgent)"},{"p":"com.google.adk.tools","c":"AgentTool","l":"create(BaseAgent, boolean)","u":"create(com.google.adk.agents.BaseAgent,boolean)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"create(BaseSessionService, BaseArtifactService, BaseAgent, Session, LiveRequestQueue, RunConfig)","u":"create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"create(BaseSessionService, BaseArtifactService, String, BaseAgent, Session, Content, RunConfig)","u":"create(com.google.adk.sessions.BaseSessionService,com.google.adk.artifacts.BaseArtifactService,java.lang.String,com.google.adk.agents.BaseAgent,com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.tools","c":"FunctionTool","l":"create(Class<?>, String)","u":"create(java.lang.Class,java.lang.String)"},{"p":"com.google.adk.tools","c":"LongRunningFunctionTool","l":"create(Class<?>, String)","u":"create(java.lang.Class,java.lang.String)"},{"p":"com.google.adk.models","c":"LlmResponse","l":"create(GenerateContentResponse)","u":"create(com.google.genai.types.GenerateContentResponse)"},{"p":"com.google.adk.models","c":"LlmResponse","l":"create(List<Candidate>)","u":"create(java.util.List)"},{"p":"com.google.adk.tools","c":"FunctionTool","l":"create(Method)","u":"create(java.lang.reflect.Method)"},{"p":"com.google.adk.tools","c":"LongRunningFunctionTool","l":"create(Method)","u":"create(java.lang.reflect.Method)"},{"p":"com.google.adk.models","c":"LlmRegistry.LlmFactory","l":"create(String)","u":"create(java.lang.String)"},{"p":"com.google.adk.tools.mcp","c":"McpSessionManager","l":"createSession()"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"createSession(String, String)","u":"createSession(java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"InMemorySessionService","l":"createSession(String, String, ConcurrentMap<String, Object>, String)","u":"createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"createSession(String, String, ConcurrentMap<String, Object>, String)","u":"createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"createSession(String, String, ConcurrentMap<String, Object>, String)","u":"createSession(java.lang.String,java.lang.String,java.util.concurrent.ConcurrentMap,java.lang.String)"},{"p":"com.google.adk.models","c":"VertexCredentials","l":"credentials()"},{"p":"com.google.adk.tools","c":"AgentTool","l":"declaration()"},{"p":"com.google.adk.tools","c":"BaseTool","l":"declaration()"},{"p":"com.google.adk.tools","c":"FunctionTool","l":"declaration()"},{"p":"com.google.adk.tools","c":"LoadArtifactsTool","l":"declaration()"},{"p":"com.google.adk.tools.mcp","c":"McpTool","l":"declaration()"},{"p":"com.google.adk.tools.retrieval","c":"BaseRetrievalTool","l":"declaration()"},{"p":"com.google.adk.sessions","c":"SessionUtils","l":"decodeContent(Content)","u":"decodeContent(com.google.genai.types.Content)"},{"p":"com.google.adk.agents","c":"LlmAgent.IncludeContents","l":"DEFAULT"},{"p":"com.google.adk.artifacts","c":"BaseArtifactService","l":"deleteArtifact(String, String, String, String)","u":"deleteArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.artifacts","c":"GcsArtifactService","l":"deleteArtifact(String, String, String, String)","u":"deleteArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.artifacts","c":"InMemoryArtifactService","l":"deleteArtifact(String, String, String, String)","u":"deleteArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"deleteSession(String, String, String)","u":"deleteSession(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"InMemorySessionService","l":"deleteSession(String, String, String)","u":"deleteSession(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"deleteSession(String, String, String)","u":"deleteSession(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"description()"},{"p":"com.google.adk.tools","c":"Annotations.Schema","l":"description()"},{"p":"com.google.adk.tools","c":"BaseTool","l":"description()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"description(String)","u":"description(java.lang.String)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"description(String)","u":"description(java.lang.String)"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"description(String)","u":"description(java.lang.String)"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"description(String)","u":"description(java.lang.String)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"disallowTransferToParent()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"disallowTransferToParent(boolean)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"disallowTransferToPeers()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"disallowTransferToPeers(boolean)"},{"p":"com.google.adk.sessions","c":"SessionUtils","l":"encodeContent(Content)","u":"encodeContent(com.google.genai.types.Content)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"endInvocation()"},{"p":"com.google.adk.sessions","c":"State","l":"entrySet()"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"equals(Object)","u":"equals(java.lang.Object)"},{"p":"com.google.adk.events","c":"Event","l":"equals(Object)","u":"equals(java.lang.Object)"},{"p":"com.google.adk.events","c":"EventActions","l":"equals(Object)","u":"equals(java.lang.Object)"},{"p":"com.google.adk.sessions","c":"State","l":"equals(Object)","u":"equals(java.lang.Object)"},{"p":"com.google.adk.events","c":"Event","l":"errorCode()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"errorCode()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"errorCode(FinishReason)","u":"errorCode(com.google.genai.types.FinishReason)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"errorCode(FinishReason)","u":"errorCode(com.google.genai.types.FinishReason)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"errorCode(Optional<FinishReason>)","u":"errorCode(java.util.Optional)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"errorCode(Optional<FinishReason>)","u":"errorCode(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event","l":"errorMessage()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"errorMessage()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"errorMessage(Optional<String>)","u":"errorMessage(java.util.Optional)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"errorMessage(Optional<String>)","u":"errorMessage(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"errorMessage(String)","u":"errorMessage(java.lang.String)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"errorMessage(String)","u":"errorMessage(java.lang.String)"},{"p":"com.google.adk.events","c":"EventActions","l":"escalate()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"escalate(boolean)"},{"p":"com.google.adk.agents","c":"CallbackContext","l":"eventActions"},{"p":"com.google.adk.agents","c":"CallbackContext","l":"eventActions()"},{"p":"com.google.adk.events","c":"EventActions","l":"EventActions()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"ListEventsResponse","l":"events()"},{"p":"com.google.adk.sessions","c":"Session","l":"events()"},{"p":"com.google.adk.sessions","c":"ListEventsResponse.Builder","l":"events(List<Event>)","u":"events(java.util.List)"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"events(List<Event>)","u":"events(java.util.List)"},{"p":"com.google.adk.events","c":"EventStream","l":"EventStream(Supplier<Event>)","u":"%3Cinit%3E(java.util.function.Supplier)"},{"p":"com.google.adk.examples","c":"Example","l":"Example()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"exampleProvider()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"exampleProvider(BaseExampleProvider)","u":"exampleProvider(com.google.adk.examples.BaseExampleProvider)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"exampleProvider(Example...)","u":"exampleProvider(com.google.adk.examples.Example...)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"exampleProvider(List<Example>)","u":"exampleProvider(java.util.List)"},{"p":"com.google.adk.flows.llmflows","c":"Examples","l":"Examples()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"executor()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"executor(Executor)","u":"executor(java.util.concurrent.Executor)"},{"p":"com.google.adk.tools","c":"ExitLoopTool","l":"exitLoop(ToolContext)","u":"exitLoop(com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.artifacts","c":"ListArtifactsResponse","l":"filenames()"},{"p":"com.google.adk.artifacts","c":"ListArtifactsResponse.Builder","l":"filenames(List<String>)","u":"filenames(java.util.List)"},{"p":"com.google.adk.events","c":"Event","l":"finalResponse()"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"findAgent(String)","u":"findAgent(java.lang.String)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"findSubAgent(String)","u":"findSubAgent(java.lang.String)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"fromApiEvent(Map<String, Object>)","u":"fromApiEvent(java.util.Map)"},{"p":"com.google.adk.events","c":"Event","l":"fromJson(String)","u":"fromJson(java.lang.String)"},{"p":"com.google.adk.sessions","c":"Session","l":"fromJson(String)","u":"fromJson(java.lang.String)"},{"p":"com.google.adk","c":"JsonBaseModel","l":"fromJsonNode(JsonNode, Class<T>)","u":"fromJsonNode(com.fasterxml.jackson.databind.JsonNode,java.lang.Class)"},{"p":"com.google.adk.agents","c":"LiveRequest","l":"fromJsonString(String)","u":"fromJsonString(java.lang.String)"},{"p":"com.google.adk","c":"JsonBaseModel","l":"fromJsonString(String, Class<T>)","u":"fromJsonString(java.lang.String,java.lang.Class)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"fromServer(ServerParameters)","u":"fromServer(io.modelcontextprotocol.client.transport.ServerParameters)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"fromServer(ServerParameters, ObjectMapper)","u":"fromServer(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"fromServer(SseServerParameters)","u":"fromServer(com.google.adk.tools.mcp.SseServerParameters)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"fromServer(SseServerParameters, ObjectMapper)","u":"fromServer(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)"},{"p":"com.google.adk.tools","c":"ToolContext","l":"functionCallId()"},{"p":"com.google.adk.tools","c":"ToolContext.Builder","l":"functionCallId(String)","u":"functionCallId(java.lang.String)"},{"p":"com.google.adk.tools","c":"ToolContext","l":"functionCallId(String)","u":"functionCallId(java.lang.String)"},{"p":"com.google.adk.tools","c":"FunctionCallingUtils","l":"FunctionCallingUtils()","u":"%3Cinit%3E()"},{"p":"com.google.adk.events","c":"Event","l":"functionCalls()"},{"p":"com.google.adk.events","c":"Event","l":"functionResponses()"},{"p":"com.google.adk.tools","c":"FunctionTool","l":"FunctionTool(Method, boolean)","u":"%3Cinit%3E(java.lang.reflect.Method,boolean)"},{"p":"com.google.adk.artifacts","c":"GcsArtifactService","l":"GcsArtifactService(String, Storage)","u":"%3Cinit%3E(java.lang.String,com.google.cloud.storage.Storage)"},{"p":"com.google.adk.models","c":"Gemini","l":"Gemini(String, Client)","u":"%3Cinit%3E(java.lang.String,com.google.genai.Client)"},{"p":"com.google.adk.models","c":"Gemini","l":"Gemini(String, String)","u":"%3Cinit%3E(java.lang.String,java.lang.String)"},{"p":"com.google.adk.models","c":"Gemini","l":"Gemini(String, VertexCredentials)","u":"%3Cinit%3E(java.lang.String,com.google.adk.models.VertexCredentials)"},{"p":"com.google.adk.flows.llmflows","c":"Functions","l":"generateClientFunctionCallId()"},{"p":"com.google.adk.models","c":"BaseLlm","l":"generateContent(LlmRequest, boolean)","u":"generateContent(com.google.adk.models.LlmRequest,boolean)"},{"p":"com.google.adk.models","c":"Claude","l":"generateContent(LlmRequest, boolean)","u":"generateContent(com.google.adk.models.LlmRequest,boolean)"},{"p":"com.google.adk.models","c":"Gemini","l":"generateContent(LlmRequest, boolean)","u":"generateContent(com.google.adk.models.LlmRequest,boolean)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"generateContentConfig()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"generateContentConfig(GenerateContentConfig)","u":"generateContentConfig(com.google.genai.types.GenerateContentConfig)"},{"p":"com.google.adk.events","c":"Event","l":"generateEventId()"},{"p":"com.google.adk.agents","c":"LiveRequestQueue","l":"get()"},{"p":"com.google.adk.sessions","c":"State","l":"get(Object)","u":"get(java.lang.Object)"},{"p":"com.google.adk.network","c":"ApiResponse","l":"getEntity()"},{"p":"com.google.adk.network","c":"HttpApiResponse","l":"getEntity()"},{"p":"com.google.adk.examples","c":"BaseExampleProvider","l":"getExamples(String)","u":"getExamples(java.lang.String)"},{"p":"com.google.adk.models","c":"LlmRequest","l":"getFirstSystemInstruction()"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"getJsonResponse(ApiResponse)","u":"getJsonResponse(com.google.adk.sessions.ApiResponse)"},{"p":"com.google.adk.sessions","c":"Session","l":"getLastUpdateTimeAsDouble()"},{"p":"com.google.adk.models","c":"LlmRegistry","l":"getLlm(String)","u":"getLlm(java.lang.String)"},{"p":"com.google.adk.flows.llmflows","c":"Functions","l":"getLongRunningFunctionCalls(List<FunctionCall>, Map<String, BaseTool>)","u":"getLongRunningFunctionCalls(java.util.List,java.util.Map)"},{"p":"com.google.adk","c":"JsonBaseModel","l":"getMapper()"},{"p":"com.google.adk.sessions","c":"ApiResponse","l":"getResponseBody()"},{"p":"com.google.adk.sessions","c":"HttpApiResponse","l":"getResponseBody()"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"getSession(String, String, String, Optional<GetSessionConfig>)","u":"getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)"},{"p":"com.google.adk.sessions","c":"InMemorySessionService","l":"getSession(String, String, String, Optional<GetSessionConfig>)","u":"getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"getSession(String, String, String, Optional<GetSessionConfig>)","u":"getSession(java.lang.String,java.lang.String,java.lang.String,java.util.Optional)"},{"p":"com.google.adk.sessions","c":"GetSessionConfig","l":"GetSessionConfig()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"LlmRequest","l":"getSystemInstructions()"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpToolsAndToolsetResult","l":"getTools()"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpToolsAndToolsetResult","l":"getToolset()"},{"p":"com.google.adk","c":"Telemetry","l":"getTracer()"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"globalInstruction()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"globalInstruction(String)","u":"globalInstruction(java.lang.String)"},{"p":"com.google.adk.tools","c":"GoogleSearchTool","l":"GoogleSearchTool()","u":"%3Cinit%3E()"},{"p":"com.google.adk.events","c":"Event","l":"groundingMetadata()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"groundingMetadata()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"groundingMetadata(GroundingMetadata)","u":"groundingMetadata(com.google.genai.types.GroundingMetadata)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"groundingMetadata(GroundingMetadata)","u":"groundingMetadata(com.google.genai.types.GroundingMetadata)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"groundingMetadata(Optional<GroundingMetadata>)","u":"groundingMetadata(java.util.Optional)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"groundingMetadata(Optional<GroundingMetadata>)","u":"groundingMetadata(java.util.Optional)"},{"p":"com.google.adk.flows.llmflows","c":"Functions","l":"handleFunctionCalls(InvocationContext, Event, Map<String, BaseTool>)","u":"handleFunctionCalls(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,java.util.Map)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"hashCode()"},{"p":"com.google.adk.events","c":"Event","l":"hashCode()"},{"p":"com.google.adk.events","c":"EventActions","l":"hashCode()"},{"p":"com.google.adk.sessions","c":"State","l":"hashCode()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters","l":"headers()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters.Builder","l":"headers(Map<String, Object>)","u":"headers(java.util.Map)"},{"p":"com.google.adk.network","c":"HttpApiResponse","l":"HttpApiResponse(Response)","u":"%3Cinit%3E(okhttp3.Response)"},{"p":"com.google.adk.sessions","c":"HttpApiResponse","l":"HttpApiResponse(Response)","u":"%3Cinit%3E(okhttp3.Response)"},{"p":"com.google.adk.events","c":"Event","l":"id()"},{"p":"com.google.adk.sessions","c":"Session","l":"id()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"id(String)","u":"id(java.lang.String)"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"id(String)","u":"id(java.lang.String)"},{"p":"com.google.adk.flows.llmflows","c":"Identity","l":"Identity()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"includeContents()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"includeContents(LlmAgent.IncludeContents)","u":"includeContents(com.google.adk.agents.LlmAgent.IncludeContents)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"incrementLlmCallsCount()"},{"p":"com.google.adk.tools.mcp","c":"McpSessionManager","l":"initializeSession(Object)","u":"initializeSession(java.lang.Object)"},{"p":"com.google.adk.artifacts","c":"InMemoryArtifactService","l":"InMemoryArtifactService()","u":"%3Cinit%3E()"},{"p":"com.google.adk.runner","c":"InMemoryRunner","l":"InMemoryRunner(BaseAgent)","u":"%3Cinit%3E(com.google.adk.agents.BaseAgent)"},{"p":"com.google.adk.runner","c":"InMemoryRunner","l":"InMemoryRunner(BaseAgent, String)","u":"%3Cinit%3E(com.google.adk.agents.BaseAgent,java.lang.String)"},{"p":"com.google.adk.sessions","c":"InMemorySessionService","l":"InMemorySessionService()","u":"%3Cinit%3E()"},{"p":"com.google.adk.examples","c":"Example","l":"input()"},{"p":"com.google.adk.examples","c":"Example.Builder","l":"input(Content)","u":"input(com.google.genai.types.Content)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"inputSchema()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"inputSchema(Schema)","u":"inputSchema(com.google.genai.types.Schema)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"instruction()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"instruction(String)","u":"instruction(java.lang.String)"},{"p":"com.google.adk.flows.llmflows","c":"Instructions","l":"Instructions()","u":"%3Cinit%3E()"},{"p":"com.google.adk.events","c":"Event","l":"interrupted()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"interrupted()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"interrupted(Boolean)","u":"interrupted(java.lang.Boolean)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"interrupted(Boolean)","u":"interrupted(java.lang.Boolean)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"interrupted(Optional<Boolean>)","u":"interrupted(java.util.Optional)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"interrupted(Optional<Boolean>)","u":"interrupted(java.util.Optional)"},{"p":"com.google.adk.agents","c":"ReadonlyContext","l":"invocationContext"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"invocationId()"},{"p":"com.google.adk.agents","c":"ReadonlyContext","l":"invocationId()"},{"p":"com.google.adk.events","c":"Event","l":"invocationId()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"invocationId(String)","u":"invocationId(java.lang.String)"},{"p":"com.google.adk.sessions","c":"State","l":"isEmpty()"},{"p":"com.google.adk","c":"CollectionUtils","l":"isNullOrEmpty(Iterable<T>)","u":"isNullOrEmpty(java.lang.Iterable)"},{"p":"com.google.adk.events","c":"EventStream","l":"iterator()"},{"p":"com.google.adk","c":"Version","l":"JAVA_ADK_VERSION"},{"p":"com.google.adk","c":"JsonBaseModel","l":"JsonBaseModel()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"State","l":"keySet()"},{"p":"com.google.adk.sessions","c":"Session","l":"lastUpdateTime()"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"lastUpdateTime(Instant)","u":"lastUpdateTime(java.time.Instant)"},{"p":"com.google.adk.sessions","c":"Session","l":"lastUpdateTime(Instant)","u":"lastUpdateTime(java.time.Instant)"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"lastUpdateTimeSeconds(double)"},{"p":"com.google.adk.artifacts","c":"BaseArtifactService","l":"listArtifactKeys(String, String, String)","u":"listArtifactKeys(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.artifacts","c":"GcsArtifactService","l":"listArtifactKeys(String, String, String)","u":"listArtifactKeys(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.artifacts","c":"InMemoryArtifactService","l":"listArtifactKeys(String, String, String)","u":"listArtifactKeys(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.tools","c":"ToolContext","l":"listArtifacts()"},{"p":"com.google.adk.artifacts","c":"ListArtifactsResponse","l":"ListArtifactsResponse()","u":"%3Cinit%3E()"},{"p":"com.google.adk.artifacts","c":"ListArtifactVersionsResponse","l":"ListArtifactVersionsResponse()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"listEvents(String, String, String)","u":"listEvents(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"InMemorySessionService","l":"listEvents(String, String, String)","u":"listEvents(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"listEvents(String, String, String)","u":"listEvents(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"ListEventsResponse","l":"ListEventsResponse()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"BaseSessionService","l":"listSessions(String, String)","u":"listSessions(java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"InMemorySessionService","l":"listSessions(String, String)","u":"listSessions(java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"listSessions(String, String)","u":"listSessions(java.lang.String,java.lang.String)"},{"p":"com.google.adk.sessions","c":"ListSessionsResponse","l":"ListSessionsResponse()","u":"%3Cinit%3E()"},{"p":"com.google.adk.artifacts","c":"BaseArtifactService","l":"listVersions(String, String, String, String)","u":"listVersions(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.artifacts","c":"GcsArtifactService","l":"listVersions(String, String, String, String)","u":"listVersions(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.artifacts","c":"InMemoryArtifactService","l":"listVersions(String, String, String, String)","u":"listVersions(java.lang.String,java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.models","c":"LlmRequest","l":"liveConnectConfig()"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"liveConnectConfig(LiveConnectConfig)","u":"liveConnectConfig(com.google.genai.types.LiveConnectConfig)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"liveRequestQueue()"},{"p":"com.google.adk.agents","c":"LiveRequestQueue","l":"LiveRequestQueue()","u":"%3Cinit%3E()"},{"p":"com.google.adk.exceptions","c":"LlmCallsLimitExceededException","l":"LlmCallsLimitExceededException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.models","c":"LlmRegistry","l":"LlmRegistry()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"LlmRequest","l":"LlmRequest()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"CallbackContext","l":"loadArtifact(String, Optional<Integer>)","u":"loadArtifact(java.lang.String,java.util.Optional)"},{"p":"com.google.adk.artifacts","c":"BaseArtifactService","l":"loadArtifact(String, String, String, String, Optional<Integer>)","u":"loadArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Optional)"},{"p":"com.google.adk.artifacts","c":"GcsArtifactService","l":"loadArtifact(String, String, String, String, Optional<Integer>)","u":"loadArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Optional)"},{"p":"com.google.adk.artifacts","c":"InMemoryArtifactService","l":"loadArtifact(String, String, String, String, Optional<Integer>)","u":"loadArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,java.util.Optional)"},{"p":"com.google.adk.tools","c":"LoadArtifactsTool","l":"LoadArtifactsTool()","u":"%3Cinit%3E()"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"loadTools()"},{"p":"com.google.adk.models","c":"VertexCredentials","l":"location()"},{"p":"com.google.adk.sessions","c":"HttpApiClient","l":"location()"},{"p":"com.google.adk.tools","c":"BaseTool","l":"longRunning()"},{"p":"com.google.adk.events","c":"Event","l":"longRunningToolIds()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"longRunningToolIds(Optional<Set<String>>)","u":"longRunningToolIds(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"longRunningToolIds(Set<String>)","u":"longRunningToolIds(java.util.Set)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"maxIterations(int)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"maxIterations(Optional<Integer>)","u":"maxIterations(java.util.Optional)"},{"p":"com.google.adk.agents","c":"RunConfig","l":"maxLlmCalls()"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpInitializationException","l":"McpInitializationException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpInitializationException","l":"McpInitializationException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"com.google.adk.tools.mcp","c":"McpSessionManager","l":"McpSessionManager(Object)","u":"%3Cinit%3E(java.lang.Object)"},{"p":"com.google.adk.tools.mcp","c":"McpTool","l":"McpTool(McpSchema.Tool, McpSyncClient, McpSessionManager)","u":"%3Cinit%3E(io.modelcontextprotocol.spec.McpSchema.Tool,io.modelcontextprotocol.client.McpSyncClient,com.google.adk.tools.mcp.McpSessionManager)"},{"p":"com.google.adk.tools.mcp","c":"McpTool","l":"McpTool(McpSchema.Tool, McpSyncClient, McpSessionManager, ObjectMapper)","u":"%3Cinit%3E(io.modelcontextprotocol.spec.McpSchema.Tool,io.modelcontextprotocol.client.McpSyncClient,com.google.adk.tools.mcp.McpSessionManager,com.fasterxml.jackson.databind.ObjectMapper)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpToolLoadingException","l":"McpToolLoadingException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpToolLoadingException","l":"McpToolLoadingException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpToolsAndToolsetResult","l":"McpToolsAndToolsetResult(List<McpTool>, McpToolset)","u":"%3Cinit%3E(java.util.List,com.google.adk.tools.mcp.McpToolset)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"McpToolset(ServerParameters)","u":"%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"McpToolset(ServerParameters, ObjectMapper)","u":"%3Cinit%3E(io.modelcontextprotocol.client.transport.ServerParameters,com.fasterxml.jackson.databind.ObjectMapper)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"McpToolset(SseServerParameters)","u":"%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset","l":"McpToolset(SseServerParameters, ObjectMapper)","u":"%3Cinit%3E(com.google.adk.tools.mcp.SseServerParameters,com.fasterxml.jackson.databind.ObjectMapper)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpToolsetException","l":"McpToolsetException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.tools.mcp","c":"McpToolset.McpToolsetException","l":"McpToolsetException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"com.google.adk.sessions","c":"HttpApiClient","l":"MEDIA_TYPE_APPLICATION_JSON"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"merge(EventActions)","u":"merge(com.google.adk.events.EventActions)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"model()"},{"p":"com.google.adk.models","c":"BaseLlm","l":"model()"},{"p":"com.google.adk.models","c":"LlmRequest","l":"model()"},{"p":"com.google.adk.models","c":"Model","l":"model()"},{"p":"com.google.adk.models","c":"Model","l":"Model()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"model(BaseLlm)","u":"model(com.google.adk.models.BaseLlm)"},{"p":"com.google.adk.models","c":"Model.Builder","l":"model(BaseLlm)","u":"model(com.google.adk.models.BaseLlm)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"model(String)","u":"model(java.lang.String)"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"model(String)","u":"model(java.lang.String)"},{"p":"com.google.adk.models","c":"Model","l":"modelName()"},{"p":"com.google.adk.models","c":"Gemini.Builder","l":"modelName(String)","u":"modelName(java.lang.String)"},{"p":"com.google.adk.models","c":"Model.Builder","l":"modelName(String)","u":"modelName(java.lang.String)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"name()"},{"p":"com.google.adk.tools","c":"Annotations.Schema","l":"name()"},{"p":"com.google.adk.tools","c":"BaseTool","l":"name()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"name(String)","u":"name(java.lang.String)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"name(String)","u":"name(java.lang.String)"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"name(String)","u":"name(java.lang.String)"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"name(String)","u":"name(java.lang.String)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"newInvocationContextId()"},{"p":"com.google.adk.sessions","c":"ListEventsResponse","l":"nextPageToken()"},{"p":"com.google.adk.sessions","c":"ListEventsResponse.Builder","l":"nextPageToken(String)","u":"nextPageToken(java.lang.String)"},{"p":"com.google.adk.agents","c":"LlmAgent.IncludeContents","l":"NONE"},{"p":"com.google.adk.agents","c":"RunConfig.StreamingMode","l":"NONE"},{"p":"com.google.adk.sessions","c":"GetSessionConfig","l":"numRecentEvents()"},{"p":"com.google.adk.sessions","c":"GetSessionConfig.Builder","l":"numRecentEvents(int)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of()"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V)","u":"of(K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V)","u":"of(K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V)","u":"of(K,V,K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V, K, V)","u":"of(K,V,K,V,K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V, K, V, K, V)","u":"of(K,V,K,V,K,V,K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V, K, V, K, V, K, V)","u":"of(K,V,K,V,K,V,K,V,K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V, K, V, K, V, K, V, K, V)","u":"of(K,V,K,V,K,V,K,V,K,V,K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V)","u":"of(K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V)","u":"of(K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V)"},{"p":"com.google.adk.utils","c":"Pairs","l":"of(K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V, K, V)","u":"of(K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V,K,V)"},{"p":"com.google.adk.examples","c":"Example","l":"output()"},{"p":"com.google.adk.examples","c":"Example.Builder","l":"output(List<Content>)","u":"output(java.util.List)"},{"p":"com.google.adk.agents","c":"RunConfig","l":"outputAudioTranscription()"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"outputKey()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"outputKey(String)","u":"outputKey(java.lang.String)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"outputSchema()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"outputSchema(Schema)","u":"outputSchema(com.google.genai.types.Schema)"},{"p":"com.google.adk.models","c":"LlmRequest.Builder","l":"outputSchema(Schema)","u":"outputSchema(com.google.genai.types.Schema)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"parentAgent()"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"parentAgent(BaseAgent)","u":"parentAgent(com.google.adk.agents.BaseAgent)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"parseReasoningEngineId(String)","u":"parseReasoningEngineId(java.lang.String)"},{"p":"com.google.adk.events","c":"Event","l":"partial()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"partial()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"partial(Boolean)","u":"partial(java.lang.Boolean)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"partial(Boolean)","u":"partial(java.lang.Boolean)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"partial(Optional<Boolean>)","u":"partial(java.util.Optional)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"partial(Optional<Boolean>)","u":"partial(java.util.Optional)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"planning()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"planning(boolean)"},{"p":"com.google.adk.flows.llmflows","c":"Functions","l":"populateClientFunctionCallId(Event)","u":"populateClientFunctionCallId(com.google.adk.events.Event)"},{"p":"com.google.adk.flows.llmflows","c":"BaseLlmFlow","l":"postprocess(InvocationContext, Event, LlmRequest, LlmResponse)","u":"postprocess(com.google.adk.agents.InvocationContext,com.google.adk.events.Event,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)"},{"p":"com.google.adk.flows.llmflows","c":"BaseLlmFlow","l":"preprocess(InvocationContext, LlmRequest)","u":"preprocess(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.tools","c":"BaseTool","l":"processLlmRequest(LlmRequest.Builder, ToolContext)","u":"processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.tools","c":"BuiltInCodeExecutionTool","l":"processLlmRequest(LlmRequest.Builder, ToolContext)","u":"processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.tools","c":"GoogleSearchTool","l":"processLlmRequest(LlmRequest.Builder, ToolContext)","u":"processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.tools","c":"LoadArtifactsTool","l":"processLlmRequest(LlmRequest.Builder, ToolContext)","u":"processLlmRequest(com.google.adk.models.LlmRequest.Builder,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.flows.llmflows","c":"AgentTransfer","l":"processRequest(InvocationContext, LlmRequest)","u":"processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.flows.llmflows","c":"Basic","l":"processRequest(InvocationContext, LlmRequest)","u":"processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.flows.llmflows","c":"Contents","l":"processRequest(InvocationContext, LlmRequest)","u":"processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.flows.llmflows","c":"Examples","l":"processRequest(InvocationContext, LlmRequest)","u":"processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.flows.llmflows","c":"Identity","l":"processRequest(InvocationContext, LlmRequest)","u":"processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.flows.llmflows","c":"Instructions","l":"processRequest(InvocationContext, LlmRequest)","u":"processRequest(com.google.adk.agents.InvocationContext,com.google.adk.models.LlmRequest)"},{"p":"com.google.adk.models","c":"VertexCredentials","l":"project()"},{"p":"com.google.adk.sessions","c":"HttpApiClient","l":"project()"},{"p":"com.google.adk.sessions","c":"State","l":"put(String, Object)","u":"put(java.lang.String,java.lang.Object)"},{"p":"com.google.adk.sessions","c":"State","l":"putAll(Map<? extends String, ? extends Object>)","u":"putAll(java.util.Map)"},{"p":"com.google.adk.sessions","c":"State","l":"putIfAbsent(String, Object)","u":"putIfAbsent(java.lang.String,java.lang.Object)"},{"p":"com.google.adk.agents","c":"ReadonlyContext","l":"ReadonlyContext(InvocationContext)","u":"%3Cinit%3E(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"LiveRequestQueue","l":"realtime(Blob)","u":"realtime(com.google.genai.types.Blob)"},{"p":"com.google.adk.models","c":"BaseLlmConnection","l":"receive()"},{"p":"com.google.adk.models","c":"GeminiLlmConnection","l":"receive()"},{"p":"com.google.adk.flows.llmflows.audio","c":"SpeechClientInterface","l":"recognize(RecognitionConfig, RecognitionAudio)","u":"recognize(com.google.cloud.speech.v1.RecognitionConfig,com.google.cloud.speech.v1.RecognitionAudio)"},{"p":"com.google.adk.flows.llmflows.audio","c":"VertexSpeechClient","l":"recognize(RecognitionConfig, RecognitionAudio)","u":"recognize(com.google.cloud.speech.v1.RecognitionConfig,com.google.cloud.speech.v1.RecognitionAudio)"},{"p":"com.google.adk.models","c":"LlmRegistry","l":"registerLlm(String, LlmRegistry.LlmFactory)","u":"registerLlm(java.lang.String,com.google.adk.models.LlmRegistry.LlmFactory)"},{"p":"com.google.adk.sessions","c":"State","l":"remove(Object)","u":"remove(java.lang.Object)"},{"p":"com.google.adk.sessions","c":"State","l":"remove(Object, Object)","u":"remove(java.lang.Object,java.lang.Object)"},{"p":"com.google.adk.sessions","c":"State","l":"replace(String, Object)","u":"replace(java.lang.String,java.lang.Object)"},{"p":"com.google.adk.sessions","c":"State","l":"replace(String, Object, Object)","u":"replace(java.lang.String,java.lang.Object,java.lang.Object)"},{"p":"com.google.adk.flows.llmflows","c":"SingleFlow","l":"REQUEST_PROCESSORS"},{"p":"com.google.adk.sessions","c":"HttpApiClient","l":"request(String, String, String)","u":"request(java.lang.String,java.lang.String,java.lang.String)"},{"p":"com.google.adk.events","c":"EventActions","l":"requestedAuthConfigs()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"requestedAuthConfigs(ConcurrentMap<String, ConcurrentMap<String, Object>>)","u":"requestedAuthConfigs(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.flows.llmflows","c":"BaseLlmFlow","l":"requestProcessors"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"resolvedModel()"},{"p":"com.google.adk.flows.llmflows","c":"SingleFlow","l":"RESPONSE_PROCESSORS"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"response(GenerateContentResponse)","u":"response(com.google.genai.types.GenerateContentResponse)"},{"p":"com.google.adk.agents","c":"RunConfig","l":"responseModalities()"},{"p":"com.google.adk.flows.llmflows","c":"BaseLlmFlow","l":"responseProcessors"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"rootAgent()"},{"p":"com.google.adk.flows","c":"BaseFlow","l":"run(InvocationContext)","u":"run(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.flows.llmflows","c":"BaseLlmFlow","l":"run(InvocationContext)","u":"run(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"runAsync(InvocationContext)","u":"runAsync(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.tools","c":"AgentTool","l":"runAsync(Map<String, Object>, ToolContext)","u":"runAsync(java.util.Map,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.tools","c":"BaseTool","l":"runAsync(Map<String, Object>, ToolContext)","u":"runAsync(java.util.Map,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.tools","c":"FunctionTool","l":"runAsync(Map<String, Object>, ToolContext)","u":"runAsync(java.util.Map,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.tools","c":"LoadArtifactsTool","l":"runAsync(Map<String, Object>, ToolContext)","u":"runAsync(java.util.Map,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.tools.mcp","c":"McpTool","l":"runAsync(Map<String, Object>, ToolContext)","u":"runAsync(java.util.Map,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.runner","c":"Runner","l":"runAsync(Session, Content, RunConfig)","u":"runAsync(com.google.adk.sessions.Session,com.google.genai.types.Content,com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.runner","c":"Runner","l":"runAsync(String, String, Content)","u":"runAsync(java.lang.String,java.lang.String,com.google.genai.types.Content)"},{"p":"com.google.adk.runner","c":"Runner","l":"runAsync(String, String, Content, RunConfig)","u":"runAsync(java.lang.String,java.lang.String,com.google.genai.types.Content,com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"runAsyncImpl(InvocationContext)","u":"runAsyncImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"runAsyncImpl(InvocationContext)","u":"runAsyncImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"LoopAgent","l":"runAsyncImpl(InvocationContext)","u":"runAsyncImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"ParallelAgent","l":"runAsyncImpl(InvocationContext)","u":"runAsyncImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"SequentialAgent","l":"runAsyncImpl(InvocationContext)","u":"runAsyncImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"runConfig()"},{"p":"com.google.adk.agents","c":"RunConfig","l":"RunConfig()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"runLive(InvocationContext)","u":"runLive(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.flows","c":"BaseFlow","l":"runLive(InvocationContext)","u":"runLive(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.flows.llmflows","c":"BaseLlmFlow","l":"runLive(InvocationContext)","u":"runLive(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.runner","c":"Runner","l":"runLive(Session, LiveRequestQueue, RunConfig)","u":"runLive(com.google.adk.sessions.Session,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.runner","c":"Runner","l":"runLive(String, String, LiveRequestQueue, RunConfig)","u":"runLive(java.lang.String,java.lang.String,com.google.adk.agents.LiveRequestQueue,com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"runLiveImpl(InvocationContext)","u":"runLiveImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"runLiveImpl(InvocationContext)","u":"runLiveImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"LoopAgent","l":"runLiveImpl(InvocationContext)","u":"runLiveImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"ParallelAgent","l":"runLiveImpl(InvocationContext)","u":"runLiveImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.agents","c":"SequentialAgent","l":"runLiveImpl(InvocationContext)","u":"runLiveImpl(com.google.adk.agents.InvocationContext)"},{"p":"com.google.adk.runner","c":"Runner","l":"Runner(BaseAgent, String, BaseArtifactService, BaseSessionService)","u":"%3Cinit%3E(com.google.adk.agents.BaseAgent,java.lang.String,com.google.adk.artifacts.BaseArtifactService,com.google.adk.sessions.BaseSessionService)"},{"p":"com.google.adk.runner","c":"Runner","l":"runWithSessionId(String, Content, RunConfig)","u":"runWithSessionId(java.lang.String,com.google.genai.types.Content,com.google.adk.agents.RunConfig)"},{"p":"com.google.adk.agents","c":"CallbackContext","l":"saveArtifact(String, Part)","u":"saveArtifact(java.lang.String,com.google.genai.types.Part)"},{"p":"com.google.adk.artifacts","c":"BaseArtifactService","l":"saveArtifact(String, String, String, String, Part)","u":"saveArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.google.genai.types.Part)"},{"p":"com.google.adk.artifacts","c":"GcsArtifactService","l":"saveArtifact(String, String, String, String, Part)","u":"saveArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.google.genai.types.Part)"},{"p":"com.google.adk.artifacts","c":"InMemoryArtifactService","l":"saveArtifact(String, String, String, String, Part)","u":"saveArtifact(java.lang.String,java.lang.String,java.lang.String,java.lang.String,com.google.genai.types.Part)"},{"p":"com.google.adk.agents","c":"RunConfig","l":"saveInputBlobsAsArtifacts()"},{"p":"com.google.adk.agents","c":"LiveRequestQueue","l":"send(LiveRequest)","u":"send(com.google.adk.agents.LiveRequest)"},{"p":"com.google.adk.models","c":"BaseLlmConnection","l":"sendContent(Content)","u":"sendContent(com.google.genai.types.Content)"},{"p":"com.google.adk.models","c":"GeminiLlmConnection","l":"sendContent(Content)","u":"sendContent(com.google.genai.types.Content)"},{"p":"com.google.adk.models","c":"BaseLlmConnection","l":"sendHistory(List<Content>)","u":"sendHistory(java.util.List)"},{"p":"com.google.adk.models","c":"GeminiLlmConnection","l":"sendHistory(List<Content>)","u":"sendHistory(java.util.List)"},{"p":"com.google.adk.models","c":"BaseLlmConnection","l":"sendRealtime(Blob)","u":"sendRealtime(com.google.genai.types.Blob)"},{"p":"com.google.adk.models","c":"GeminiLlmConnection","l":"sendRealtime(Blob)","u":"sendRealtime(com.google.genai.types.Blob)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"session()"},{"p":"com.google.adk.sessions","c":"SessionException","l":"SessionException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.sessions","c":"SessionException","l":"SessionException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"com.google.adk.sessions","c":"SessionException","l":"SessionException(Throwable)","u":"%3Cinit%3E(java.lang.Throwable)"},{"p":"com.google.adk.sessions","c":"ListSessionsResponse","l":"sessionIds()"},{"p":"com.google.adk.sessions","c":"SessionNotFoundException","l":"SessionNotFoundException(String)","u":"%3Cinit%3E(java.lang.String)"},{"p":"com.google.adk.sessions","c":"SessionNotFoundException","l":"SessionNotFoundException(String, Throwable)","u":"%3Cinit%3E(java.lang.String,java.lang.Throwable)"},{"p":"com.google.adk.sessions","c":"ListSessionsResponse","l":"sessions()"},{"p":"com.google.adk.sessions","c":"ListSessionsResponse.Builder","l":"sessions(List<Session>)","u":"sessions(java.util.List)"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"sessionService()"},{"p":"com.google.adk.runner","c":"Runner","l":"sessionService()"},{"p":"com.google.adk.sessions","c":"SessionUtils","l":"SessionUtils()","u":"%3Cinit%3E()"},{"p":"com.google.adk.events","c":"Event","l":"setActions(EventActions)","u":"setActions(com.google.adk.events.EventActions)"},{"p":"com.google.adk.tools","c":"ToolContext","l":"setActions(EventActions)","u":"setActions(com.google.adk.events.EventActions)"},{"p":"com.google.adk.events","c":"EventActions","l":"setArtifactDelta(ConcurrentMap<String, Part>)","u":"setArtifactDelta(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.events","c":"Event","l":"setAuthor(String)","u":"setAuthor(java.lang.String)"},{"p":"com.google.adk.events","c":"Event","l":"setContent(Optional<Content>)","u":"setContent(java.util.Optional)"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"setCredentials(GoogleCredentials)","u":"setCredentials(com.google.auth.oauth2.GoogleCredentials)"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"setCredentials(Optional<GoogleCredentials>)","u":"setCredentials(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event","l":"setErrorCode(Optional<FinishReason>)","u":"setErrorCode(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event","l":"setErrorMessage(Optional<String>)","u":"setErrorMessage(java.util.Optional)"},{"p":"com.google.adk.events","c":"EventActions","l":"setEscalate(boolean)"},{"p":"com.google.adk.events","c":"EventActions","l":"setEscalate(Optional<Boolean>)","u":"setEscalate(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event","l":"setGroundingMetadata(Optional<GroundingMetadata>)","u":"setGroundingMetadata(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event","l":"setId(String)","u":"setId(java.lang.String)"},{"p":"com.google.adk.events","c":"Event","l":"setInterrupted(Optional<Boolean>)","u":"setInterrupted(java.util.Optional)"},{"p":"com.google.adk.events","c":"Event","l":"setInvocationId(String)","u":"setInvocationId(java.lang.String)"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"setLocation(Optional<String>)","u":"setLocation(java.util.Optional)"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"setLocation(String)","u":"setLocation(java.lang.String)"},{"p":"com.google.adk.events","c":"Event","l":"setLongRunningToolIds(Optional<Set<String>>)","u":"setLongRunningToolIds(java.util.Optional)"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"setMaxLlmCalls(int)"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"setOutputAudioTranscription(AudioTranscriptionConfig)","u":"setOutputAudioTranscription(com.google.genai.types.AudioTranscriptionConfig)"},{"p":"com.google.adk.events","c":"Event","l":"setPartial(Optional<Boolean>)","u":"setPartial(java.util.Optional)"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"setProject(Optional<String>)","u":"setProject(java.util.Optional)"},{"p":"com.google.adk.models","c":"VertexCredentials.Builder","l":"setProject(String)","u":"setProject(java.lang.String)"},{"p":"com.google.adk.events","c":"EventActions","l":"setRequestedAuthConfigs(ConcurrentMap<String, ConcurrentMap<String, Object>>)","u":"setRequestedAuthConfigs(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"setResponseModalities(Iterable<Modality>)","u":"setResponseModalities(java.lang.Iterable)"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"setSaveInputBlobsAsArtifacts(boolean)"},{"p":"com.google.adk.events","c":"EventActions","l":"setSkipSummarization(boolean)"},{"p":"com.google.adk.events","c":"EventActions","l":"setSkipSummarization(Boolean)","u":"setSkipSummarization(java.lang.Boolean)"},{"p":"com.google.adk.events","c":"EventActions","l":"setSkipSummarization(Optional<Boolean>)","u":"setSkipSummarization(java.util.Optional)"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"setSpeechConfig(SpeechConfig)","u":"setSpeechConfig(com.google.genai.types.SpeechConfig)"},{"p":"com.google.adk.events","c":"EventActions","l":"setStateDelta(ConcurrentMap<String, Object>)","u":"setStateDelta(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.agents","c":"RunConfig.Builder","l":"setStreamingMode(RunConfig.StreamingMode)","u":"setStreamingMode(com.google.adk.agents.RunConfig.StreamingMode)"},{"p":"com.google.adk.events","c":"Event","l":"setTimestamp(long)"},{"p":"com.google.adk.events","c":"EventActions","l":"setTransferToAgent(Optional<String>)","u":"setTransferToAgent(java.util.Optional)"},{"p":"com.google.adk.events","c":"EventActions","l":"setTransferToAgent(String)","u":"setTransferToAgent(java.lang.String)"},{"p":"com.google.adk.events","c":"Event","l":"setTurnComplete(Optional<Boolean>)","u":"setTurnComplete(java.util.Optional)"},{"p":"com.google.adk.agents","c":"LiveRequest","l":"shouldClose()"},{"p":"com.google.adk.flows.llmflows","c":"SingleFlow","l":"SingleFlow()","u":"%3Cinit%3E()"},{"p":"com.google.adk.flows.llmflows","c":"SingleFlow","l":"SingleFlow(List<RequestProcessor>, List<ResponseProcessor>)","u":"%3Cinit%3E(java.util.List,java.util.List)"},{"p":"com.google.adk.sessions","c":"State","l":"size()"},{"p":"com.google.adk.events","c":"EventActions","l":"skipSummarization()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"skipSummarization(boolean)"},{"p":"com.google.adk.agents","c":"RunConfig","l":"speechConfig()"},{"p":"com.google.adk.agents","c":"RunConfig.StreamingMode","l":"SSE"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters","l":"sseReadTimeout()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters.Builder","l":"sseReadTimeout(Duration)","u":"sseReadTimeout(java.time.Duration)"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters","l":"SseServerParameters()","u":"%3Cinit%3E()"},{"p":"com.google.adk.agents","c":"CallbackContext","l":"state()"},{"p":"com.google.adk.agents","c":"ReadonlyContext","l":"state()"},{"p":"com.google.adk.sessions","c":"Session","l":"state()"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"state(ConcurrentMap<String, Object>)","u":"state(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.sessions","c":"State","l":"State(ConcurrentMap<String, Object>)","u":"%3Cinit%3E(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.sessions","c":"State","l":"State(ConcurrentMap<String, Object>, ConcurrentMap<String, Object>)","u":"%3Cinit%3E(java.util.concurrent.ConcurrentMap,java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"state(State)","u":"state(com.google.adk.sessions.State)"},{"p":"com.google.adk.events","c":"EventActions","l":"stateDelta()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"stateDelta(ConcurrentMap<String, Object>)","u":"stateDelta(java.util.concurrent.ConcurrentMap)"},{"p":"com.google.adk.agents","c":"RunConfig","l":"streamingMode()"},{"p":"com.google.adk.events","c":"Event","l":"stringifyContent()"},{"p":"com.google.adk.agents","c":"BaseAgent","l":"subAgents()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"subAgents(BaseAgent...)","u":"subAgents(com.google.adk.agents.BaseAgent...)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"subAgents(BaseAgent...)","u":"subAgents(com.google.adk.agents.BaseAgent...)"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"subAgents(BaseAgent...)","u":"subAgents(com.google.adk.agents.BaseAgent...)"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"subAgents(BaseAgent...)","u":"subAgents(com.google.adk.agents.BaseAgent...)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"subAgents(List<? extends BaseAgent>)","u":"subAgents(java.util.List)"},{"p":"com.google.adk.agents","c":"LoopAgent.Builder","l":"subAgents(List<? extends BaseAgent>)","u":"subAgents(java.util.List)"},{"p":"com.google.adk.agents","c":"ParallelAgent.Builder","l":"subAgents(List<? extends BaseAgent>)","u":"subAgents(java.util.List)"},{"p":"com.google.adk.agents","c":"SequentialAgent.Builder","l":"subAgents(List<? extends BaseAgent>)","u":"subAgents(java.util.List)"},{"p":"com.google.adk.sessions","c":"State","l":"TEMP_PREFIX"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters","l":"timeout()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters.Builder","l":"timeout(Duration)","u":"timeout(java.time.Duration)"},{"p":"com.google.adk.events","c":"Event","l":"timestamp()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"timestamp(long)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"timestamp(Optional<Long>)","u":"timestamp(java.util.Optional)"},{"p":"com.google.adk.agents","c":"LiveRequest","l":"toBuilder()"},{"p":"com.google.adk.events","c":"Event","l":"toBuilder()"},{"p":"com.google.adk.events","c":"EventActions","l":"toBuilder()"},{"p":"com.google.adk.examples","c":"Example","l":"toBuilder()"},{"p":"com.google.adk.models","c":"LlmRequest","l":"toBuilder()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"toBuilder()"},{"p":"com.google.adk.models","c":"Model","l":"toBuilder()"},{"p":"com.google.adk.tools","c":"ToolContext","l":"toBuilder()"},{"p":"com.google.adk.tools.mcp","c":"McpTool","l":"toGeminiSchema(McpSchema.JsonSchema)","u":"toGeminiSchema(io.modelcontextprotocol.spec.McpSchema.JsonSchema)"},{"p":"com.google.adk","c":"JsonBaseModel","l":"toJson()"},{"p":"com.google.adk","c":"JsonBaseModel","l":"toJsonNode(Object)","u":"toJsonNode(java.lang.Object)"},{"p":"com.google.adk","c":"JsonBaseModel","l":"toJsonString(Object)","u":"toJsonString(java.lang.Object)"},{"p":"com.google.adk.agents","c":"LlmAgent","l":"tools()"},{"p":"com.google.adk.models","c":"LlmRequest","l":"tools()"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"tools(BaseTool...)","u":"tools(com.google.adk.tools.BaseTool...)"},{"p":"com.google.adk.agents","c":"LlmAgent.Builder","l":"tools(List<? extends BaseTool>)","u":"tools(java.util.List)"},{"p":"com.google.adk","c":"Telemetry","l":"traceCallLlm(InvocationContext, String, LlmRequest, LlmResponse)","u":"traceCallLlm(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.models.LlmRequest,com.google.adk.models.LlmResponse)"},{"p":"com.google.adk","c":"Telemetry","l":"traceSendData(InvocationContext, String, List<Content>)","u":"traceSendData(com.google.adk.agents.InvocationContext,java.lang.String,java.util.List)"},{"p":"com.google.adk","c":"Telemetry","l":"traceToolCall(Map<String, Object>)","u":"traceToolCall(java.util.Map)"},{"p":"com.google.adk","c":"Telemetry","l":"traceToolResponse(InvocationContext, String, Event)","u":"traceToolResponse(com.google.adk.agents.InvocationContext,java.lang.String,com.google.adk.events.Event)"},{"p":"com.google.adk.events","c":"EventActions","l":"transferToAgent()"},{"p":"com.google.adk.events","c":"EventActions.Builder","l":"transferToAgent(String)","u":"transferToAgent(java.lang.String)"},{"p":"com.google.adk.flows.llmflows","c":"AgentTransfer","l":"transferToAgent(String, ToolContext)","u":"transferToAgent(java.lang.String,com.google.adk.tools.ToolContext)"},{"p":"com.google.adk.events","c":"Event","l":"turnComplete()"},{"p":"com.google.adk.models","c":"LlmResponse","l":"turnComplete()"},{"p":"com.google.adk.events","c":"Event.Builder","l":"turnComplete(Boolean)","u":"turnComplete(java.lang.Boolean)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"turnComplete(Boolean)","u":"turnComplete(java.lang.Boolean)"},{"p":"com.google.adk.events","c":"Event.Builder","l":"turnComplete(Optional<Boolean>)","u":"turnComplete(java.util.Optional)"},{"p":"com.google.adk.models","c":"LlmResponse.Builder","l":"turnComplete(Optional<Boolean>)","u":"turnComplete(java.util.Optional)"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters","l":"url()"},{"p":"com.google.adk.tools.mcp","c":"SseServerParameters.Builder","l":"url(String)","u":"url(java.lang.String)"},{"p":"com.google.adk.sessions","c":"State","l":"USER_PREFIX"},{"p":"com.google.adk.agents","c":"CallbackContext","l":"userContent()"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"userContent()"},{"p":"com.google.adk.agents","c":"InvocationContext","l":"userId()"},{"p":"com.google.adk.sessions","c":"Session","l":"userId()"},{"p":"com.google.adk.sessions","c":"Session.Builder","l":"userId(String)","u":"userId(java.lang.String)"},{"p":"com.google.adk","c":"SchemaUtils","l":"validateMapOnSchema(Map<String, Object>, Schema, Boolean)","u":"validateMapOnSchema(java.util.Map,com.google.genai.types.Schema,java.lang.Boolean)"},{"p":"com.google.adk","c":"SchemaUtils","l":"validateOutputSchema(String, Schema)","u":"validateOutputSchema(java.lang.String,com.google.genai.types.Schema)"},{"p":"com.google.adk.agents","c":"LlmAgent.IncludeContents","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.google.adk.agents","c":"RunConfig.StreamingMode","l":"valueOf(String)","u":"valueOf(java.lang.String)"},{"p":"com.google.adk.agents","c":"LlmAgent.IncludeContents","l":"values()"},{"p":"com.google.adk.agents","c":"RunConfig.StreamingMode","l":"values()"},{"p":"com.google.adk.sessions","c":"State","l":"values()"},{"p":"com.google.adk.artifacts","c":"ListArtifactVersionsResponse","l":"versions()"},{"p":"com.google.adk.artifacts","c":"ListArtifactVersionsResponse.Builder","l":"versions(List<Part>)","u":"versions(java.util.List)"},{"p":"com.google.adk.sessions","c":"HttpApiClient","l":"vertexAI()"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"VertexAiSessionService()","u":"%3Cinit%3E()"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"VertexAiSessionService(String, String, HttpApiClient)","u":"%3Cinit%3E(java.lang.String,java.lang.String,com.google.adk.sessions.HttpApiClient)"},{"p":"com.google.adk.sessions","c":"VertexAiSessionService","l":"VertexAiSessionService(String, String, Optional<GoogleCredentials>, Optional<HttpOptions>)","u":"%3Cinit%3E(java.lang.String,java.lang.String,java.util.Optional,java.util.Optional)"},{"p":"com.google.adk.models","c":"VertexCredentials","l":"VertexCredentials()","u":"%3Cinit%3E()"},{"p":"com.google.adk.models","c":"Gemini.Builder","l":"vertexCredentials(VertexCredentials)","u":"vertexCredentials(com.google.adk.models.VertexCredentials)"},{"p":"com.google.adk.flows.llmflows.audio","c":"VertexSpeechClient","l":"VertexSpeechClient()","u":"%3Cinit%3E()"}];updateSearchResults();