<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Class Hierarchy (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="class tree">
<meta name="generator" content="javadoc/TreeWriter">
<link rel="stylesheet" type="text/css" href="resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="script-files/script.js"></script>
<script type="text/javascript" src="script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-files/jquery-ui.min.js"></script>
</head>
<body class="tree-page">
<script type="text/javascript">const pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li class="nav-bar-cell1-rev">Tree</li>
<li><a href="index-all.html">Index</a></li>
<li><a href="search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list"></ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 class="title">Hierarchy For All Packages</h1>
</div>
<span class="package-hierarchy-label">Package Hierarchies:</span>
<ul class="horizontal contents-list">
<li><a href="com/google/adk/package-tree.html">com.google.adk</a>, </li>
<li><a href="com/google/adk/agents/package-tree.html">com.google.adk.agents</a>, </li>
<li><a href="com/google/adk/artifacts/package-tree.html">com.google.adk.artifacts</a>, </li>
<li><a href="com/google/adk/events/package-tree.html">com.google.adk.events</a>, </li>
<li><a href="com/google/adk/examples/package-tree.html">com.google.adk.examples</a>, </li>
<li><a href="com/google/adk/exceptions/package-tree.html">com.google.adk.exceptions</a>, </li>
<li><a href="com/google/adk/flows/package-tree.html">com.google.adk.flows</a>, </li>
<li><a href="com/google/adk/flows/llmflows/package-tree.html">com.google.adk.flows.llmflows</a>, </li>
<li><a href="com/google/adk/flows/llmflows/audio/package-tree.html">com.google.adk.flows.llmflows.audio</a>, </li>
<li><a href="com/google/adk/models/package-tree.html">com.google.adk.models</a>, </li>
<li><a href="com/google/adk/network/package-tree.html">com.google.adk.network</a>, </li>
<li><a href="com/google/adk/runner/package-tree.html">com.google.adk.runner</a>, </li>
<li><a href="com/google/adk/sessions/package-tree.html">com.google.adk.sessions</a>, </li>
<li><a href="com/google/adk/tools/package-tree.html">com.google.adk.tools</a>, </li>
<li><a href="com/google/adk/tools/mcp/package-tree.html">com.google.adk.tools.mcp</a>, </li>
<li><a href="com/google/adk/tools/retrieval/package-tree.html">com.google.adk.tools.retrieval</a>, </li>
<li><a href="com/google/adk/utils/package-tree.html">com.google.adk.utils</a></li>
</ul>
<section class="hierarchy">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/AgentTransfer.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">AgentTransfer</a></li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/Annotations.html" class="type-name-link" title="class in com.google.adk.tools">Annotations</a></li>
<li class="circle">com.google.adk.network.<a href="com/google/adk/network/ApiResponse.html" class="type-name-link" title="class in com.google.adk.network">ApiResponse</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a>)
<ul>
<li class="circle">com.google.adk.network.<a href="com/google/adk/network/HttpApiResponse.html" class="type-name-link" title="class in com.google.adk.network">HttpApiResponse</a></li>
</ul>
</li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/ApiResponse.html" class="type-name-link" title="class in com.google.adk.sessions">ApiResponse</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a>)
<ul>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiResponse.html" class="type-name-link" title="class in com.google.adk.sessions">HttpApiResponse</a></li>
</ul>
</li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/BaseAgent.html" class="type-name-link" title="class in com.google.adk.agents">BaseAgent</a>
<ul>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.html" class="type-name-link" title="class in com.google.adk.agents">LoopAgent</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.html" class="type-name-link" title="class in com.google.adk.agents">ParallelAgent</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent</a></li>
</ul>
</li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/BaseLlm.html" class="type-name-link" title="class in com.google.adk.models">BaseLlm</a>
<ul>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/Claude.html" class="type-name-link" title="class in com.google.adk.models">Claude</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/Gemini.html" class="type-name-link" title="class in com.google.adk.models">Gemini</a></li>
</ul>
</li>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/BaseLlmFlow.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">BaseLlmFlow</a> (implements com.google.adk.flows.<a href="com/google/adk/flows/BaseFlow.html" title="interface in com.google.adk.flows">BaseFlow</a>)
<ul>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/SingleFlow.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">SingleFlow</a>
<ul>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/AutoFlow.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">AutoFlow</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/BaseTool.html" class="type-name-link" title="class in com.google.adk.tools">BaseTool</a>
<ul>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/AgentTool.html" class="type-name-link" title="class in com.google.adk.tools">AgentTool</a></li>
<li class="circle">com.google.adk.tools.retrieval.<a href="com/google/adk/tools/retrieval/BaseRetrievalTool.html" class="type-name-link" title="class in com.google.adk.tools.retrieval">BaseRetrievalTool</a></li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/BuiltInCodeExecutionTool.html" class="type-name-link" title="class in com.google.adk.tools">BuiltInCodeExecutionTool</a></li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/FunctionTool.html" class="type-name-link" title="class in com.google.adk.tools">FunctionTool</a>
<ul>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/LongRunningFunctionTool.html" class="type-name-link" title="class in com.google.adk.tools">LongRunningFunctionTool</a></li>
</ul>
</li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/GoogleSearchTool.html" class="type-name-link" title="class in com.google.adk.tools">GoogleSearchTool</a></li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/LoadArtifactsTool.html" class="type-name-link" title="class in com.google.adk.tools">LoadArtifactsTool</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpTool.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpTool</a></li>
</ul>
</li>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Basic.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Basic</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.html" class="type-name-link" title="class in com.google.adk.agents">Callbacks</a></li>
<li class="circle">com.google.adk.<a href="com/google/adk/CollectionUtils.html" class="type-name-link" title="class in com.google.adk">CollectionUtils</a></li>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Contents.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Contents</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/ConversionUtils.html" class="type-name-link" title="class in com.google.adk.tools.mcp">ConversionUtils</a></li>
<li class="circle">com.google.adk.events.<a href="com/google/adk/events/Event.Builder.html" class="type-name-link" title="class in com.google.adk.events">Event.Builder</a></li>
<li class="circle">com.google.adk.events.<a href="com/google/adk/events/EventActions.html" class="type-name-link" title="class in com.google.adk.events">EventActions</a></li>
<li class="circle">com.google.adk.events.<a href="com/google/adk/events/EventActions.Builder.html" class="type-name-link" title="class in com.google.adk.events">EventActions.Builder</a></li>
<li class="circle">com.google.adk.events.<a href="com/google/adk/events/EventStream.html" class="type-name-link" title="class in com.google.adk.events">EventStream</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Iterable.html" title="class or interface in java.lang" class="external-link">Iterable</a>&lt;T&gt;)</li>
<li class="circle">com.google.adk.examples.<a href="com/google/adk/examples/Example.html" class="type-name-link" title="class in com.google.adk.examples">Example</a></li>
<li class="circle">com.google.adk.examples.<a href="com/google/adk/examples/Example.Builder.html" class="type-name-link" title="class in com.google.adk.examples">Example.Builder</a></li>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Examples.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Examples</a></li>
<li class="circle">com.google.adk.examples.<a href="com/google/adk/examples/ExampleUtils.html" class="type-name-link" title="class in com.google.adk.examples">ExampleUtils</a></li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/ExitLoopTool.html" class="type-name-link" title="class in com.google.adk.tools">ExitLoopTool</a></li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/FunctionCallingUtils.html" class="type-name-link" title="class in com.google.adk.tools">FunctionCallingUtils</a></li>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Functions.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Functions</a></li>
<li class="circle">com.google.adk.artifacts.<a href="com/google/adk/artifacts/GcsArtifactService.html" class="type-name-link" title="class in com.google.adk.artifacts">GcsArtifactService</a> (implements com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>)</li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/Gemini.Builder.html" class="type-name-link" title="class in com.google.adk.models">Gemini.Builder</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/GeminiLlmConnection.html" class="type-name-link" title="class in com.google.adk.models">GeminiLlmConnection</a> (implements com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" title="interface in com.google.adk.models">BaseLlmConnection</a>)</li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.html" class="type-name-link" title="class in com.google.adk.sessions">GetSessionConfig</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/GetSessionConfig.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">GetSessionConfig.Builder</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/HttpApiClient.html" class="type-name-link" title="class in com.google.adk.sessions">HttpApiClient</a></li>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Identity.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Identity</a></li>
<li class="circle">com.google.adk.artifacts.<a href="com/google/adk/artifacts/InMemoryArtifactService.html" class="type-name-link" title="class in com.google.adk.artifacts">InMemoryArtifactService</a> (implements com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" title="interface in com.google.adk.artifacts">BaseArtifactService</a>)</li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/InMemorySessionService.html" class="type-name-link" title="class in com.google.adk.sessions">InMemorySessionService</a> (implements com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>)</li>
<li class="circle">com.google.adk.flows.llmflows.<a href="com/google/adk/flows/llmflows/Instructions.html" class="type-name-link" title="class in com.google.adk.flows.llmflows">Instructions</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/InvocationContext.html" class="type-name-link" title="class in com.google.adk.agents">InvocationContext</a></li>
<li class="circle">com.google.adk.<a href="com/google/adk/JsonBaseModel.html" class="type-name-link" title="class in com.google.adk">JsonBaseModel</a>
<ul>
<li class="circle">com.google.adk.events.<a href="com/google/adk/events/Event.html" class="type-name-link" title="class in com.google.adk.events">Event</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequest</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/LlmRequest.html" class="type-name-link" title="class in com.google.adk.models">LlmRequest</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/LlmResponse.html" class="type-name-link" title="class in com.google.adk.models">LlmResponse</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/Session.html" class="type-name-link" title="class in com.google.adk.sessions">Session</a></li>
</ul>
</li>
<li class="circle">com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactsResponse</a></li>
<li class="circle">com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactsResponse.Builder</a></li>
<li class="circle">com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse</a></li>
<li class="circle">com.google.adk.artifacts.<a href="com/google/adk/artifacts/ListArtifactVersionsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.artifacts">ListArtifactVersionsResponse.Builder</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.html" class="type-name-link" title="class in com.google.adk.sessions">ListEventsResponse</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/ListEventsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">ListEventsResponse.Builder</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.html" class="type-name-link" title="class in com.google.adk.sessions">ListSessionsResponse</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/ListSessionsResponse.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">ListSessionsResponse.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LiveRequest.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequest.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LiveRequestQueue.html" class="type-name-link" title="class in com.google.adk.agents">LiveRequestQueue</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LlmAgent.Builder</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/LlmRegistry.html" class="type-name-link" title="class in com.google.adk.models">LlmRegistry</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/LlmRequest.Builder.html" class="type-name-link" title="class in com.google.adk.models">LlmRequest.Builder</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/LlmResponse.Builder.html" class="type-name-link" title="class in com.google.adk.models">LlmResponse.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LoopAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">LoopAgent.Builder</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpSessionManager.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpSessionManager</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset</a> (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" title="class or interface in java.lang" class="external-link">AutoCloseable</a>)</li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolsAndToolsetResult.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsAndToolsetResult</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/Model.html" class="type-name-link" title="class in com.google.adk.models">Model</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/Model.Builder.html" class="type-name-link" title="class in com.google.adk.models">Model.Builder</a></li>
<li class="circle">com.google.adk.utils.<a href="com/google/adk/utils/Pairs.html" class="type-name-link" title="class in com.google.adk.utils">Pairs</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/ParallelAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">ParallelAgent.Builder</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/ReadonlyContext.html" class="type-name-link" title="class in com.google.adk.agents">ReadonlyContext</a>
<ul>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/CallbackContext.html" class="type-name-link" title="class in com.google.adk.agents">CallbackContext</a>
<ul>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.html" class="type-name-link" title="class in com.google.adk.tools">ToolContext</a></li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.html" class="type-name-link" title="class in com.google.adk.agents">RunConfig</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.Builder.html" class="type-name-link" title="class in com.google.adk.agents">RunConfig.Builder</a></li>
<li class="circle">com.google.adk.runner.<a href="com/google/adk/runner/Runner.html" class="type-name-link" title="class in com.google.adk.runner">Runner</a>
<ul>
<li class="circle">com.google.adk.runner.<a href="com/google/adk/runner/InMemoryRunner.html" class="type-name-link" title="class in com.google.adk.runner">InMemoryRunner</a></li>
</ul>
</li>
<li class="circle">com.google.adk.<a href="com/google/adk/SchemaUtils.html" class="type-name-link" title="class in com.google.adk">SchemaUtils</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/SequentialAgent.Builder.html" class="type-name-link" title="class in com.google.adk.agents">SequentialAgent.Builder</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/Session.Builder.html" class="type-name-link" title="class in com.google.adk.sessions">Session.Builder</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/SessionUtils.html" class="type-name-link" title="class in com.google.adk.sessions">SessionUtils</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.html" class="type-name-link" title="class in com.google.adk.tools.mcp">SseServerParameters</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/SseServerParameters.Builder.html" class="type-name-link" title="class in com.google.adk.tools.mcp">SseServerParameters.Builder</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/State.html" class="type-name-link" title="class in com.google.adk.sessions">State</a> (implements java.util.concurrent.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/util/concurrent/ConcurrentMap.html" title="class or interface in java.util.concurrent" class="external-link">ConcurrentMap</a>&lt;K,<wbr>V&gt;)</li>
<li class="circle">com.google.adk.<a href="com/google/adk/Telemetry.html" class="type-name-link" title="class in com.google.adk">Telemetry</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Throwable.html" class="type-name-link external-link" title="class or interface in java.lang">Throwable</a> (implements java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" class="type-name-link external-link" title="class or interface in java.lang">Exception</a>
<ul>
<li class="circle">com.google.adk.exceptions.<a href="com/google/adk/exceptions/LlmCallsLimitExceededException.html" class="type-name-link" title="class in com.google.adk.exceptions">LlmCallsLimitExceededException</a></li>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/RuntimeException.html" class="type-name-link external-link" title="class or interface in java.lang">RuntimeException</a>
<ul>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a>
<ul>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpInitializationException</a></li>
<li class="circle">com.google.adk.tools.mcp.<a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html" class="type-name-link" title="class in com.google.adk.tools.mcp">McpToolset.McpToolLoadingException</a></li>
</ul>
</li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/SessionException.html" class="type-name-link" title="class in com.google.adk.sessions">SessionException</a>
<ul>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/SessionNotFoundException.html" class="type-name-link" title="class in com.google.adk.sessions">SessionNotFoundException</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/ToolContext.Builder.html" class="type-name-link" title="class in com.google.adk.tools">ToolContext.Builder</a></li>
<li class="circle">com.google.adk.<a href="com/google/adk/Version.html" class="type-name-link" title="class in com.google.adk">Version</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/VertexAiSessionService.html" class="type-name-link" title="class in com.google.adk.sessions">VertexAiSessionService</a> (implements com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" title="interface in com.google.adk.sessions">BaseSessionService</a>)</li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.html" class="type-name-link" title="class in com.google.adk.models">VertexCredentials</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/VertexCredentials.Builder.html" class="type-name-link" title="class in com.google.adk.models">VertexCredentials.Builder</a></li>
<li class="circle">com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/VertexSpeechClient.html" class="type-name-link" title="class in com.google.adk.flows.llmflows.audio">VertexSpeechClient</a> (implements com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html" title="interface in com.google.adk.flows.llmflows.audio">SpeechClientInterface</a>)</li>
</ul>
</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/AutoCloseable.html" class="type-name-link external-link" title="class or interface in java.lang">AutoCloseable</a>
<ul>
<li class="circle">com.google.adk.flows.llmflows.audio.<a href="com/google/adk/flows/llmflows/audio/SpeechClientInterface.html" class="type-name-link" title="interface in com.google.adk.flows.llmflows.audio">SpeechClientInterface</a></li>
</ul>
</li>
<li class="circle">com.google.adk.artifacts.<a href="com/google/adk/artifacts/BaseArtifactService.html" class="type-name-link" title="interface in com.google.adk.artifacts">BaseArtifactService</a></li>
<li class="circle">com.google.adk.examples.<a href="com/google/adk/examples/BaseExampleProvider.html" class="type-name-link" title="interface in com.google.adk.examples">BaseExampleProvider</a></li>
<li class="circle">com.google.adk.flows.<a href="com/google/adk/flows/BaseFlow.html" class="type-name-link" title="interface in com.google.adk.flows">BaseFlow</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/BaseLlmConnection.html" class="type-name-link" title="interface in com.google.adk.models">BaseLlmConnection</a></li>
<li class="circle">com.google.adk.sessions.<a href="com/google/adk/sessions/BaseSessionService.html" class="type-name-link" title="interface in com.google.adk.sessions">BaseSessionService</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterAgentCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterAgentCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterAgentCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterModelCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterModelCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterModelCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterModelCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterToolCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterToolCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.AfterToolCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.AfterToolCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeAgentCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeAgentCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeAgentCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeModelCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeModelCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeModelCallbackSync</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeToolCallback.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallback</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/Callbacks.BeforeToolCallbackSync.html" class="type-name-link" title="interface in com.google.adk.agents">Callbacks.BeforeToolCallbackSync</a></li>
<li class="circle">com.google.adk.models.<a href="com/google/adk/models/LlmRegistry.LlmFactory.html" class="type-name-link" title="interface in com.google.adk.models">LlmRegistry.LlmFactory</a></li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Annotation Interface Hierarchy">Annotation Interface Hierarchy</h2>
<ul>
<li class="circle">com.google.adk.tools.<a href="com/google/adk/tools/Annotations.Schema.html" class="type-name-link" title="annotation interface in com.google.adk.tools">Annotations.Schema</a> (implements java.lang.annotation.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/annotation/Annotation.html" title="class or interface in java.lang.annotation" class="external-link">Annotation</a>)</li>
</ul>
</section>
<section class="hierarchy">
<h2 title="Enum Class Hierarchy">Enum Class Hierarchy</h2>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Object.html" class="type-name-link external-link" title="class or interface in java.lang">Object</a>
<ul>
<li class="circle">java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Enum.html" class="type-name-link external-link" title="class or interface in java.lang">Enum</a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Comparable.html" title="class or interface in java.lang" class="external-link">Comparable</a>&lt;T&gt;, java.lang.constant.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/constant/Constable.html" title="class or interface in java.lang.constant" class="external-link">Constable</a>, java.io.<a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a>)
<ul>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/LlmAgent.IncludeContents.html" class="type-name-link" title="enum class in com.google.adk.agents">LlmAgent.IncludeContents</a></li>
<li class="circle">com.google.adk.agents.<a href="com/google/adk/agents/RunConfig.StreamingMode.html" class="type-name-link" title="enum class in com.google.adk.agents">RunConfig.StreamingMode</a></li>
</ul>
</li>
</ul>
</li>
</ul>
</section>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
