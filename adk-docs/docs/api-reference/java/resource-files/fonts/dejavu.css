/*
 * Copyright (c) 2024, Oracle and/or its affiliates. All rights reserved.
 * DO NOT ALTER OR REMOVE COPYRIGHT NOTICES OR THIS FILE HEADER.
 *
 * Licensed under the Universal Permissive License v 1.0 as shown at https://oss.oracle.com/licenses/upl/
 */

/* DejaVu fonts v2.37 */

@font-face {
  font-family: 'DejaVu Sans Mono';
  src: url('DejaVuLGCSansMono.woff2') format('woff2'),
       url('DejaVuLGCSansMono.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'DejaVu Sans Mono';
  src: url('DejaVuLGCSansMono-Oblique.woff2') format('woff2'),
       url('DejaVuLGCSansMono-Oblique.woff') format('woff');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'DejaVu Sans Mono';
  src: url('DejaVuLGCSansMono-Bold.woff2') format('woff2'),
       url('DejaVuLGCSansMono-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'DejaVu Sans Mono';
  src: url('DejaVuLGCSansMono-BoldOblique.woff2') format('woff2'),
       url('DejaVuLGCSansMono-BoldOblique.woff') format('woff');
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'DejaVu Sans';
  src: url('DejaVuLGCSans.woff2') format('woff2'),
       url('DejaVuLGCSans.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'DejaVu Sans';
  src: url('DejaVuLGCSans-Oblique.woff2') format('woff2'),
       url('DejaVuLGCSans-Oblique.woff') format('woff');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'DejaVu Sans';
  src: url('DejaVuLGCSans-Bold.woff2') format('woff2'),
       url('DejaVuLGCSans-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'DejaVu Sans';
  src: url('DejaVuLGCSans-BoldOblique.woff2') format('woff2'),
       url('DejaVuLGCSans-BoldOblique.woff') format('woff');
  font-weight: bold;
  font-style: italic;
}

@font-face {
  font-family: 'DejaVu Serif';
  src: url('DejaVuLGCSerif.woff2') format('woff2'),
       url('DejaVuLGCSerif.woff') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'DejaVu Serif';
  src: url('DejaVuLGCSerif-Italic.woff2') format('woff2'),
       url('DejaVuLGCSerif-Italic.woff') format('woff');
  font-weight: normal;
  font-style: italic;
}

@font-face {
  font-family: 'DejaVu Serif';
  src: url('DejaVuLGCSerif-Bold.woff2') format('woff2'),
       url('DejaVuLGCSerif-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'DejaVu Serif';
  src: url('DejaVuLGCSerif-BoldItalic.woff2') format('woff2'),
       url('DejaVuLGCSerif-BoldItalic.woff') format('woff');
  font-weight: bold;
  font-style: italic;
}
