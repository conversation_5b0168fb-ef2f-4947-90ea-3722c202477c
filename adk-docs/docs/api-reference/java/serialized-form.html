<!DOCTYPE HTML>
<html lang="en">
<head>
<!-- Generated by javadoc (24) on Tue May 20 10:46:17 UTC 2025 -->
<title>Serialized Form (Agent Development Kit 0.1.0 API)</title>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="dc.created" content="2025-05-20">
<meta name="description" content="serialized forms">
<meta name="generator" content="javadoc/SerializedFormWriter">
<link rel="stylesheet" type="text/css" href="resource-files/jquery-ui.min.css" title="Style">
<link rel="stylesheet" type="text/css" href="resource-files/stylesheet.css" title="Style">
<script type="text/javascript" src="script-files/script.js"></script>
<script type="text/javascript" src="script-files/jquery-3.7.1.min.js"></script>
<script type="text/javascript" src="script-files/jquery-ui.min.js"></script>
</head>
<body class="serialized-form-page">
<script type="text/javascript">const pathtoroot = "./";
loadScripts(document, 'script');</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<header role="banner">
<nav role="navigation">
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="top-nav" id="navbar-top">
<div class="nav-content">
<div class="nav-menu-button"><button id="navbar-toggle-button" aria-controls="navbar-top" aria-expanded="false" aria-label="Toggle navigation links"><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span><span class="nav-bar-toggle-icon">&nbsp;</span></button></div>
<div class="skip-nav"><a href="#skip-navbar-top" title="Skip navigation links">Skip navigation links</a></div>
<ul id="navbar-top-firstrow" class="nav-list" title="Navigation">
<li><a href="index.html">Overview</a></li>
<li><a href="overview-tree.html">Tree</a></li>
<li><a href="index-all.html">Index</a></li>
<li><a href="search.html">Search</a></li>
</ul>
</div>
</div>
<div class="sub-nav">
<div class="nav-content">
<ol class="sub-nav-list"></ol>
<div class="nav-list-search">
<input type="text" id="search-input" disabled placeholder="Search" aria-label="Search in documentation" autocomplete="off">
<input type="reset" id="reset-search" disabled value="Reset">
</div>
</div>
</div>
<!-- ========= END OF TOP NAVBAR ========= -->
<span class="skip-nav" id="skip-navbar-top"></span></nav>
</header>
<div class="main-grid">
<main role="main">
<div class="header">
<h1 title="Serialized Form" class="title">Serialized Form</h1>
</div>
<ul class="block-list">
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="com/google/adk/exceptions/package-summary.html">com.google.adk.exceptions</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="com.google.adk.exceptions.LlmCallsLimitExceededException">
<h3>Exception Class&nbsp;<a href="com/google/adk/exceptions/LlmCallsLimitExceededException.html" title="class in com.google.adk.exceptions">com.google.adk.exceptions.LlmCallsLimitExceededException</a></h3>
<div class="type-signature">class LlmCallsLimitExceededException extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/Exception.html" title="class or interface in java.lang" class="external-link">Exception</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="com/google/adk/sessions/package-summary.html">com.google.adk.sessions</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="com.google.adk.sessions.SessionException">
<h3>Exception Class&nbsp;<a href="com/google/adk/sessions/SessionException.html" title="class in com.google.adk.sessions">com.google.adk.sessions.SessionException</a></h3>
<div class="type-signature">class SessionException extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="com.google.adk.sessions.SessionNotFoundException">
<h3>Exception Class&nbsp;<a href="com/google/adk/sessions/SessionNotFoundException.html" title="class in com.google.adk.sessions">com.google.adk.sessions.SessionNotFoundException</a></h3>
<div class="type-signature">class SessionNotFoundException extends <a href="com/google/adk/sessions/SessionException.html" title="class in com.google.adk.sessions">SessionException</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
<li>
<section class="serialized-package-container">
<h2 title="Package">Package&nbsp;<a href="com/google/adk/tools/mcp/package-summary.html">com.google.adk.tools.mcp</a></h2>
<ul class="block-list">
<li>
<section class="serialized-class-details" id="com.google.adk.tools.mcp.McpToolset.McpInitializationException">
<h3>Exception Class&nbsp;<a href="com/google/adk/tools/mcp/McpToolset.McpInitializationException.html" title="class in com.google.adk.tools.mcp">com.google.adk.tools.mcp.McpToolset.McpInitializationException</a></h3>
<div class="type-signature">class McpInitializationException extends <a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="com.google.adk.tools.mcp.McpToolset.McpToolLoadingException">
<h3>Exception Class&nbsp;<a href="com/google/adk/tools/mcp/McpToolset.McpToolLoadingException.html" title="class in com.google.adk.tools.mcp">com.google.adk.tools.mcp.McpToolset.McpToolLoadingException</a></h3>
<div class="type-signature">class McpToolLoadingException extends <a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" title="class in com.google.adk.tools.mcp">McpToolset.McpToolsetException</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
<li>
<section class="serialized-class-details" id="com.google.adk.tools.mcp.McpToolset.McpToolsetException">
<h3>Exception Class&nbsp;<a href="com/google/adk/tools/mcp/McpToolset.McpToolsetException.html" title="class in com.google.adk.tools.mcp">com.google.adk.tools.mcp.McpToolset.McpToolsetException</a></h3>
<div class="type-signature">class McpToolsetException extends <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/lang/RuntimeException.html" title="class or interface in java.lang" class="external-link">RuntimeException</a> implements <a href="https://docs.oracle.com/en/java/javase/17/docs/api/java.base/java/io/Serializable.html" title="class or interface in java.io" class="external-link">Serializable</a></div>
</section>
</li>
</ul>
</section>
</li>
</ul>
<footer role="contentinfo">
<hr>
<p class="legal-copy"><small>Copyright &#169; 2025. All rights reserved.</small></p>
</footer>
</main>
</div>
</body>
</html>
