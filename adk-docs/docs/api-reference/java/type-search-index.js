typeSearchIndex = [{"p":"com.google.adk.agents","l":"Callbacks.AfterAgentCallback"},{"p":"com.google.adk.agents","l":"Callbacks.AfterAgentCallbackSync"},{"p":"com.google.adk.agents","l":"Callbacks.AfterModelCallback"},{"p":"com.google.adk.agents","l":"Callbacks.AfterModelCallbackSync"},{"p":"com.google.adk.agents","l":"Callbacks.AfterToolCallback"},{"p":"com.google.adk.agents","l":"Callbacks.AfterToolCallbackSync"},{"p":"com.google.adk.tools","l":"AgentTool"},{"p":"com.google.adk.flows.llmflows","l":"AgentTransfer"},{"l":"All Classes and Interfaces","u":"allclasses-index.html"},{"p":"com.google.adk.tools","l":"Annotations"},{"p":"com.google.adk.network","l":"ApiResponse"},{"p":"com.google.adk.sessions","l":"ApiResponse"},{"p":"com.google.adk.flows.llmflows","l":"AutoFlow"},{"p":"com.google.adk.agents","l":"BaseAgent"},{"p":"com.google.adk.artifacts","l":"BaseArtifactService"},{"p":"com.google.adk.examples","l":"BaseExampleProvider"},{"p":"com.google.adk.flows","l":"BaseFlow"},{"p":"com.google.adk.models","l":"BaseLlm"},{"p":"com.google.adk.models","l":"BaseLlmConnection"},{"p":"com.google.adk.flows.llmflows","l":"BaseLlmFlow"},{"p":"com.google.adk.tools.retrieval","l":"BaseRetrievalTool"},{"p":"com.google.adk.sessions","l":"BaseSessionService"},{"p":"com.google.adk.tools","l":"BaseTool"},{"p":"com.google.adk.flows.llmflows","l":"Basic"},{"p":"com.google.adk.agents","l":"Callbacks.BeforeAgentCallback"},{"p":"com.google.adk.agents","l":"Callbacks.BeforeAgentCallbackSync"},{"p":"com.google.adk.agents","l":"Callbacks.BeforeModelCallback"},{"p":"com.google.adk.agents","l":"Callbacks.BeforeModelCallbackSync"},{"p":"com.google.adk.agents","l":"Callbacks.BeforeToolCallback"},{"p":"com.google.adk.agents","l":"Callbacks.BeforeToolCallbackSync"},{"p":"com.google.adk.events","l":"Event.Builder"},{"p":"com.google.adk.events","l":"EventActions.Builder"},{"p":"com.google.adk.examples","l":"Example.Builder"},{"p":"com.google.adk.models","l":"Gemini.Builder"},{"p":"com.google.adk.sessions","l":"GetSessionConfig.Builder"},{"p":"com.google.adk.artifacts","l":"ListArtifactsResponse.Builder"},{"p":"com.google.adk.artifacts","l":"ListArtifactVersionsResponse.Builder"},{"p":"com.google.adk.sessions","l":"ListEventsResponse.Builder"},{"p":"com.google.adk.sessions","l":"ListSessionsResponse.Builder"},{"p":"com.google.adk.agents","l":"LiveRequest.Builder"},{"p":"com.google.adk.agents","l":"LlmAgent.Builder"},{"p":"com.google.adk.models","l":"LlmRequest.Builder"},{"p":"com.google.adk.models","l":"LlmResponse.Builder"},{"p":"com.google.adk.agents","l":"LoopAgent.Builder"},{"p":"com.google.adk.models","l":"Model.Builder"},{"p":"com.google.adk.agents","l":"ParallelAgent.Builder"},{"p":"com.google.adk.agents","l":"RunConfig.Builder"},{"p":"com.google.adk.agents","l":"SequentialAgent.Builder"},{"p":"com.google.adk.sessions","l":"Session.Builder"},{"p":"com.google.adk.tools.mcp","l":"SseServerParameters.Builder"},{"p":"com.google.adk.tools","l":"ToolContext.Builder"},{"p":"com.google.adk.models","l":"VertexCredentials.Builder"},{"p":"com.google.adk.tools","l":"BuiltInCodeExecutionTool"},{"p":"com.google.adk.agents","l":"CallbackContext"},{"p":"com.google.adk.agents","l":"Callbacks"},{"p":"com.google.adk.models","l":"Claude"},{"p":"com.google.adk","l":"CollectionUtils"},{"p":"com.google.adk.flows.llmflows","l":"Contents"},{"p":"com.google.adk.tools.mcp","l":"ConversionUtils"},{"p":"com.google.adk.events","l":"Event"},{"p":"com.google.adk.events","l":"EventActions"},{"p":"com.google.adk.events","l":"EventStream"},{"p":"com.google.adk.examples","l":"Example"},{"p":"com.google.adk.flows.llmflows","l":"Examples"},{"p":"com.google.adk.examples","l":"ExampleUtils"},{"p":"com.google.adk.tools","l":"ExitLoopTool"},{"p":"com.google.adk.tools","l":"FunctionCallingUtils"},{"p":"com.google.adk.flows.llmflows","l":"Functions"},{"p":"com.google.adk.tools","l":"FunctionTool"},{"p":"com.google.adk.artifacts","l":"GcsArtifactService"},{"p":"com.google.adk.models","l":"Gemini"},{"p":"com.google.adk.models","l":"GeminiLlmConnection"},{"p":"com.google.adk.sessions","l":"GetSessionConfig"},{"p":"com.google.adk.tools","l":"GoogleSearchTool"},{"p":"com.google.adk.sessions","l":"HttpApiClient"},{"p":"com.google.adk.network","l":"HttpApiResponse"},{"p":"com.google.adk.sessions","l":"HttpApiResponse"},{"p":"com.google.adk.flows.llmflows","l":"Identity"},{"p":"com.google.adk.agents","l":"LlmAgent.IncludeContents"},{"p":"com.google.adk.artifacts","l":"InMemoryArtifactService"},{"p":"com.google.adk.runner","l":"InMemoryRunner"},{"p":"com.google.adk.sessions","l":"InMemorySessionService"},{"p":"com.google.adk.flows.llmflows","l":"Instructions"},{"p":"com.google.adk.agents","l":"InvocationContext"},{"p":"com.google.adk","l":"JsonBaseModel"},{"p":"com.google.adk.artifacts","l":"ListArtifactsResponse"},{"p":"com.google.adk.artifacts","l":"ListArtifactVersionsResponse"},{"p":"com.google.adk.sessions","l":"ListEventsResponse"},{"p":"com.google.adk.sessions","l":"ListSessionsResponse"},{"p":"com.google.adk.agents","l":"LiveRequest"},{"p":"com.google.adk.agents","l":"LiveRequestQueue"},{"p":"com.google.adk.agents","l":"LlmAgent"},{"p":"com.google.adk.exceptions","l":"LlmCallsLimitExceededException"},{"p":"com.google.adk.models","l":"LlmRegistry.LlmFactory"},{"p":"com.google.adk.models","l":"LlmRegistry"},{"p":"com.google.adk.models","l":"LlmRequest"},{"p":"com.google.adk.models","l":"LlmResponse"},{"p":"com.google.adk.tools","l":"LoadArtifactsTool"},{"p":"com.google.adk.tools","l":"LongRunningFunctionTool"},{"p":"com.google.adk.agents","l":"LoopAgent"},{"p":"com.google.adk.tools.mcp","l":"McpToolset.McpInitializationException"},{"p":"com.google.adk.tools.mcp","l":"McpSessionManager"},{"p":"com.google.adk.tools.mcp","l":"McpTool"},{"p":"com.google.adk.tools.mcp","l":"McpToolset.McpToolLoadingException"},{"p":"com.google.adk.tools.mcp","l":"McpToolset.McpToolsAndToolsetResult"},{"p":"com.google.adk.tools.mcp","l":"McpToolset"},{"p":"com.google.adk.tools.mcp","l":"McpToolset.McpToolsetException"},{"p":"com.google.adk.models","l":"Model"},{"p":"com.google.adk.utils","l":"Pairs"},{"p":"com.google.adk.agents","l":"ParallelAgent"},{"p":"com.google.adk.agents","l":"ReadonlyContext"},{"p":"com.google.adk.agents","l":"RunConfig"},{"p":"com.google.adk.runner","l":"Runner"},{"p":"com.google.adk.tools","l":"Annotations.Schema"},{"p":"com.google.adk","l":"SchemaUtils"},{"p":"com.google.adk.agents","l":"SequentialAgent"},{"p":"com.google.adk.sessions","l":"Session"},{"p":"com.google.adk.sessions","l":"SessionException"},{"p":"com.google.adk.sessions","l":"SessionNotFoundException"},{"p":"com.google.adk.sessions","l":"SessionUtils"},{"p":"com.google.adk.flows.llmflows","l":"SingleFlow"},{"p":"com.google.adk.flows.llmflows.audio","l":"SpeechClientInterface"},{"p":"com.google.adk.tools.mcp","l":"SseServerParameters"},{"p":"com.google.adk.sessions","l":"State"},{"p":"com.google.adk.agents","l":"RunConfig.StreamingMode"},{"p":"com.google.adk","l":"Telemetry"},{"p":"com.google.adk.tools","l":"ToolContext"},{"p":"com.google.adk","l":"Version"},{"p":"com.google.adk.sessions","l":"VertexAiSessionService"},{"p":"com.google.adk.models","l":"VertexCredentials"},{"p":"com.google.adk.flows.llmflows.audio","l":"VertexSpeechClient"}];updateSearchResults();