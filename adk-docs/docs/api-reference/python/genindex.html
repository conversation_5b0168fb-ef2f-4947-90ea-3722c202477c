<!doctype html>
<html class="no-js" lang="en" data-content_root="./">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><link rel="index" title="Index" href="#" /><link rel="search" title="Search" href="search.html" />

    <!-- Generated with Sphinx 8.2.3 and Furo 2024.08.06 --><title>Index - Agent Development Kit documentation</title>
<link rel="stylesheet" type="text/css" href="_static/pygments.css?v=a746c00c" />
    <link rel="stylesheet" type="text/css" href="_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="_static/autodoc_pydantic.css?v=9e08a393" />
    <link rel="stylesheet" type="text/css" href="_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="_static/css/custom.css" />
    
    


<style>
  body {
    --color-code-background: #f8f8f8;
  --color-code-foreground: black;
  --font-stack: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --font-stack--monospace: Roboto Mono, "Helvetica Neue Mono", monospace;
  --font-stack--headings: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --color-brand-primary: black;
  --color-brand-content: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #202020;
  --color-code-foreground: #d0d0d0;
  --font-stack: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --font-stack--monospace: Roboto Mono, "Helvetica Neue Mono", monospace;
  --font-stack--headings: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --color-brand-primary: white;
  --color-brand-content: white;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #202020;
  --color-code-foreground: #d0d0d0;
  --font-stack: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --font-stack--monospace: Roboto Mono, "Helvetica Neue Mono", monospace;
  --font-stack--headings: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --color-brand-primary: white;
  --color-brand-content: white;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="index.html"><div class="brand">Agent Development Kit  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="index.html">
  
  
  <span class="sidebar-brand-text">Agent Development Kit  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html">Submodules</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.agents">google.adk.agents module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.artifacts">google.adk.artifacts module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.code_executors">google.adk.code_executors module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.evaluation">google.adk.evaluation module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.events">google.adk.events module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.examples">google.adk.examples module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.memory">google.adk.memory module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.models">google.adk.models module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.planners">google.adk.planners module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.runners">google.adk.runners module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.sessions">google.adk.sessions module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.tools">google.adk.tools package</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          
<section class="genindex-section">
  <h1 id="index">Index</h1>
  <div class="genindex-jumpbox"><a href="#A"><strong>A</strong></a> | <a href="#B"><strong>B</strong></a> | <a href="#C"><strong>C</strong></a> | <a href="#D"><strong>D</strong></a> | <a href="#E"><strong>E</strong></a> | <a href="#F"><strong>F</strong></a> | <a href="#G"><strong>G</strong></a> | <a href="#H"><strong>H</strong></a> | <a href="#I"><strong>I</strong></a> | <a href="#L"><strong>L</strong></a> | <a href="#M"><strong>M</strong></a> | <a href="#N"><strong>N</strong></a> | <a href="#O"><strong>O</strong></a> | <a href="#P"><strong>P</strong></a> | <a href="#R"><strong>R</strong></a> | <a href="#S"><strong>S</strong></a> | <a href="#T"><strong>T</strong></a> | <a href="#U"><strong>U</strong></a> | <a href="#V"><strong>V</strong></a></div>
</section>
<section id="A" class="genindex-section">
  <h2>A</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.events.Event.actions">actions (google.adk.events.Event attribute)</a>, <a href="google-adk.html#id17">[1]</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.ToolContext.actions">(google.adk.tools.ToolContext property)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.add_input_files">add_input_files() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.add_processed_file_names">add_processed_file_names() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.memory.BaseMemoryService.add_session_to_memory">add_session_to_memory() (google.adk.memory.BaseMemoryService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.memory.InMemoryMemoryService.add_session_to_memory">(google.adk.memory.InMemoryMemoryService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.memory.VertexAiRagMemoryService.add_session_to_memory">(google.adk.memory.VertexAiRagMemoryService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.adk_to_mcp_tool_type">adk_to_mcp_tool_type() (in module google.adk.tools.mcp_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.after_agent_callback">after_agent_callback (google.adk.agents.BaseAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.after_model_callback">after_model_callback (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.after_tool_callback">after_tool_callback (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.InMemoryRunner.agent">agent (google.adk.runners.InMemoryRunner attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.runners.Runner.agent">(google.adk.runners.Runner attribute)</a>, <a href="google-adk.html#id31">[1]</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.agents.Agent">Agent (in module google.adk.agents)</a>
</li>
        <li><a href="google-adk.html#google.adk.evaluation.AgentEvaluator">AgentEvaluator (class in google.adk.evaluation)</a>
</li>
        <li><a href="google-adk.html#google.adk.models.Gemini.api_client">api_client (google.adk.models.Gemini property)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.APIHubToolset">APIHubToolset (class in google.adk.tools)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.InMemoryRunner.app_name">app_name (google.adk.runners.InMemoryRunner attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.runners.Runner.app_name">(google.adk.runners.Runner attribute)</a>, <a href="google-adk.html#id32">[1]</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.Session.app_name">(google.adk.sessions.Session attribute)</a>, <a href="google-adk.html#id36">[1]</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.sessions.State.APP_PREFIX">APP_PREFIX (google.adk.sessions.State attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.BaseSessionService.append_event">append_event() (google.adk.sessions.BaseSessionService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.DatabaseSessionService.append_event">(google.adk.sessions.DatabaseSessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.InMemorySessionService.append_event">(google.adk.sessions.InMemorySessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.VertexAiSessionService.append_event">(google.adk.sessions.VertexAiSessionService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.application_integration_tool.ApplicationIntegrationToolset">ApplicationIntegrationToolset (class in google.adk.tools.application_integration_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.planners.BuiltInPlanner.apply_thinking_config">apply_thinking_config() (google.adk.planners.BuiltInPlanner method)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.EventActions.artifact_delta">artifact_delta (google.adk.events.EventActions attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.Runner.artifact_service">artifact_service (google.adk.runners.Runner attribute)</a>, <a href="google-adk.html#id33">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.artifacts">artifacts (google.adk.artifacts.InMemoryArtifactService attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.AuthToolArguments.auth_config">auth_config (google.adk.tools.AuthToolArguments attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.author">author (google.adk.events.Event attribute)</a>, <a href="google-adk.html#id18">[1]</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="B" class="genindex-section">
  <h2>B</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.base_url">base_url (google.adk.code_executors.ContainerCodeExecutor attribute)</a>, <a href="google-adk.html#id13">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.artifacts.BaseArtifactService">BaseArtifactService (class in google.adk.artifacts)</a>
</li>
        <li><a href="google-adk.html#google.adk.examples.BaseExampleProvider">BaseExampleProvider (class in google.adk.examples)</a>
</li>
        <li><a href="google-adk.html#google.adk.memory.BaseMemoryService">BaseMemoryService (class in google.adk.memory)</a>
</li>
        <li><a href="google-adk.html#google.adk.planners.BasePlanner">BasePlanner (class in google.adk.planners)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.retrieval.BaseRetrievalTool">BaseRetrievalTool (class in google.adk.tools.retrieval)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.BaseSessionService">BaseSessionService (class in google.adk.sessions)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.BaseTool">BaseTool (class in google.adk.tools)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.before_agent_callback">before_agent_callback (google.adk.agents.BaseAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.before_model_callback">before_model_callback (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.before_tool_callback">before_tool_callback (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.branch">branch (google.adk.events.Event attribute)</a>, <a href="google-adk.html#id19">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.planners.BasePlanner.build_planning_instruction">build_planning_instruction() (google.adk.planners.BasePlanner method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.planners.BuiltInPlanner.build_planning_instruction">(google.adk.planners.BuiltInPlanner method)</a>
</li>
          <li><a href="google-adk.html#google.adk.planners.PlanReActPlanner.build_planning_instruction">(google.adk.planners.PlanReActPlanner method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.planners.BuiltInPlanner">BuiltInPlanner (class in google.adk.planners)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="C" class="genindex-section">
  <h2>C</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.call">call() (google.adk.tools.openapi_tool.RestApiTool method)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.canonical_after_model_callbacks">canonical_after_model_callbacks (google.adk.agents.LlmAgent property)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.canonical_before_model_callbacks">canonical_before_model_callbacks (google.adk.agents.LlmAgent property)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.canonical_global_instruction">canonical_global_instruction() (google.adk.agents.LlmAgent method)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.canonical_instruction">canonical_instruction() (google.adk.agents.LlmAgent method)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.canonical_model">canonical_model (google.adk.agents.LlmAgent property)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.canonical_tools">canonical_tools (google.adk.agents.LlmAgent property)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.clear_input_files">clear_input_files() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.Runner.close_session">close_session() (google.adk.runners.Runner method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.BaseSessionService.close_session">(google.adk.sessions.BaseSessionService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.code_block_delimiters">code_block_delimiters (google.adk.code_executors.BaseCodeExecutor attribute)</a>, <a href="google-adk.html#id0">[1]</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.code_executor">code_executor (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext">CodeExecutorContext (class in google.adk.code_executors)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.configure_auth_credential">configure_auth_credential() (google.adk.tools.openapi_tool.RestApiTool method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.configure_auth_scheme">configure_auth_scheme() (google.adk.tools.openapi_tool.RestApiTool method)</a>
</li>
        <li><a href="google-adk.html#google.adk.models.BaseLlm.connect">connect() (google.adk.models.BaseLlm method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.models.Gemini.connect">(google.adk.models.Gemini method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.connection_params">connection_params (google.adk.tools.mcp_tool.MCPToolset attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.BaseSessionService.create_session">create_session() (google.adk.sessions.BaseSessionService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.DatabaseSessionService.create_session">(google.adk.sessions.DatabaseSessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.InMemorySessionService.create_session">(google.adk.sessions.InMemorySessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.VertexAiSessionService.create_session">(google.adk.sessions.VertexAiSessionService method)</a>
</li>
        </ul></li>
    </ul></td>
  </tr></table>
</section>

<section id="D" class="genindex-section">
  <h2>D</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.VertexAiSearchTool.data_store_id">data_store_id (google.adk.tools.VertexAiSearchTool attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.DatabaseSessionService">DatabaseSessionService (class in google.adk.sessions)</a>
</li>
        <li><a href="google-adk.html#google.adk.artifacts.BaseArtifactService.delete_artifact">delete_artifact() (google.adk.artifacts.BaseArtifactService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.artifacts.GcsArtifactService.delete_artifact">(google.adk.artifacts.GcsArtifactService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.delete_artifact">(google.adk.artifacts.InMemoryArtifactService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.sessions.BaseSessionService.delete_session">delete_session() (google.adk.sessions.BaseSessionService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.DatabaseSessionService.delete_session">(google.adk.sessions.DatabaseSessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.InMemorySessionService.delete_session">(google.adk.sessions.InMemorySessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.VertexAiSessionService.delete_session">(google.adk.sessions.VertexAiSessionService method)</a>
</li>
        </ul></li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.description">description (google.adk.agents.BaseAgent attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.BaseTool.description">(google.adk.tools.BaseTool attribute)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.disallow_transfer_to_parent">disallow_transfer_to_parent (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.disallow_transfer_to_peers">disallow_transfer_to_peers (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.docker_path">docker_path (google.adk.code_executors.ContainerCodeExecutor attribute)</a>, <a href="google-adk.html#id14">[1]</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="E" class="genindex-section">
  <h2>E</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.error_retry_attempts">error_retry_attempts (google.adk.code_executors.BaseCodeExecutor attribute)</a>, <a href="google-adk.html#id9">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.events.EventActions.escalate">escalate (google.adk.events.EventActions attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.evaluation.AgentEvaluator.evaluate">evaluate() (google.adk.evaluation.AgentEvaluator static method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.ToolContext.event_actions">event_actions (google.adk.tools.ToolContext attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.Session.events">events (google.adk.sessions.Session attribute)</a>, <a href="google-adk.html#id37">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.examples">examples (google.adk.agents.LlmAgent attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.ExampleTool.examples">(google.adk.tools.ExampleTool attribute)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.ExampleTool">ExampleTool (class in google.adk.tools)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool.EXCLUDE_FIELDS">EXCLUDE_FIELDS (google.adk.tools.application_integration_tool.IntegrationConnectorTool attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.execute_code">execute_code() (google.adk.code_executors.BaseCodeExecutor method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.execute_code">(google.adk.code_executors.ContainerCodeExecutor method)</a>
</li>
          <li><a href="google-adk.html#google.adk.code_executors.UnsafeLocalCodeExecutor.execute_code">(google.adk.code_executors.UnsafeLocalCodeExecutor method)</a>
</li>
          <li><a href="google-adk.html#google.adk.code_executors.VertexAiCodeExecutor.execute_code">(google.adk.code_executors.VertexAiCodeExecutor method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.execution_result_delimiters">execution_result_delimiters (google.adk.code_executors.BaseCodeExecutor attribute)</a>, <a href="google-adk.html#id10">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.exit_loop">exit_loop() (in module google.adk.tools)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.exit_stack">exit_stack (google.adk.tools.mcp_tool.MCPToolset attribute)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="F" class="genindex-section">
  <h2>F</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.retrieval.FilesRetrieval">FilesRetrieval (class in google.adk.tools.retrieval)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.find_agent">find_agent() (google.adk.agents.BaseAgent method)</a>
</li>
        <li><a href="google-adk.html#google.adk.evaluation.AgentEvaluator.find_config_for_test_file">find_config_for_test_file() (google.adk.evaluation.AgentEvaluator static method)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.find_sub_agent">find_sub_agent() (google.adk.agents.BaseAgent method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.from_parsed_operation">from_parsed_operation() (google.adk.tools.openapi_tool.RestApiTool class method)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.from_parsed_operation_str">from_parsed_operation_str() (google.adk.tools.openapi_tool.RestApiTool class method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.from_server">from_server() (google.adk.tools.mcp_tool.MCPToolset class method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.FunctionTool.func">func (google.adk.tools.FunctionTool attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.AuthToolArguments.function_call_id">function_call_id (google.adk.tools.AuthToolArguments attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.ToolContext.function_call_id">(google.adk.tools.ToolContext attribute)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.FunctionTool">FunctionTool (class in google.adk.tools)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="G" class="genindex-section">
  <h2>G</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.artifacts.GcsArtifactService">GcsArtifactService (class in google.adk.artifacts)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.gemini_to_json_schema">gemini_to_json_schema() (in module google.adk.tools.mcp_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.models.BaseLlm.generate_content_async">generate_content_async() (google.adk.models.BaseLlm method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.models.Gemini.generate_content_async">(google.adk.models.Gemini method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.generate_content_config">generate_content_config (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.State.get">get() (google.adk.sessions.State method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.ToolContext.get_auth_response">get_auth_response() (google.adk.tools.ToolContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_error_count">get_error_count() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.examples.BaseExampleProvider.get_examples">get_examples() (google.adk.examples.BaseExampleProvider method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.examples.VertexAiExampleStore.get_examples">(google.adk.examples.VertexAiExampleStore method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_execution_id">get_execution_id() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.get_function_calls">get_function_calls (google.adk.events.Event attribute)</a>
</li>
        <li><a href="google-adk.html#id24">get_function_calls() (google.adk.events.Event method)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.get_function_responses">get_function_responses() (google.adk.events.Event method)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_input_files">get_input_files() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_processed_file_names">get_processed_file_names() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.BaseSessionService.get_session">get_session() (google.adk.sessions.BaseSessionService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.DatabaseSessionService.get_session">(google.adk.sessions.DatabaseSessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.InMemorySessionService.get_session">(google.adk.sessions.InMemorySessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.VertexAiSessionService.get_session">(google.adk.sessions.VertexAiSessionService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_state_delta">get_state_delta() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.APIHubToolset.get_tool">get_tool() (google.adk.tools.APIHubToolset method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.openapi_tool.OpenAPIToolset.get_tool">(google.adk.tools.openapi_tool.OpenAPIToolset method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.APIHubToolset.get_tools">get_tools() (google.adk.tools.APIHubToolset method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.application_integration_tool.ApplicationIntegrationToolset.get_tools">(google.adk.tools.application_integration_tool.ApplicationIntegrationToolset method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.openapi_tool.OpenAPIToolset.get_tools">(google.adk.tools.openapi_tool.OpenAPIToolset method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.global_instruction">global_instruction (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li>
    google.adk.agents

        <ul>
          <li><a href="google-adk.html#module-google.adk.agents">module</a>
</li>
        </ul></li>
        <li>
    google.adk.artifacts

        <ul>
          <li><a href="google-adk.html#module-google.adk.artifacts">module</a>
</li>
        </ul></li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li>
    google.adk.code_executors

        <ul>
          <li><a href="google-adk.html#module-google.adk.code_executors">module</a>
</li>
        </ul></li>
        <li>
    google.adk.evaluation

        <ul>
          <li><a href="google-adk.html#module-google.adk.evaluation">module</a>
</li>
        </ul></li>
        <li>
    google.adk.events

        <ul>
          <li><a href="google-adk.html#module-google.adk.events">module</a>
</li>
        </ul></li>
        <li>
    google.adk.examples

        <ul>
          <li><a href="google-adk.html#module-google.adk.examples">module</a>
</li>
        </ul></li>
        <li>
    google.adk.memory

        <ul>
          <li><a href="google-adk.html#module-google.adk.memory">module</a>
</li>
        </ul></li>
        <li>
    google.adk.models

        <ul>
          <li><a href="google-adk.html#module-google.adk.models">module</a>
</li>
        </ul></li>
        <li>
    google.adk.planners

        <ul>
          <li><a href="google-adk.html#module-google.adk.planners">module</a>
</li>
        </ul></li>
        <li>
    google.adk.runners

        <ul>
          <li><a href="google-adk.html#module-google.adk.runners">module</a>
</li>
        </ul></li>
        <li>
    google.adk.sessions

        <ul>
          <li><a href="google-adk.html#module-google.adk.sessions">module</a>
</li>
        </ul></li>
        <li>
    google.adk.tools

        <ul>
          <li><a href="google-adk.html#module-google.adk.tools">module</a>
</li>
        </ul></li>
        <li>
    google.adk.tools.application_integration_tool

        <ul>
          <li><a href="google-adk.html#module-google.adk.tools.application_integration_tool">module</a>
</li>
        </ul></li>
        <li>
    google.adk.tools.google_api_tool

        <ul>
          <li><a href="google-adk.html#module-google.adk.tools.google_api_tool">module</a>
</li>
        </ul></li>
        <li>
    google.adk.tools.mcp_tool

        <ul>
          <li><a href="google-adk.html#module-google.adk.tools.mcp_tool">module</a>
</li>
        </ul></li>
        <li>
    google.adk.tools.openapi_tool

        <ul>
          <li><a href="google-adk.html#module-google.adk.tools.openapi_tool">module</a>
</li>
        </ul></li>
        <li>
    google.adk.tools.retrieval

        <ul>
          <li><a href="google-adk.html#module-google.adk.tools.retrieval">module</a>
</li>
        </ul></li>
    </ul></td>
  </tr></table>
</section>

<section id="H" class="genindex-section">
  <h2>H</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.sessions.State.has_delta">has_delta() (google.adk.sessions.State method)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.events.Event.has_trailing_code_execution_result">has_trailing_code_execution_result() (google.adk.events.Event method)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="I" class="genindex-section">
  <h2>I</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.events.Event.id">id (google.adk.events.Event attribute)</a>, <a href="google-adk.html#id20">[1]</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.Session.id">(google.adk.sessions.Session attribute)</a>, <a href="google-adk.html#id38">[1]</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.image">image (google.adk.code_executors.ContainerCodeExecutor attribute)</a>, <a href="google-adk.html#id15">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.include_contents">include_contents (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.increment_error_count">increment_error_count() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.memory.InMemoryMemoryService">InMemoryMemoryService (class in google.adk.memory)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.InMemoryRunner">InMemoryRunner (class in google.adk.runners)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.InMemorySessionService">InMemorySessionService (class in google.adk.sessions)</a>
</li>
        <li><a href="google-adk.html#google.adk.examples.Example.input">input (google.adk.examples.Example attribute)</a>, <a href="google-adk.html#id26">[1]</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.input_schema">input_schema (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.instruction">instruction (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool">IntegrationConnectorTool (class in google.adk.tools.application_integration_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.ToolContext.invocation_context">invocation_context (google.adk.tools.ToolContext attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.invocation_id">invocation_id (google.adk.events.Event attribute)</a>, <a href="google-adk.html#id21">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.is_final_response">is_final_response (google.adk.events.Event attribute)</a>
</li>
        <li><a href="google-adk.html#id25">is_final_response() (google.adk.events.Event method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.BaseTool.is_long_running">is_long_running (google.adk.tools.BaseTool attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.LongRunningFunctionTool.is_long_running">(google.adk.tools.LongRunningFunctionTool attribute)</a>
</li>
        </ul></li>
    </ul></td>
  </tr></table>
</section>

<section id="L" class="genindex-section">
  <h2>L</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.sessions.Session.last_update_time">last_update_time (google.adk.sessions.Session attribute)</a>, <a href="google-adk.html#id39">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.artifacts.BaseArtifactService.list_artifact_keys">list_artifact_keys() (google.adk.artifacts.BaseArtifactService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.artifacts.GcsArtifactService.list_artifact_keys">(google.adk.artifacts.GcsArtifactService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.list_artifact_keys">(google.adk.artifacts.InMemoryArtifactService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.ToolContext.list_artifacts">list_artifacts() (google.adk.tools.ToolContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.BaseSessionService.list_events">list_events() (google.adk.sessions.BaseSessionService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.DatabaseSessionService.list_events">(google.adk.sessions.DatabaseSessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.InMemorySessionService.list_events">(google.adk.sessions.InMemorySessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.VertexAiSessionService.list_events">(google.adk.sessions.VertexAiSessionService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.sessions.BaseSessionService.list_sessions">list_sessions() (google.adk.sessions.BaseSessionService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.sessions.DatabaseSessionService.list_sessions">(google.adk.sessions.DatabaseSessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.InMemorySessionService.list_sessions">(google.adk.sessions.InMemorySessionService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.sessions.VertexAiSessionService.list_sessions">(google.adk.sessions.VertexAiSessionService method)</a>
</li>
        </ul></li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.artifacts.BaseArtifactService.list_versions">list_versions() (google.adk.artifacts.BaseArtifactService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.artifacts.GcsArtifactService.list_versions">(google.adk.artifacts.GcsArtifactService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.list_versions">(google.adk.artifacts.InMemoryArtifactService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.retrieval.LlamaIndexRetrieval">LlamaIndexRetrieval (class in google.adk.tools.retrieval)</a>
</li>
        <li><a href="google-adk.html#google.adk.models.LLMRegistry">LLMRegistry (class in google.adk.models)</a>
</li>
        <li><a href="google-adk.html#google.adk.artifacts.BaseArtifactService.load_artifact">load_artifact() (google.adk.artifacts.BaseArtifactService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.artifacts.GcsArtifactService.load_artifact">(google.adk.artifacts.GcsArtifactService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.load_artifact">(google.adk.artifacts.InMemoryArtifactService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.load_tools">load_tools() (google.adk.tools.mcp_tool.MCPToolset method)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.long_running_tool_ids">long_running_tool_ids (google.adk.events.Event attribute)</a>, <a href="google-adk.html#id22">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.LongRunningFunctionTool">LongRunningFunctionTool (class in google.adk.tools)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="M" class="genindex-section">
  <h2>M</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.LoopAgent.max_iterations">max_iterations (google.adk.agents.LoopAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPTool">MCPTool (class in google.adk.tools.mcp_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset">MCPToolset (class in google.adk.tools.mcp_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.Runner.memory_service">memory_service (google.adk.runners.Runner attribute)</a>, <a href="google-adk.html#id34">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.model">model (google.adk.agents.LlmAgent attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.models.BaseLlm.model">(google.adk.models.BaseLlm attribute)</a>, <a href="google-adk.html#id28">[1]</a>
</li>
          <li><a href="google-adk.html#google.adk.models.Gemini.model">(google.adk.models.Gemini attribute)</a>, <a href="google-adk.html#id29">[1]</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.model_post_init">model_post_init() (google.adk.agents.BaseAgent method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.model_post_init">(google.adk.code_executors.ContainerCodeExecutor method)</a>
</li>
          <li><a href="google-adk.html#google.adk.code_executors.VertexAiCodeExecutor.model_post_init">(google.adk.code_executors.VertexAiCodeExecutor method)</a>
</li>
          <li><a href="google-adk.html#google.adk.events.Event.model_post_init">(google.adk.events.Event method)</a>
</li>
        </ul></li>
        <li>
    module

        <ul>
          <li><a href="google-adk.html#module-google.adk.agents">google.adk.agents</a>
</li>
          <li><a href="google-adk.html#module-google.adk.artifacts">google.adk.artifacts</a>
</li>
          <li><a href="google-adk.html#module-google.adk.code_executors">google.adk.code_executors</a>
</li>
          <li><a href="google-adk.html#module-google.adk.evaluation">google.adk.evaluation</a>
</li>
          <li><a href="google-adk.html#module-google.adk.events">google.adk.events</a>
</li>
          <li><a href="google-adk.html#module-google.adk.examples">google.adk.examples</a>
</li>
          <li><a href="google-adk.html#module-google.adk.memory">google.adk.memory</a>
</li>
          <li><a href="google-adk.html#module-google.adk.models">google.adk.models</a>
</li>
          <li><a href="google-adk.html#module-google.adk.planners">google.adk.planners</a>
</li>
          <li><a href="google-adk.html#module-google.adk.runners">google.adk.runners</a>
</li>
          <li><a href="google-adk.html#module-google.adk.sessions">google.adk.sessions</a>
</li>
          <li><a href="google-adk.html#module-google.adk.tools">google.adk.tools</a>
</li>
          <li><a href="google-adk.html#module-google.adk.tools.application_integration_tool">google.adk.tools.application_integration_tool</a>
</li>
          <li><a href="google-adk.html#module-google.adk.tools.google_api_tool">google.adk.tools.google_api_tool</a>
</li>
          <li><a href="google-adk.html#module-google.adk.tools.mcp_tool">google.adk.tools.mcp_tool</a>
</li>
          <li><a href="google-adk.html#module-google.adk.tools.openapi_tool">google.adk.tools.openapi_tool</a>
</li>
          <li><a href="google-adk.html#module-google.adk.tools.retrieval">google.adk.tools.retrieval</a>
</li>
        </ul></li>
    </ul></td>
  </tr></table>
</section>

<section id="N" class="genindex-section">
  <h2>N</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.name">name (google.adk.agents.BaseAgent attribute)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.BaseTool.name">(google.adk.tools.BaseTool attribute)</a>
</li>
        </ul></li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.events.Event.new_id">new_id() (google.adk.events.Event static method)</a>
</li>
        <li><a href="google-adk.html#google.adk.models.LLMRegistry.new_llm">new_llm() (google.adk.models.LLMRegistry static method)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="O" class="genindex-section">
  <h2>O</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.openapi_tool.OpenAPIToolset">OpenAPIToolset (class in google.adk.tools.openapi_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.optimize_data_file">optimize_data_file (google.adk.code_executors.BaseCodeExecutor attribute)</a>, <a href="google-adk.html#id11">[1]</a>

        <ul>
          <li><a href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.optimize_data_file">(google.adk.code_executors.ContainerCodeExecutor attribute)</a>
</li>
          <li><a href="google-adk.html#google.adk.code_executors.UnsafeLocalCodeExecutor.optimize_data_file">(google.adk.code_executors.UnsafeLocalCodeExecutor attribute)</a>
</li>
        </ul></li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool.OPTIONAL_FIELDS">OPTIONAL_FIELDS (google.adk.tools.application_integration_tool.IntegrationConnectorTool attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.examples.Example.output">output (google.adk.examples.Example attribute)</a>, <a href="google-adk.html#id27">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.output_key">output_key (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.output_schema">output_schema (google.adk.agents.LlmAgent attribute)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="P" class="genindex-section">
  <h2>P</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.parent_agent">parent_agent (google.adk.agents.BaseAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.planner">planner (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.planners.PlanReActPlanner">PlanReActPlanner (class in google.adk.planners)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.BaseTool.process_llm_request">process_llm_request() (google.adk.tools.BaseTool method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.tools.ExampleTool.process_llm_request">(google.adk.tools.ExampleTool method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.retrieval.VertexAiRagRetrieval.process_llm_request">(google.adk.tools.retrieval.VertexAiRagRetrieval method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.VertexAiSearchTool.process_llm_request">(google.adk.tools.VertexAiSearchTool method)</a>
</li>
        </ul></li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.planners.BasePlanner.process_planning_response">process_planning_response() (google.adk.planners.BasePlanner method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.planners.BuiltInPlanner.process_planning_response">(google.adk.planners.BuiltInPlanner method)</a>
</li>
          <li><a href="google-adk.html#google.adk.planners.PlanReActPlanner.process_planning_response">(google.adk.planners.PlanReActPlanner method)</a>
</li>
        </ul></li>
    </ul></td>
  </tr></table>
</section>

<section id="R" class="genindex-section">
  <h2>R</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.models.LLMRegistry.register">register() (google.adk.models.LLMRegistry static method)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.ToolContext.request_credential">request_credential() (google.adk.tools.ToolContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.EventActions.requested_auth_configs">requested_auth_configs (google.adk.events.EventActions attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.reset_error_count">reset_error_count() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.models.LLMRegistry.resolve">resolve() (google.adk.models.LLMRegistry static method)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.VertexAiCodeExecutor.resource_name">resource_name (google.adk.code_executors.VertexAiCodeExecutor attribute)</a>, <a href="google-adk.html#id16">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool">RestApiTool (class in google.adk.tools.openapi_tool)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.root_agent">root_agent (google.adk.agents.BaseAgent property)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.Runner.run">run() (google.adk.runners.Runner method)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.run_async">run_async() (google.adk.agents.BaseAgent method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.runners.Runner.run_async">(google.adk.runners.Runner method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool.run_async">(google.adk.tools.application_integration_tool.IntegrationConnectorTool method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.BaseTool.run_async">(google.adk.tools.BaseTool method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.FunctionTool.run_async">(google.adk.tools.FunctionTool method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPTool.run_async">(google.adk.tools.mcp_tool.MCPTool method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.run_async">(google.adk.tools.openapi_tool.RestApiTool method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.retrieval.LlamaIndexRetrieval.run_async">(google.adk.tools.retrieval.LlamaIndexRetrieval method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.retrieval.VertexAiRagRetrieval.run_async">(google.adk.tools.retrieval.VertexAiRagRetrieval method)</a>
</li>
        </ul></li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.run_live">run_live() (google.adk.agents.BaseAgent method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.runners.Runner.run_live">(google.adk.runners.Runner method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.runners.Runner">Runner (class in google.adk.runners)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="S" class="genindex-section">
  <h2>S</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.artifacts.BaseArtifactService.save_artifact">save_artifact() (google.adk.artifacts.BaseArtifactService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.artifacts.GcsArtifactService.save_artifact">(google.adk.artifacts.GcsArtifactService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.save_artifact">(google.adk.artifacts.InMemoryArtifactService method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.VertexAiSearchTool.search_engine_id">search_engine_id (google.adk.tools.VertexAiSearchTool attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.memory.BaseMemoryService.search_memory">search_memory() (google.adk.memory.BaseMemoryService method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.memory.InMemoryMemoryService.search_memory">(google.adk.memory.InMemoryMemoryService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.memory.VertexAiRagMemoryService.search_memory">(google.adk.memory.VertexAiRagMemoryService method)</a>
</li>
          <li><a href="google-adk.html#google.adk.tools.ToolContext.search_memory">(google.adk.tools.ToolContext method)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.session">session (google.adk.tools.mcp_tool.MCPToolset attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.memory.InMemoryMemoryService.session_events">session_events (google.adk.memory.InMemoryMemoryService attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.runners.Runner.session_service">session_service (google.adk.runners.Runner attribute)</a>, <a href="google-adk.html#id35">[1]</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.set_execution_id">set_execution_id() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.EventActions.skip_summarization">skip_summarization (google.adk.events.EventActions attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.State">State (class in google.adk.sessions)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.Session.state">state (google.adk.sessions.Session attribute)</a>, <a href="google-adk.html#id40">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.events.EventActions.state_delta">state_delta (google.adk.events.EventActions attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.stateful">stateful (google.adk.code_executors.BaseCodeExecutor attribute)</a>, <a href="google-adk.html#id12">[1]</a>

        <ul>
          <li><a href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.stateful">(google.adk.code_executors.ContainerCodeExecutor attribute)</a>
</li>
          <li><a href="google-adk.html#google.adk.code_executors.UnsafeLocalCodeExecutor.stateful">(google.adk.code_executors.UnsafeLocalCodeExecutor attribute)</a>
</li>
        </ul></li>
        <li><a href="google-adk.html#google.adk.agents.BaseAgent.sub_agents">sub_agents (google.adk.agents.BaseAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.models.BaseLlm.supported_models">supported_models() (google.adk.models.BaseLlm class method)</a>

        <ul>
          <li><a href="google-adk.html#google.adk.models.Gemini.supported_models">(google.adk.models.Gemini static method)</a>
</li>
        </ul></li>
    </ul></td>
  </tr></table>
</section>

<section id="T" class="genindex-section">
  <h2>T</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.sessions.State.TEMP_PREFIX">TEMP_PREFIX (google.adk.sessions.State attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.planners.BuiltInPlanner.thinking_config">thinking_config (google.adk.planners.BuiltInPlanner attribute)</a>, <a href="google-adk.html#id30">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.events.Event.timestamp">timestamp (google.adk.events.Event attribute)</a>, <a href="google-adk.html#id23">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.State.to_dict">to_dict() (google.adk.sessions.State method)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.ToolContext">ToolContext (class in google.adk.tools)</a>
</li>
        <li><a href="google-adk.html#google.adk.agents.LlmAgent.tools">tools (google.adk.agents.LlmAgent attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.events.EventActions.transfer_to_agent">transfer_to_agent (google.adk.events.EventActions attribute)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.transfer_to_agent">transfer_to_agent() (in module google.adk.tools)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="U" class="genindex-section">
  <h2>U</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.sessions.State.update">update() (google.adk.sessions.State method)</a>
</li>
        <li><a href="google-adk.html#google.adk.code_executors.CodeExecutorContext.update_code_execution_result">update_code_execution_result() (google.adk.code_executors.CodeExecutorContext method)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.sessions.Session.user_id">user_id (google.adk.sessions.Session attribute)</a>, <a href="google-adk.html#id41">[1]</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.State.USER_PREFIX">USER_PREFIX (google.adk.sessions.State attribute)</a>
</li>
    </ul></td>
  </tr></table>
</section>

<section id="V" class="genindex-section">
  <h2>V</h2>
  <table style="width: 100%" class="indextable genindextable"><tr>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.examples.VertexAiExampleStore">VertexAiExampleStore (class in google.adk.examples)</a>
</li>
        <li><a href="google-adk.html#google.adk.memory.VertexAiRagMemoryService">VertexAiRagMemoryService (class in google.adk.memory)</a>
</li>
    </ul></td>
    <td style="width: 33%; vertical-align: top;"><ul>
        <li><a href="google-adk.html#google.adk.tools.retrieval.VertexAiRagRetrieval">VertexAiRagRetrieval (class in google.adk.tools.retrieval)</a>
</li>
        <li><a href="google-adk.html#google.adk.tools.VertexAiSearchTool">VertexAiSearchTool (class in google.adk.tools)</a>
</li>
        <li><a href="google-adk.html#google.adk.sessions.VertexAiSessionService">VertexAiSessionService (class in google.adk.sessions)</a>
</li>
    </ul></td>
  </tr></table>
</section>


        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2025, Google
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="_static/documentation_options.js?v=5929fcd5"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/scripts/furo.js?v=5fa4622c"></script>
    </body>
</html>