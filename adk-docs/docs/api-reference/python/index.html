<!doctype html>
<html class="no-js" lang="en" data-content_root="./">
  <head><meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width,initial-scale=1"/>
    <meta name="color-scheme" content="light dark"><meta name="viewport" content="width=device-width, initial-scale=1" />
<link rel="index" title="Index" href="genindex.html" /><link rel="search" title="Search" href="search.html" /><link rel="next" title="Submodules" href="google-adk.html" />

    <!-- Generated with Sphinx 8.2.3 and Furo 2024.08.06 -->
        <title>Agent Development Kit documentation</title>
      <link rel="stylesheet" type="text/css" href="_static/pygments.css?v=a746c00c" />
    <link rel="stylesheet" type="text/css" href="_static/styles/furo.css?v=354aac6f" />
    <link rel="stylesheet" type="text/css" href="_static/autodoc_pydantic.css?v=9e08a393" />
    <link rel="stylesheet" type="text/css" href="_static/styles/furo-extensions.css?v=302659d7" />
    <link rel="stylesheet" type="text/css" href="_static/css/custom.css" />
    
    


<style>
  body {
    --color-code-background: #f8f8f8;
  --color-code-foreground: black;
  --font-stack: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --font-stack--monospace: Roboto Mono, "Helvetica Neue Mono", monospace;
  --font-stack--headings: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --color-brand-primary: black;
  --color-brand-content: black;
  
  }
  @media not print {
    body[data-theme="dark"] {
      --color-code-background: #202020;
  --color-code-foreground: #d0d0d0;
  --font-stack: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --font-stack--monospace: Roboto Mono, "Helvetica Neue Mono", monospace;
  --font-stack--headings: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --color-brand-primary: white;
  --color-brand-content: white;
  
    }
    @media (prefers-color-scheme: dark) {
      body:not([data-theme="light"]) {
        --color-code-background: #202020;
  --color-code-foreground: #d0d0d0;
  --font-stack: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --font-stack--monospace: Roboto Mono, "Helvetica Neue Mono", monospace;
  --font-stack--headings: "Google Sans Text", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --color-brand-primary: white;
  --color-brand-content: white;
  
      }
    }
  }
</style></head>
  <body>
    
    <script>
      document.body.dataset.theme = localStorage.getItem("theme") || "auto";
    </script>
    

<svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
  <symbol id="svg-toc" viewBox="0 0 24 24">
    <title>Contents</title>
    <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 1024 1024">
      <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 0 0 0 13.8z"/>
    </svg>
  </symbol>
  <symbol id="svg-menu" viewBox="0 0 24 24">
    <title>Menu</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-menu">
      <line x1="3" y1="12" x2="21" y2="12"></line>
      <line x1="3" y1="6" x2="21" y2="6"></line>
      <line x1="3" y1="18" x2="21" y2="18"></line>
    </svg>
  </symbol>
  <symbol id="svg-arrow-right" viewBox="0 0 24 24">
    <title>Expand</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather-chevron-right">
      <polyline points="9 18 15 12 9 6"></polyline>
    </svg>
  </symbol>
  <symbol id="svg-sun" viewBox="0 0 24 24">
    <title>Light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="feather-sun">
      <circle cx="12" cy="12" r="5"></circle>
      <line x1="12" y1="1" x2="12" y2="3"></line>
      <line x1="12" y1="21" x2="12" y2="23"></line>
      <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
      <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
      <line x1="1" y1="12" x2="3" y2="12"></line>
      <line x1="21" y1="12" x2="23" y2="12"></line>
      <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
      <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
    </svg>
  </symbol>
  <symbol id="svg-moon" viewBox="0 0 24 24">
    <title>Dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-moon">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M12 3c.132 0 .263 0 .393 0a7.5 7.5 0 0 0 7.92 12.446a9 9 0 1 1 -8.313 -12.454z" />
    </svg>
  </symbol>
  <symbol id="svg-sun-with-moon" viewBox="0 0 24 24">
    <title>Auto light/dark, in light mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path style="opacity: 50%" d="M 5.411 14.504 C 5.471 14.504 5.532 14.504 5.591 14.504 C 3.639 16.319 4.383 19.569 6.931 20.352 C 7.693 20.586 8.512 20.551 9.25 20.252 C 8.023 23.207 4.056 23.725 2.11 21.184 C 0.166 18.642 1.702 14.949 4.874 14.536 C 5.051 14.512 5.231 14.5 5.411 14.5 L 5.411 14.504 Z"/>
      <line x1="14.5" y1="3.25" x2="14.5" y2="1.25"/>
      <line x1="14.5" y1="15.85" x2="14.5" y2="17.85"/>
      <line x1="10.044" y1="5.094" x2="8.63" y2="3.68"/>
      <line x1="19" y1="14.05" x2="20.414" y2="15.464"/>
      <line x1="8.2" y1="9.55" x2="6.2" y2="9.55"/>
      <line x1="20.8" y1="9.55" x2="22.8" y2="9.55"/>
      <line x1="10.044" y1="14.006" x2="8.63" y2="15.42"/>
      <line x1="19" y1="5.05" x2="20.414" y2="3.636"/>
      <circle cx="14.5" cy="9.55" r="3.6"/>
    </svg>
  </symbol>
  <symbol id="svg-moon-with-sun" viewBox="0 0 24 24">
    <title>Auto light/dark, in dark mode</title>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round"
      class="icon-custom-derived-from-feather-sun-and-tabler-moon">
      <path d="M 8.282 7.007 C 8.385 7.007 8.494 7.007 8.595 7.007 C 5.18 10.184 6.481 15.869 10.942 17.24 C 12.275 17.648 13.706 17.589 15 17.066 C 12.851 22.236 5.91 23.143 2.505 18.696 C -0.897 14.249 1.791 7.786 7.342 7.063 C 7.652 7.021 7.965 7 8.282 7 L 8.282 7.007 Z"/>
      <line style="opacity: 50%" x1="18" y1="3.705" x2="18" y2="2.5"/>
      <line style="opacity: 50%" x1="18" y1="11.295" x2="18" y2="12.5"/>
      <line style="opacity: 50%" x1="15.316" y1="4.816" x2="14.464" y2="3.964"/>
      <line style="opacity: 50%" x1="20.711" y1="10.212" x2="21.563" y2="11.063"/>
      <line style="opacity: 50%" x1="14.205" y1="7.5" x2="13.001" y2="7.5"/>
      <line style="opacity: 50%" x1="21.795" y1="7.5" x2="23" y2="7.5"/>
      <line style="opacity: 50%" x1="15.316" y1="10.184" x2="14.464" y2="11.036"/>
      <line style="opacity: 50%" x1="20.711" y1="4.789" x2="21.563" y2="3.937"/>
      <circle style="opacity: 50%" cx="18" cy="7.5" r="2.169"/>
    </svg>
  </symbol>
  <symbol id="svg-pencil" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-pencil-code">
      <path d="M4 20h4l10.5 -10.5a2.828 2.828 0 1 0 -4 -4l-10.5 10.5v4" />
      <path d="M13.5 6.5l4 4" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
  <symbol id="svg-eye" viewBox="0 0 24 24">
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor"
      stroke-width="1" stroke-linecap="round" stroke-linejoin="round" class="icon-tabler-eye-code">
      <path stroke="none" d="M0 0h24v24H0z" fill="none" />
      <path d="M10 12a2 2 0 1 0 4 0a2 2 0 0 0 -4 0" />
      <path
        d="M11.11 17.958c-3.209 -.307 -5.91 -2.293 -8.11 -5.958c2.4 -4 5.4 -6 9 -6c3.6 0 6.6 2 9 6c-.21 .352 -.427 .688 -.647 1.008" />
      <path d="M20 21l2 -2l-2 -2" />
      <path d="M17 17l-2 2l2 2" />
    </svg>
  </symbol>
</svg>

<input type="checkbox" class="sidebar-toggle" name="__navigation" id="__navigation">
<input type="checkbox" class="sidebar-toggle" name="__toc" id="__toc">
<label class="overlay sidebar-overlay" for="__navigation">
  <div class="visually-hidden">Hide navigation sidebar</div>
</label>
<label class="overlay toc-overlay" for="__toc">
  <div class="visually-hidden">Hide table of contents sidebar</div>
</label>

<a class="skip-to-content muted-link" href="#furo-main-content">Skip to content</a>



<div class="page">
  <header class="mobile-header">
    <div class="header-left">
      <label class="nav-overlay-icon" for="__navigation">
        <div class="visually-hidden">Toggle site navigation sidebar</div>
        <i class="icon"><svg><use href="#svg-menu"></use></svg></i>
      </label>
    </div>
    <div class="header-center">
      <a href="#"><div class="brand">Agent Development Kit  documentation</div></a>
    </div>
    <div class="header-right">
      <div class="theme-toggle-container theme-toggle-header">
        <button class="theme-toggle">
          <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
          <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
          <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
          <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
          <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
        </button>
      </div>
      <label class="toc-overlay-icon toc-header-icon no-toc" for="__toc">
        <div class="visually-hidden">Toggle table of contents sidebar</div>
        <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
      </label>
    </div>
  </header>
  <aside class="sidebar-drawer">
    <div class="sidebar-container">
      
      <div class="sidebar-sticky"><a class="sidebar-brand" href="#">
  
  
  <span class="sidebar-brand-text">Agent Development Kit  documentation</span>
  
</a><form class="sidebar-search-container" method="get" action="search.html" role="search">
  <input class="sidebar-search" placeholder="Search" name="q" aria-label="Search">
  <input type="hidden" name="check_keywords" value="yes">
  <input type="hidden" name="area" value="default">
</form>
<div id="searchbox"></div><div class="sidebar-scroll"><div class="sidebar-tree">
  <ul>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html">Submodules</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.agents">google.adk.agents module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.artifacts">google.adk.artifacts module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.code_executors">google.adk.code_executors module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.evaluation">google.adk.evaluation module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.events">google.adk.events module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.examples">google.adk.examples module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.memory">google.adk.memory module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.models">google.adk.models module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.planners">google.adk.planners module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.runners">google.adk.runners module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.sessions">google.adk.sessions module</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.tools">google.adk.tools package</a></li>
</ul>

</div>
</div>

      </div>
      
    </div>
  </aside>
  <div class="main">
    <div class="content">
      <div class="article-container">
        <a href="#" class="back-to-top muted-link">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
            <path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8v12z"></path>
          </svg>
          <span>Back to top</span>
        </a>
        <div class="content-icon-container">
          <div class="view-this-page">
  <a class="muted-link" href="_sources/index.rst.txt" title="View this page">
    <svg><use href="#svg-eye"></use></svg>
    <span class="visually-hidden">View this page</span>
  </a>
</div>
<div class="theme-toggle-container theme-toggle-content">
            <button class="theme-toggle">
              <div class="visually-hidden">Toggle Light / Dark / Auto color theme</div>
              <svg class="theme-icon-when-auto-light"><use href="#svg-sun-with-moon"></use></svg>
              <svg class="theme-icon-when-auto-dark"><use href="#svg-moon-with-sun"></use></svg>
              <svg class="theme-icon-when-dark"><use href="#svg-moon"></use></svg>
              <svg class="theme-icon-when-light"><use href="#svg-sun"></use></svg>
            </button>
          </div>
          <label class="toc-overlay-icon toc-content-icon no-toc" for="__toc">
            <div class="visually-hidden">Toggle table of contents sidebar</div>
            <i class="icon"><svg><use href="#svg-toc"></use></svg></i>
          </label>
        </div>
        <article role="main" id="furo-main-content">
          <section id="google">
<h1>google<a class="headerlink" href="#google" title="Link to this heading">¶</a></h1>
<div class="toctree-wrapper compound">
<ul>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html">Submodules</a></li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.agents">google.adk.agents module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.agents.Agent"><code class="docutils literal notranslate"><span class="pre">Agent</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent"><code class="docutils literal notranslate"><span class="pre">BaseAgent</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.after_agent_callback"><code class="docutils literal notranslate"><span class="pre">BaseAgent.after_agent_callback</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.before_agent_callback"><code class="docutils literal notranslate"><span class="pre">BaseAgent.before_agent_callback</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.description"><code class="docutils literal notranslate"><span class="pre">BaseAgent.description</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.name"><code class="docutils literal notranslate"><span class="pre">BaseAgent.name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.parent_agent"><code class="docutils literal notranslate"><span class="pre">BaseAgent.parent_agent</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.sub_agents"><code class="docutils literal notranslate"><span class="pre">BaseAgent.sub_agents</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.find_agent"><code class="docutils literal notranslate"><span class="pre">BaseAgent.find_agent()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.find_sub_agent"><code class="docutils literal notranslate"><span class="pre">BaseAgent.find_sub_agent()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.model_post_init"><code class="docutils literal notranslate"><span class="pre">BaseAgent.model_post_init()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.run_async"><code class="docutils literal notranslate"><span class="pre">BaseAgent.run_async()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.run_live"><code class="docutils literal notranslate"><span class="pre">BaseAgent.run_live()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.BaseAgent.root_agent"><code class="docutils literal notranslate"><span class="pre">BaseAgent.root_agent</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent"><code class="docutils literal notranslate"><span class="pre">LlmAgent</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.after_model_callback"><code class="docutils literal notranslate"><span class="pre">LlmAgent.after_model_callback</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.after_tool_callback"><code class="docutils literal notranslate"><span class="pre">LlmAgent.after_tool_callback</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.before_model_callback"><code class="docutils literal notranslate"><span class="pre">LlmAgent.before_model_callback</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.before_tool_callback"><code class="docutils literal notranslate"><span class="pre">LlmAgent.before_tool_callback</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.code_executor"><code class="docutils literal notranslate"><span class="pre">LlmAgent.code_executor</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.disallow_transfer_to_parent"><code class="docutils literal notranslate"><span class="pre">LlmAgent.disallow_transfer_to_parent</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.disallow_transfer_to_peers"><code class="docutils literal notranslate"><span class="pre">LlmAgent.disallow_transfer_to_peers</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.examples"><code class="docutils literal notranslate"><span class="pre">LlmAgent.examples</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.generate_content_config"><code class="docutils literal notranslate"><span class="pre">LlmAgent.generate_content_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.global_instruction"><code class="docutils literal notranslate"><span class="pre">LlmAgent.global_instruction</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.include_contents"><code class="docutils literal notranslate"><span class="pre">LlmAgent.include_contents</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.input_schema"><code class="docutils literal notranslate"><span class="pre">LlmAgent.input_schema</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.instruction"><code class="docutils literal notranslate"><span class="pre">LlmAgent.instruction</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.model"><code class="docutils literal notranslate"><span class="pre">LlmAgent.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.output_key"><code class="docutils literal notranslate"><span class="pre">LlmAgent.output_key</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.output_schema"><code class="docutils literal notranslate"><span class="pre">LlmAgent.output_schema</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.planner"><code class="docutils literal notranslate"><span class="pre">LlmAgent.planner</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.tools"><code class="docutils literal notranslate"><span class="pre">LlmAgent.tools</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.canonical_global_instruction"><code class="docutils literal notranslate"><span class="pre">LlmAgent.canonical_global_instruction()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.canonical_instruction"><code class="docutils literal notranslate"><span class="pre">LlmAgent.canonical_instruction()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.canonical_after_model_callbacks"><code class="docutils literal notranslate"><span class="pre">LlmAgent.canonical_after_model_callbacks</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.canonical_before_model_callbacks"><code class="docutils literal notranslate"><span class="pre">LlmAgent.canonical_before_model_callbacks</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.canonical_model"><code class="docutils literal notranslate"><span class="pre">LlmAgent.canonical_model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LlmAgent.canonical_tools"><code class="docutils literal notranslate"><span class="pre">LlmAgent.canonical_tools</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.agents.LoopAgent"><code class="docutils literal notranslate"><span class="pre">LoopAgent</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.agents.LoopAgent.max_iterations"><code class="docutils literal notranslate"><span class="pre">LoopAgent.max_iterations</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.agents.ParallelAgent"><code class="docutils literal notranslate"><span class="pre">ParallelAgent</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.agents.SequentialAgent"><code class="docutils literal notranslate"><span class="pre">SequentialAgent</span></code></a></li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.artifacts">google.adk.artifacts module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.artifacts.BaseArtifactService"><code class="docutils literal notranslate"><span class="pre">BaseArtifactService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.BaseArtifactService.delete_artifact"><code class="docutils literal notranslate"><span class="pre">BaseArtifactService.delete_artifact()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.BaseArtifactService.list_artifact_keys"><code class="docutils literal notranslate"><span class="pre">BaseArtifactService.list_artifact_keys()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.BaseArtifactService.list_versions"><code class="docutils literal notranslate"><span class="pre">BaseArtifactService.list_versions()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.BaseArtifactService.load_artifact"><code class="docutils literal notranslate"><span class="pre">BaseArtifactService.load_artifact()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.BaseArtifactService.save_artifact"><code class="docutils literal notranslate"><span class="pre">BaseArtifactService.save_artifact()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.artifacts.GcsArtifactService"><code class="docutils literal notranslate"><span class="pre">GcsArtifactService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.GcsArtifactService.delete_artifact"><code class="docutils literal notranslate"><span class="pre">GcsArtifactService.delete_artifact()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.GcsArtifactService.list_artifact_keys"><code class="docutils literal notranslate"><span class="pre">GcsArtifactService.list_artifact_keys()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.GcsArtifactService.list_versions"><code class="docutils literal notranslate"><span class="pre">GcsArtifactService.list_versions()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.GcsArtifactService.load_artifact"><code class="docutils literal notranslate"><span class="pre">GcsArtifactService.load_artifact()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.GcsArtifactService.save_artifact"><code class="docutils literal notranslate"><span class="pre">GcsArtifactService.save_artifact()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.artifacts.InMemoryArtifactService"><code class="docutils literal notranslate"><span class="pre">InMemoryArtifactService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.artifacts"><code class="docutils literal notranslate"><span class="pre">InMemoryArtifactService.artifacts</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.delete_artifact"><code class="docutils literal notranslate"><span class="pre">InMemoryArtifactService.delete_artifact()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.list_artifact_keys"><code class="docutils literal notranslate"><span class="pre">InMemoryArtifactService.list_artifact_keys()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.list_versions"><code class="docutils literal notranslate"><span class="pre">InMemoryArtifactService.list_versions()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.load_artifact"><code class="docutils literal notranslate"><span class="pre">InMemoryArtifactService.load_artifact()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.artifacts.InMemoryArtifactService.save_artifact"><code class="docutils literal notranslate"><span class="pre">InMemoryArtifactService.save_artifact()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.code_executors">google.adk.code_executors module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.code_executors.BaseCodeExecutor"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.optimize_data_file"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.optimize_data_file</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.stateful"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.stateful</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.error_retry_attempts"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.error_retry_attempts</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.code_block_delimiters"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.code_block_delimiters</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.execution_result_delimiters"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.execution_result_delimiters</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id0"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.code_block_delimiters</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id9"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.error_retry_attempts</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id10"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.execution_result_delimiters</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id11"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.optimize_data_file</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id12"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.stateful</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.BaseCodeExecutor.execute_code"><code class="docutils literal notranslate"><span class="pre">BaseCodeExecutor.execute_code()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.add_input_files"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.add_input_files()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.add_processed_file_names"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.add_processed_file_names()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.clear_input_files"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.clear_input_files()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_error_count"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.get_error_count()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_execution_id"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.get_execution_id()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_input_files"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.get_input_files()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_processed_file_names"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.get_processed_file_names()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.get_state_delta"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.get_state_delta()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.increment_error_count"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.increment_error_count()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.reset_error_count"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.reset_error_count()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.set_execution_id"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.set_execution_id()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.CodeExecutorContext.update_code_execution_result"><code class="docutils literal notranslate"><span class="pre">CodeExecutorContext.update_code_execution_result()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.base_url"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.base_url</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.image"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.image</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.docker_path"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.docker_path</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id13"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.base_url</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id14"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.docker_path</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id15"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.image</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.optimize_data_file"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.optimize_data_file</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.stateful"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.stateful</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.execute_code"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.execute_code()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.ContainerCodeExecutor.model_post_init"><code class="docutils literal notranslate"><span class="pre">ContainerCodeExecutor.model_post_init()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.code_executors.UnsafeLocalCodeExecutor"><code class="docutils literal notranslate"><span class="pre">UnsafeLocalCodeExecutor</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.UnsafeLocalCodeExecutor.optimize_data_file"><code class="docutils literal notranslate"><span class="pre">UnsafeLocalCodeExecutor.optimize_data_file</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.UnsafeLocalCodeExecutor.stateful"><code class="docutils literal notranslate"><span class="pre">UnsafeLocalCodeExecutor.stateful</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.UnsafeLocalCodeExecutor.execute_code"><code class="docutils literal notranslate"><span class="pre">UnsafeLocalCodeExecutor.execute_code()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.code_executors.VertexAiCodeExecutor"><code class="docutils literal notranslate"><span class="pre">VertexAiCodeExecutor</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.VertexAiCodeExecutor.resource_name"><code class="docutils literal notranslate"><span class="pre">VertexAiCodeExecutor.resource_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id16"><code class="docutils literal notranslate"><span class="pre">VertexAiCodeExecutor.resource_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.VertexAiCodeExecutor.execute_code"><code class="docutils literal notranslate"><span class="pre">VertexAiCodeExecutor.execute_code()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.code_executors.VertexAiCodeExecutor.model_post_init"><code class="docutils literal notranslate"><span class="pre">VertexAiCodeExecutor.model_post_init()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.evaluation">google.adk.evaluation module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.evaluation.AgentEvaluator"><code class="docutils literal notranslate"><span class="pre">AgentEvaluator</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.evaluation.AgentEvaluator.evaluate"><code class="docutils literal notranslate"><span class="pre">AgentEvaluator.evaluate()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.evaluation.AgentEvaluator.find_config_for_test_file"><code class="docutils literal notranslate"><span class="pre">AgentEvaluator.find_config_for_test_file()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.events">google.adk.events module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.events.Event"><code class="docutils literal notranslate"><span class="pre">Event</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.invocation_id"><code class="docutils literal notranslate"><span class="pre">Event.invocation_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.author"><code class="docutils literal notranslate"><span class="pre">Event.author</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.actions"><code class="docutils literal notranslate"><span class="pre">Event.actions</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.long_running_tool_ids"><code class="docutils literal notranslate"><span class="pre">Event.long_running_tool_ids</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.branch"><code class="docutils literal notranslate"><span class="pre">Event.branch</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.id"><code class="docutils literal notranslate"><span class="pre">Event.id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.timestamp"><code class="docutils literal notranslate"><span class="pre">Event.timestamp</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.is_final_response"><code class="docutils literal notranslate"><span class="pre">Event.is_final_response</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.get_function_calls"><code class="docutils literal notranslate"><span class="pre">Event.get_function_calls</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id17"><code class="docutils literal notranslate"><span class="pre">Event.actions</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id18"><code class="docutils literal notranslate"><span class="pre">Event.author</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id19"><code class="docutils literal notranslate"><span class="pre">Event.branch</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id20"><code class="docutils literal notranslate"><span class="pre">Event.id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id21"><code class="docutils literal notranslate"><span class="pre">Event.invocation_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id22"><code class="docutils literal notranslate"><span class="pre">Event.long_running_tool_ids</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id23"><code class="docutils literal notranslate"><span class="pre">Event.timestamp</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.new_id"><code class="docutils literal notranslate"><span class="pre">Event.new_id()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id24"><code class="docutils literal notranslate"><span class="pre">Event.get_function_calls()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.get_function_responses"><code class="docutils literal notranslate"><span class="pre">Event.get_function_responses()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.has_trailing_code_execution_result"><code class="docutils literal notranslate"><span class="pre">Event.has_trailing_code_execution_result()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id25"><code class="docutils literal notranslate"><span class="pre">Event.is_final_response()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.Event.model_post_init"><code class="docutils literal notranslate"><span class="pre">Event.model_post_init()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.events.EventActions"><code class="docutils literal notranslate"><span class="pre">EventActions</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.EventActions.artifact_delta"><code class="docutils literal notranslate"><span class="pre">EventActions.artifact_delta</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.EventActions.escalate"><code class="docutils literal notranslate"><span class="pre">EventActions.escalate</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.EventActions.requested_auth_configs"><code class="docutils literal notranslate"><span class="pre">EventActions.requested_auth_configs</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.EventActions.skip_summarization"><code class="docutils literal notranslate"><span class="pre">EventActions.skip_summarization</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.EventActions.state_delta"><code class="docutils literal notranslate"><span class="pre">EventActions.state_delta</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.events.EventActions.transfer_to_agent"><code class="docutils literal notranslate"><span class="pre">EventActions.transfer_to_agent</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.examples">google.adk.examples module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.examples.BaseExampleProvider"><code class="docutils literal notranslate"><span class="pre">BaseExampleProvider</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.examples.BaseExampleProvider.get_examples"><code class="docutils literal notranslate"><span class="pre">BaseExampleProvider.get_examples()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.examples.Example"><code class="docutils literal notranslate"><span class="pre">Example</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.examples.Example.input"><code class="docutils literal notranslate"><span class="pre">Example.input</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.examples.Example.output"><code class="docutils literal notranslate"><span class="pre">Example.output</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id26"><code class="docutils literal notranslate"><span class="pre">Example.input</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id27"><code class="docutils literal notranslate"><span class="pre">Example.output</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.examples.VertexAiExampleStore"><code class="docutils literal notranslate"><span class="pre">VertexAiExampleStore</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.examples.VertexAiExampleStore.get_examples"><code class="docutils literal notranslate"><span class="pre">VertexAiExampleStore.get_examples()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.memory">google.adk.memory module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.memory.BaseMemoryService"><code class="docutils literal notranslate"><span class="pre">BaseMemoryService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.memory.BaseMemoryService.add_session_to_memory"><code class="docutils literal notranslate"><span class="pre">BaseMemoryService.add_session_to_memory()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.memory.BaseMemoryService.search_memory"><code class="docutils literal notranslate"><span class="pre">BaseMemoryService.search_memory()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.memory.InMemoryMemoryService"><code class="docutils literal notranslate"><span class="pre">InMemoryMemoryService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.memory.InMemoryMemoryService.add_session_to_memory"><code class="docutils literal notranslate"><span class="pre">InMemoryMemoryService.add_session_to_memory()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.memory.InMemoryMemoryService.search_memory"><code class="docutils literal notranslate"><span class="pre">InMemoryMemoryService.search_memory()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.memory.InMemoryMemoryService.session_events"><code class="docutils literal notranslate"><span class="pre">InMemoryMemoryService.session_events</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.memory.VertexAiRagMemoryService"><code class="docutils literal notranslate"><span class="pre">VertexAiRagMemoryService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.memory.VertexAiRagMemoryService.add_session_to_memory"><code class="docutils literal notranslate"><span class="pre">VertexAiRagMemoryService.add_session_to_memory()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.memory.VertexAiRagMemoryService.search_memory"><code class="docutils literal notranslate"><span class="pre">VertexAiRagMemoryService.search_memory()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.models">google.adk.models module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.models.BaseLlm"><code class="docutils literal notranslate"><span class="pre">BaseLlm</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.BaseLlm.model"><code class="docutils literal notranslate"><span class="pre">BaseLlm.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id28"><code class="docutils literal notranslate"><span class="pre">BaseLlm.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.BaseLlm.supported_models"><code class="docutils literal notranslate"><span class="pre">BaseLlm.supported_models()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.BaseLlm.connect"><code class="docutils literal notranslate"><span class="pre">BaseLlm.connect()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.BaseLlm.generate_content_async"><code class="docutils literal notranslate"><span class="pre">BaseLlm.generate_content_async()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.models.Gemini"><code class="docutils literal notranslate"><span class="pre">Gemini</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.Gemini.model"><code class="docutils literal notranslate"><span class="pre">Gemini.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id29"><code class="docutils literal notranslate"><span class="pre">Gemini.model</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.Gemini.supported_models"><code class="docutils literal notranslate"><span class="pre">Gemini.supported_models()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.Gemini.connect"><code class="docutils literal notranslate"><span class="pre">Gemini.connect()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.Gemini.generate_content_async"><code class="docutils literal notranslate"><span class="pre">Gemini.generate_content_async()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.Gemini.api_client"><code class="docutils literal notranslate"><span class="pre">Gemini.api_client</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.models.LLMRegistry"><code class="docutils literal notranslate"><span class="pre">LLMRegistry</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.LLMRegistry.new_llm"><code class="docutils literal notranslate"><span class="pre">LLMRegistry.new_llm()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.LLMRegistry.register"><code class="docutils literal notranslate"><span class="pre">LLMRegistry.register()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.models.LLMRegistry.resolve"><code class="docutils literal notranslate"><span class="pre">LLMRegistry.resolve()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.planners">google.adk.planners module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.planners.BasePlanner"><code class="docutils literal notranslate"><span class="pre">BasePlanner</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.BasePlanner.build_planning_instruction"><code class="docutils literal notranslate"><span class="pre">BasePlanner.build_planning_instruction()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.BasePlanner.process_planning_response"><code class="docutils literal notranslate"><span class="pre">BasePlanner.process_planning_response()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.planners.BuiltInPlanner"><code class="docutils literal notranslate"><span class="pre">BuiltInPlanner</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.BuiltInPlanner.thinking_config"><code class="docutils literal notranslate"><span class="pre">BuiltInPlanner.thinking_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.BuiltInPlanner.apply_thinking_config"><code class="docutils literal notranslate"><span class="pre">BuiltInPlanner.apply_thinking_config()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.BuiltInPlanner.build_planning_instruction"><code class="docutils literal notranslate"><span class="pre">BuiltInPlanner.build_planning_instruction()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.BuiltInPlanner.process_planning_response"><code class="docutils literal notranslate"><span class="pre">BuiltInPlanner.process_planning_response()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id30"><code class="docutils literal notranslate"><span class="pre">BuiltInPlanner.thinking_config</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.planners.PlanReActPlanner"><code class="docutils literal notranslate"><span class="pre">PlanReActPlanner</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.PlanReActPlanner.build_planning_instruction"><code class="docutils literal notranslate"><span class="pre">PlanReActPlanner.build_planning_instruction()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.planners.PlanReActPlanner.process_planning_response"><code class="docutils literal notranslate"><span class="pre">PlanReActPlanner.process_planning_response()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.runners">google.adk.runners module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.runners.InMemoryRunner"><code class="docutils literal notranslate"><span class="pre">InMemoryRunner</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.InMemoryRunner.agent"><code class="docutils literal notranslate"><span class="pre">InMemoryRunner.agent</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.InMemoryRunner.app_name"><code class="docutils literal notranslate"><span class="pre">InMemoryRunner.app_name</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner"><code class="docutils literal notranslate"><span class="pre">Runner</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.app_name"><code class="docutils literal notranslate"><span class="pre">Runner.app_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.agent"><code class="docutils literal notranslate"><span class="pre">Runner.agent</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.artifact_service"><code class="docutils literal notranslate"><span class="pre">Runner.artifact_service</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.session_service"><code class="docutils literal notranslate"><span class="pre">Runner.session_service</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.memory_service"><code class="docutils literal notranslate"><span class="pre">Runner.memory_service</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id31"><code class="docutils literal notranslate"><span class="pre">Runner.agent</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id32"><code class="docutils literal notranslate"><span class="pre">Runner.app_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id33"><code class="docutils literal notranslate"><span class="pre">Runner.artifact_service</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.close_session"><code class="docutils literal notranslate"><span class="pre">Runner.close_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id34"><code class="docutils literal notranslate"><span class="pre">Runner.memory_service</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.run"><code class="docutils literal notranslate"><span class="pre">Runner.run()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.run_async"><code class="docutils literal notranslate"><span class="pre">Runner.run_async()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.runners.Runner.run_live"><code class="docutils literal notranslate"><span class="pre">Runner.run_live()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id35"><code class="docutils literal notranslate"><span class="pre">Runner.session_service</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.sessions">google.adk.sessions module</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService"><code class="docutils literal notranslate"><span class="pre">BaseSessionService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService.append_event"><code class="docutils literal notranslate"><span class="pre">BaseSessionService.append_event()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService.close_session"><code class="docutils literal notranslate"><span class="pre">BaseSessionService.close_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService.create_session"><code class="docutils literal notranslate"><span class="pre">BaseSessionService.create_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService.delete_session"><code class="docutils literal notranslate"><span class="pre">BaseSessionService.delete_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService.get_session"><code class="docutils literal notranslate"><span class="pre">BaseSessionService.get_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService.list_events"><code class="docutils literal notranslate"><span class="pre">BaseSessionService.list_events()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.BaseSessionService.list_sessions"><code class="docutils literal notranslate"><span class="pre">BaseSessionService.list_sessions()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.sessions.DatabaseSessionService"><code class="docutils literal notranslate"><span class="pre">DatabaseSessionService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.DatabaseSessionService.append_event"><code class="docutils literal notranslate"><span class="pre">DatabaseSessionService.append_event()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.DatabaseSessionService.create_session"><code class="docutils literal notranslate"><span class="pre">DatabaseSessionService.create_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.DatabaseSessionService.delete_session"><code class="docutils literal notranslate"><span class="pre">DatabaseSessionService.delete_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.DatabaseSessionService.get_session"><code class="docutils literal notranslate"><span class="pre">DatabaseSessionService.get_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.DatabaseSessionService.list_events"><code class="docutils literal notranslate"><span class="pre">DatabaseSessionService.list_events()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.DatabaseSessionService.list_sessions"><code class="docutils literal notranslate"><span class="pre">DatabaseSessionService.list_sessions()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.sessions.InMemorySessionService"><code class="docutils literal notranslate"><span class="pre">InMemorySessionService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.InMemorySessionService.append_event"><code class="docutils literal notranslate"><span class="pre">InMemorySessionService.append_event()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.InMemorySessionService.create_session"><code class="docutils literal notranslate"><span class="pre">InMemorySessionService.create_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.InMemorySessionService.delete_session"><code class="docutils literal notranslate"><span class="pre">InMemorySessionService.delete_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.InMemorySessionService.get_session"><code class="docutils literal notranslate"><span class="pre">InMemorySessionService.get_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.InMemorySessionService.list_events"><code class="docutils literal notranslate"><span class="pre">InMemorySessionService.list_events()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.InMemorySessionService.list_sessions"><code class="docutils literal notranslate"><span class="pre">InMemorySessionService.list_sessions()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.sessions.Session"><code class="docutils literal notranslate"><span class="pre">Session</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.Session.id"><code class="docutils literal notranslate"><span class="pre">Session.id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.Session.app_name"><code class="docutils literal notranslate"><span class="pre">Session.app_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.Session.user_id"><code class="docutils literal notranslate"><span class="pre">Session.user_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.Session.state"><code class="docutils literal notranslate"><span class="pre">Session.state</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.Session.events"><code class="docutils literal notranslate"><span class="pre">Session.events</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.Session.last_update_time"><code class="docutils literal notranslate"><span class="pre">Session.last_update_time</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id36"><code class="docutils literal notranslate"><span class="pre">Session.app_name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id37"><code class="docutils literal notranslate"><span class="pre">Session.events</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id38"><code class="docutils literal notranslate"><span class="pre">Session.id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id39"><code class="docutils literal notranslate"><span class="pre">Session.last_update_time</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id40"><code class="docutils literal notranslate"><span class="pre">Session.state</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#id41"><code class="docutils literal notranslate"><span class="pre">Session.user_id</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.sessions.State"><code class="docutils literal notranslate"><span class="pre">State</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.State.APP_PREFIX"><code class="docutils literal notranslate"><span class="pre">State.APP_PREFIX</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.State.TEMP_PREFIX"><code class="docutils literal notranslate"><span class="pre">State.TEMP_PREFIX</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.State.USER_PREFIX"><code class="docutils literal notranslate"><span class="pre">State.USER_PREFIX</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.State.get"><code class="docutils literal notranslate"><span class="pre">State.get()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.State.has_delta"><code class="docutils literal notranslate"><span class="pre">State.has_delta()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.State.to_dict"><code class="docutils literal notranslate"><span class="pre">State.to_dict()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.State.update"><code class="docutils literal notranslate"><span class="pre">State.update()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.sessions.VertexAiSessionService"><code class="docutils literal notranslate"><span class="pre">VertexAiSessionService</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.VertexAiSessionService.append_event"><code class="docutils literal notranslate"><span class="pre">VertexAiSessionService.append_event()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.VertexAiSessionService.create_session"><code class="docutils literal notranslate"><span class="pre">VertexAiSessionService.create_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.VertexAiSessionService.delete_session"><code class="docutils literal notranslate"><span class="pre">VertexAiSessionService.delete_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.VertexAiSessionService.get_session"><code class="docutils literal notranslate"><span class="pre">VertexAiSessionService.get_session()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.VertexAiSessionService.list_events"><code class="docutils literal notranslate"><span class="pre">VertexAiSessionService.list_events()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.sessions.VertexAiSessionService.list_sessions"><code class="docutils literal notranslate"><span class="pre">VertexAiSessionService.list_sessions()</span></code></a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="google-adk.html#module-google.adk.tools">google.adk.tools package</a><ul>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.APIHubToolset"><code class="docutils literal notranslate"><span class="pre">APIHubToolset</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.APIHubToolset.get_tool"><code class="docutils literal notranslate"><span class="pre">APIHubToolset.get_tool()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.APIHubToolset.get_tools"><code class="docutils literal notranslate"><span class="pre">APIHubToolset.get_tools()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.AuthToolArguments"><code class="docutils literal notranslate"><span class="pre">AuthToolArguments</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.AuthToolArguments.auth_config"><code class="docutils literal notranslate"><span class="pre">AuthToolArguments.auth_config</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.AuthToolArguments.function_call_id"><code class="docutils literal notranslate"><span class="pre">AuthToolArguments.function_call_id</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.BaseTool"><code class="docutils literal notranslate"><span class="pre">BaseTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.BaseTool.description"><code class="docutils literal notranslate"><span class="pre">BaseTool.description</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.BaseTool.is_long_running"><code class="docutils literal notranslate"><span class="pre">BaseTool.is_long_running</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.BaseTool.name"><code class="docutils literal notranslate"><span class="pre">BaseTool.name</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.BaseTool.process_llm_request"><code class="docutils literal notranslate"><span class="pre">BaseTool.process_llm_request()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.BaseTool.run_async"><code class="docutils literal notranslate"><span class="pre">BaseTool.run_async()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.ExampleTool"><code class="docutils literal notranslate"><span class="pre">ExampleTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ExampleTool.examples"><code class="docutils literal notranslate"><span class="pre">ExampleTool.examples</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ExampleTool.process_llm_request"><code class="docutils literal notranslate"><span class="pre">ExampleTool.process_llm_request()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.FunctionTool"><code class="docutils literal notranslate"><span class="pre">FunctionTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.FunctionTool.func"><code class="docutils literal notranslate"><span class="pre">FunctionTool.func</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.FunctionTool.run_async"><code class="docutils literal notranslate"><span class="pre">FunctionTool.run_async()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.LongRunningFunctionTool"><code class="docutils literal notranslate"><span class="pre">LongRunningFunctionTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.LongRunningFunctionTool.is_long_running"><code class="docutils literal notranslate"><span class="pre">LongRunningFunctionTool.is_long_running</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext"><code class="docutils literal notranslate"><span class="pre">ToolContext</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.invocation_context"><code class="docutils literal notranslate"><span class="pre">ToolContext.invocation_context</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.function_call_id"><code class="docutils literal notranslate"><span class="pre">ToolContext.function_call_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.event_actions"><code class="docutils literal notranslate"><span class="pre">ToolContext.event_actions</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.actions"><code class="docutils literal notranslate"><span class="pre">ToolContext.actions</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.get_auth_response"><code class="docutils literal notranslate"><span class="pre">ToolContext.get_auth_response()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.list_artifacts"><code class="docutils literal notranslate"><span class="pre">ToolContext.list_artifacts()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.request_credential"><code class="docutils literal notranslate"><span class="pre">ToolContext.request_credential()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.ToolContext.search_memory"><code class="docutils literal notranslate"><span class="pre">ToolContext.search_memory()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.VertexAiSearchTool"><code class="docutils literal notranslate"><span class="pre">VertexAiSearchTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.VertexAiSearchTool.data_store_id"><code class="docutils literal notranslate"><span class="pre">VertexAiSearchTool.data_store_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.VertexAiSearchTool.search_engine_id"><code class="docutils literal notranslate"><span class="pre">VertexAiSearchTool.search_engine_id</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.VertexAiSearchTool.process_llm_request"><code class="docutils literal notranslate"><span class="pre">VertexAiSearchTool.process_llm_request()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.exit_loop"><code class="docutils literal notranslate"><span class="pre">exit_loop()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.transfer_to_agent"><code class="docutils literal notranslate"><span class="pre">transfer_to_agent()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.application_integration_tool.ApplicationIntegrationToolset"><code class="docutils literal notranslate"><span class="pre">ApplicationIntegrationToolset</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.application_integration_tool.ApplicationIntegrationToolset.get_tools"><code class="docutils literal notranslate"><span class="pre">ApplicationIntegrationToolset.get_tools()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool"><code class="docutils literal notranslate"><span class="pre">IntegrationConnectorTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool.EXCLUDE_FIELDS"><code class="docutils literal notranslate"><span class="pre">IntegrationConnectorTool.EXCLUDE_FIELDS</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool.OPTIONAL_FIELDS"><code class="docutils literal notranslate"><span class="pre">IntegrationConnectorTool.OPTIONAL_FIELDS</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.application_integration_tool.IntegrationConnectorTool.run_async"><code class="docutils literal notranslate"><span class="pre">IntegrationConnectorTool.run_async()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPTool"><code class="docutils literal notranslate"><span class="pre">MCPTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPTool.run_async"><code class="docutils literal notranslate"><span class="pre">MCPTool.run_async()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset"><code class="docutils literal notranslate"><span class="pre">MCPToolset</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.connection_params"><code class="docutils literal notranslate"><span class="pre">MCPToolset.connection_params</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.exit_stack"><code class="docutils literal notranslate"><span class="pre">MCPToolset.exit_stack</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.session"><code class="docutils literal notranslate"><span class="pre">MCPToolset.session</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.from_server"><code class="docutils literal notranslate"><span class="pre">MCPToolset.from_server()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.MCPToolset.load_tools"><code class="docutils literal notranslate"><span class="pre">MCPToolset.load_tools()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.adk_to_mcp_tool_type"><code class="docutils literal notranslate"><span class="pre">adk_to_mcp_tool_type()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.mcp_tool.gemini_to_json_schema"><code class="docutils literal notranslate"><span class="pre">gemini_to_json_schema()</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.OpenAPIToolset"><code class="docutils literal notranslate"><span class="pre">OpenAPIToolset</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.OpenAPIToolset.get_tool"><code class="docutils literal notranslate"><span class="pre">OpenAPIToolset.get_tool()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.OpenAPIToolset.get_tools"><code class="docutils literal notranslate"><span class="pre">OpenAPIToolset.get_tools()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool"><code class="docutils literal notranslate"><span class="pre">RestApiTool</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.call"><code class="docutils literal notranslate"><span class="pre">RestApiTool.call()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.configure_auth_credential"><code class="docutils literal notranslate"><span class="pre">RestApiTool.configure_auth_credential()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.configure_auth_scheme"><code class="docutils literal notranslate"><span class="pre">RestApiTool.configure_auth_scheme()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.from_parsed_operation"><code class="docutils literal notranslate"><span class="pre">RestApiTool.from_parsed_operation()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.from_parsed_operation_str"><code class="docutils literal notranslate"><span class="pre">RestApiTool.from_parsed_operation_str()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.openapi_tool.RestApiTool.run_async"><code class="docutils literal notranslate"><span class="pre">RestApiTool.run_async()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.retrieval.BaseRetrievalTool"><code class="docutils literal notranslate"><span class="pre">BaseRetrievalTool</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.retrieval.FilesRetrieval"><code class="docutils literal notranslate"><span class="pre">FilesRetrieval</span></code></a></li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.retrieval.LlamaIndexRetrieval"><code class="docutils literal notranslate"><span class="pre">LlamaIndexRetrieval</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.retrieval.LlamaIndexRetrieval.run_async"><code class="docutils literal notranslate"><span class="pre">LlamaIndexRetrieval.run_async()</span></code></a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="google-adk.html#google.adk.tools.retrieval.VertexAiRagRetrieval"><code class="docutils literal notranslate"><span class="pre">VertexAiRagRetrieval</span></code></a><ul>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.retrieval.VertexAiRagRetrieval.process_llm_request"><code class="docutils literal notranslate"><span class="pre">VertexAiRagRetrieval.process_llm_request()</span></code></a></li>
<li class="toctree-l3"><a class="reference internal" href="google-adk.html#google.adk.tools.retrieval.VertexAiRagRetrieval.run_async"><code class="docutils literal notranslate"><span class="pre">VertexAiRagRetrieval.run_async()</span></code></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</section>

        </article>
      </div>
      <footer>
        
        <div class="related-pages">
          <a class="next-page" href="google-adk.html">
              <div class="page-info">
                <div class="context">
                  <span>Next</span>
                </div>
                <div class="title">Submodules</div>
              </div>
              <svg class="furo-related-icon"><use href="#svg-arrow-right"></use></svg>
            </a>
          
        </div>
        <div class="bottom-of-page">
          <div class="left-details">
            <div class="copyright">
                Copyright &#169; 2025, Google
            </div>
            Made with <a href="https://www.sphinx-doc.org/">Sphinx</a> and <a class="muted-link" href="https://pradyunsg.me">@pradyunsg</a>'s
            
            <a href="https://github.com/pradyunsg/furo">Furo</a>
            
          </div>
          <div class="right-details">
            
          </div>
        </div>
        
      </footer>
    </div>
    <aside class="toc-drawer no-toc">
      
      
      
    </aside>
  </div>
</div><script src="_static/documentation_options.js?v=5929fcd5"></script>
    <script src="_static/doctools.js?v=9bcbadda"></script>
    <script src="_static/sphinx_highlight.js?v=dc90522c"></script>
    <script src="_static/scripts/furo.js?v=5fa4622c"></script>
    </body>
</html>