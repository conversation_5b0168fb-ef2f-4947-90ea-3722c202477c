Search.setIndex({"alltitles":{")":[[0,"id68"],[0,"id87"]],"Submodules":[[0,null]],"])":[[0,"id44"],[0,"id47"],[0,"id52"],[0,"id57"]],"google":[[1,null]],"google.adk.agents module":[[0,"module-google.adk.agents"]],"google.adk.artifacts module":[[0,"module-google.adk.artifacts"]],"google.adk.code_executors module":[[0,"module-google.adk.code_executors"]],"google.adk.evaluation module":[[0,"module-google.adk.evaluation"]],"google.adk.events module":[[0,"module-google.adk.events"]],"google.adk.examples module":[[0,"module-google.adk.examples"]],"google.adk.memory module":[[0,"module-google.adk.memory"]],"google.adk.models module":[[0,"module-google.adk.models"]],"google.adk.planners module":[[0,"module-google.adk.planners"]],"google.adk.runners module":[[0,"module-google.adk.runners"]],"google.adk.sessions module":[[0,"module-google.adk.sessions"]],"google.adk.tools package":[[0,"module-google.adk.tools"]]},"docnames":["google-adk","index"],"envversion":{"sphinx":65,"sphinx.domains.c":3,"sphinx.domains.changeset":1,"sphinx.domains.citation":1,"sphinx.domains.cpp":9,"sphinx.domains.index":1,"sphinx.domains.javascript":3,"sphinx.domains.math":2,"sphinx.domains.python":4,"sphinx.domains.rst":2,"sphinx.domains.std":2},"filenames":["google-adk.rst","index.rst"],"indexentries":{"actions (google.adk.events.event attribute)":[[0,"google.adk.events.Event.actions",false],[0,"id17",false]],"actions (google.adk.tools.toolcontext property)":[[0,"google.adk.tools.ToolContext.actions",false]],"add_input_files() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.add_input_files",false]],"add_processed_file_names() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.add_processed_file_names",false]],"add_session_to_memory() (google.adk.memory.basememoryservice method)":[[0,"google.adk.memory.BaseMemoryService.add_session_to_memory",false]],"add_session_to_memory() (google.adk.memory.inmemorymemoryservice method)":[[0,"google.adk.memory.InMemoryMemoryService.add_session_to_memory",false]],"add_session_to_memory() (google.adk.memory.vertexairagmemoryservice method)":[[0,"google.adk.memory.VertexAiRagMemoryService.add_session_to_memory",false]],"adk_to_mcp_tool_type() (in module google.adk.tools.mcp_tool)":[[0,"google.adk.tools.mcp_tool.adk_to_mcp_tool_type",false]],"after_agent_callback (google.adk.agents.baseagent attribute)":[[0,"google.adk.agents.BaseAgent.after_agent_callback",false]],"after_model_callback (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.after_model_callback",false]],"after_tool_callback (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.after_tool_callback",false]],"agent (google.adk.runners.inmemoryrunner attribute)":[[0,"google.adk.runners.InMemoryRunner.agent",false]],"agent (google.adk.runners.runner attribute)":[[0,"google.adk.runners.Runner.agent",false],[0,"id31",false]],"agent (in module google.adk.agents)":[[0,"google.adk.agents.Agent",false]],"agentevaluator (class in google.adk.evaluation)":[[0,"google.adk.evaluation.AgentEvaluator",false]],"api_client (google.adk.models.gemini property)":[[0,"google.adk.models.Gemini.api_client",false]],"apihubtoolset (class in google.adk.tools)":[[0,"google.adk.tools.APIHubToolset",false]],"app_name (google.adk.runners.inmemoryrunner attribute)":[[0,"google.adk.runners.InMemoryRunner.app_name",false]],"app_name (google.adk.runners.runner attribute)":[[0,"google.adk.runners.Runner.app_name",false],[0,"id32",false]],"app_name (google.adk.sessions.session attribute)":[[0,"google.adk.sessions.Session.app_name",false],[0,"id36",false]],"app_prefix (google.adk.sessions.state attribute)":[[0,"google.adk.sessions.State.APP_PREFIX",false]],"append_event() (google.adk.sessions.basesessionservice method)":[[0,"google.adk.sessions.BaseSessionService.append_event",false]],"append_event() (google.adk.sessions.databasesessionservice method)":[[0,"google.adk.sessions.DatabaseSessionService.append_event",false]],"append_event() (google.adk.sessions.inmemorysessionservice method)":[[0,"google.adk.sessions.InMemorySessionService.append_event",false]],"append_event() (google.adk.sessions.vertexaisessionservice method)":[[0,"google.adk.sessions.VertexAiSessionService.append_event",false]],"applicationintegrationtoolset (class in google.adk.tools.application_integration_tool)":[[0,"google.adk.tools.application_integration_tool.ApplicationIntegrationToolset",false]],"apply_thinking_config() (google.adk.planners.builtinplanner method)":[[0,"google.adk.planners.BuiltInPlanner.apply_thinking_config",false]],"artifact_delta (google.adk.events.eventactions attribute)":[[0,"google.adk.events.EventActions.artifact_delta",false]],"artifact_service (google.adk.runners.runner attribute)":[[0,"google.adk.runners.Runner.artifact_service",false],[0,"id33",false]],"artifacts (google.adk.artifacts.inmemoryartifactservice attribute)":[[0,"google.adk.artifacts.InMemoryArtifactService.artifacts",false]],"auth_config (google.adk.tools.authtoolarguments attribute)":[[0,"google.adk.tools.AuthToolArguments.auth_config",false]],"author (google.adk.events.event attribute)":[[0,"google.adk.events.Event.author",false],[0,"id18",false]],"base_url (google.adk.code_executors.containercodeexecutor attribute)":[[0,"google.adk.code_executors.ContainerCodeExecutor.base_url",false],[0,"id13",false]],"baseartifactservice (class in google.adk.artifacts)":[[0,"google.adk.artifacts.BaseArtifactService",false]],"baseexampleprovider (class in google.adk.examples)":[[0,"google.adk.examples.BaseExampleProvider",false]],"basememoryservice (class in google.adk.memory)":[[0,"google.adk.memory.BaseMemoryService",false]],"baseplanner (class in google.adk.planners)":[[0,"google.adk.planners.BasePlanner",false]],"baseretrievaltool (class in google.adk.tools.retrieval)":[[0,"google.adk.tools.retrieval.BaseRetrievalTool",false]],"basesessionservice (class in google.adk.sessions)":[[0,"google.adk.sessions.BaseSessionService",false]],"basetool (class in google.adk.tools)":[[0,"google.adk.tools.BaseTool",false]],"before_agent_callback (google.adk.agents.baseagent attribute)":[[0,"google.adk.agents.BaseAgent.before_agent_callback",false]],"before_model_callback (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.before_model_callback",false]],"before_tool_callback (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.before_tool_callback",false]],"branch (google.adk.events.event attribute)":[[0,"google.adk.events.Event.branch",false],[0,"id19",false]],"build_planning_instruction() (google.adk.planners.baseplanner method)":[[0,"google.adk.planners.BasePlanner.build_planning_instruction",false]],"build_planning_instruction() (google.adk.planners.builtinplanner method)":[[0,"google.adk.planners.BuiltInPlanner.build_planning_instruction",false]],"build_planning_instruction() (google.adk.planners.planreactplanner method)":[[0,"google.adk.planners.PlanReActPlanner.build_planning_instruction",false]],"builtinplanner (class in google.adk.planners)":[[0,"google.adk.planners.BuiltInPlanner",false]],"call() (google.adk.tools.openapi_tool.restapitool method)":[[0,"google.adk.tools.openapi_tool.RestApiTool.call",false]],"canonical_after_model_callbacks (google.adk.agents.llmagent property)":[[0,"google.adk.agents.LlmAgent.canonical_after_model_callbacks",false]],"canonical_before_model_callbacks (google.adk.agents.llmagent property)":[[0,"google.adk.agents.LlmAgent.canonical_before_model_callbacks",false]],"canonical_global_instruction() (google.adk.agents.llmagent method)":[[0,"google.adk.agents.LlmAgent.canonical_global_instruction",false]],"canonical_instruction() (google.adk.agents.llmagent method)":[[0,"google.adk.agents.LlmAgent.canonical_instruction",false]],"canonical_model (google.adk.agents.llmagent property)":[[0,"google.adk.agents.LlmAgent.canonical_model",false]],"canonical_tools (google.adk.agents.llmagent property)":[[0,"google.adk.agents.LlmAgent.canonical_tools",false]],"clear_input_files() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.clear_input_files",false]],"close_session() (google.adk.runners.runner method)":[[0,"google.adk.runners.Runner.close_session",false]],"close_session() (google.adk.sessions.basesessionservice method)":[[0,"google.adk.sessions.BaseSessionService.close_session",false]],"code_block_delimiters (google.adk.code_executors.basecodeexecutor attribute)":[[0,"google.adk.code_executors.BaseCodeExecutor.code_block_delimiters",false],[0,"id0",false]],"code_executor (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.code_executor",false]],"codeexecutorcontext (class in google.adk.code_executors)":[[0,"google.adk.code_executors.CodeExecutorContext",false]],"configure_auth_credential() (google.adk.tools.openapi_tool.restapitool method)":[[0,"google.adk.tools.openapi_tool.RestApiTool.configure_auth_credential",false]],"configure_auth_scheme() (google.adk.tools.openapi_tool.restapitool method)":[[0,"google.adk.tools.openapi_tool.RestApiTool.configure_auth_scheme",false]],"connect() (google.adk.models.basellm method)":[[0,"google.adk.models.BaseLlm.connect",false]],"connect() (google.adk.models.gemini method)":[[0,"google.adk.models.Gemini.connect",false]],"connection_params (google.adk.tools.mcp_tool.mcptoolset attribute)":[[0,"google.adk.tools.mcp_tool.MCPToolset.connection_params",false]],"create_session() (google.adk.sessions.basesessionservice method)":[[0,"google.adk.sessions.BaseSessionService.create_session",false]],"create_session() (google.adk.sessions.databasesessionservice method)":[[0,"google.adk.sessions.DatabaseSessionService.create_session",false]],"create_session() (google.adk.sessions.inmemorysessionservice method)":[[0,"google.adk.sessions.InMemorySessionService.create_session",false]],"create_session() (google.adk.sessions.vertexaisessionservice method)":[[0,"google.adk.sessions.VertexAiSessionService.create_session",false]],"data_store_id (google.adk.tools.vertexaisearchtool attribute)":[[0,"google.adk.tools.VertexAiSearchTool.data_store_id",false]],"databasesessionservice (class in google.adk.sessions)":[[0,"google.adk.sessions.DatabaseSessionService",false]],"delete_artifact() (google.adk.artifacts.baseartifactservice method)":[[0,"google.adk.artifacts.BaseArtifactService.delete_artifact",false]],"delete_artifact() (google.adk.artifacts.gcsartifactservice method)":[[0,"google.adk.artifacts.GcsArtifactService.delete_artifact",false]],"delete_artifact() (google.adk.artifacts.inmemoryartifactservice method)":[[0,"google.adk.artifacts.InMemoryArtifactService.delete_artifact",false]],"delete_session() (google.adk.sessions.basesessionservice method)":[[0,"google.adk.sessions.BaseSessionService.delete_session",false]],"delete_session() (google.adk.sessions.databasesessionservice method)":[[0,"google.adk.sessions.DatabaseSessionService.delete_session",false]],"delete_session() (google.adk.sessions.inmemorysessionservice method)":[[0,"google.adk.sessions.InMemorySessionService.delete_session",false]],"delete_session() (google.adk.sessions.vertexaisessionservice method)":[[0,"google.adk.sessions.VertexAiSessionService.delete_session",false]],"description (google.adk.agents.baseagent attribute)":[[0,"google.adk.agents.BaseAgent.description",false]],"description (google.adk.tools.basetool attribute)":[[0,"google.adk.tools.BaseTool.description",false]],"disallow_transfer_to_parent (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.disallow_transfer_to_parent",false]],"disallow_transfer_to_peers (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.disallow_transfer_to_peers",false]],"docker_path (google.adk.code_executors.containercodeexecutor attribute)":[[0,"google.adk.code_executors.ContainerCodeExecutor.docker_path",false],[0,"id14",false]],"error_retry_attempts (google.adk.code_executors.basecodeexecutor attribute)":[[0,"google.adk.code_executors.BaseCodeExecutor.error_retry_attempts",false],[0,"id9",false]],"escalate (google.adk.events.eventactions attribute)":[[0,"google.adk.events.EventActions.escalate",false]],"evaluate() (google.adk.evaluation.agentevaluator static method)":[[0,"google.adk.evaluation.AgentEvaluator.evaluate",false]],"event_actions (google.adk.tools.toolcontext attribute)":[[0,"google.adk.tools.ToolContext.event_actions",false]],"events (google.adk.sessions.session attribute)":[[0,"google.adk.sessions.Session.events",false],[0,"id37",false]],"examples (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.examples",false]],"examples (google.adk.tools.exampletool attribute)":[[0,"google.adk.tools.ExampleTool.examples",false]],"exampletool (class in google.adk.tools)":[[0,"google.adk.tools.ExampleTool",false]],"exclude_fields (google.adk.tools.application_integration_tool.integrationconnectortool attribute)":[[0,"google.adk.tools.application_integration_tool.IntegrationConnectorTool.EXCLUDE_FIELDS",false]],"execute_code() (google.adk.code_executors.basecodeexecutor method)":[[0,"google.adk.code_executors.BaseCodeExecutor.execute_code",false]],"execute_code() (google.adk.code_executors.containercodeexecutor method)":[[0,"google.adk.code_executors.ContainerCodeExecutor.execute_code",false]],"execute_code() (google.adk.code_executors.unsafelocalcodeexecutor method)":[[0,"google.adk.code_executors.UnsafeLocalCodeExecutor.execute_code",false]],"execute_code() (google.adk.code_executors.vertexaicodeexecutor method)":[[0,"google.adk.code_executors.VertexAiCodeExecutor.execute_code",false]],"execution_result_delimiters (google.adk.code_executors.basecodeexecutor attribute)":[[0,"google.adk.code_executors.BaseCodeExecutor.execution_result_delimiters",false],[0,"id10",false]],"exit_loop() (in module google.adk.tools)":[[0,"google.adk.tools.exit_loop",false]],"exit_stack (google.adk.tools.mcp_tool.mcptoolset attribute)":[[0,"google.adk.tools.mcp_tool.MCPToolset.exit_stack",false]],"filesretrieval (class in google.adk.tools.retrieval)":[[0,"google.adk.tools.retrieval.FilesRetrieval",false]],"find_agent() (google.adk.agents.baseagent method)":[[0,"google.adk.agents.BaseAgent.find_agent",false]],"find_config_for_test_file() (google.adk.evaluation.agentevaluator static method)":[[0,"google.adk.evaluation.AgentEvaluator.find_config_for_test_file",false]],"find_sub_agent() (google.adk.agents.baseagent method)":[[0,"google.adk.agents.BaseAgent.find_sub_agent",false]],"from_parsed_operation() (google.adk.tools.openapi_tool.restapitool class method)":[[0,"google.adk.tools.openapi_tool.RestApiTool.from_parsed_operation",false]],"from_parsed_operation_str() (google.adk.tools.openapi_tool.restapitool class method)":[[0,"google.adk.tools.openapi_tool.RestApiTool.from_parsed_operation_str",false]],"from_server() (google.adk.tools.mcp_tool.mcptoolset class method)":[[0,"google.adk.tools.mcp_tool.MCPToolset.from_server",false]],"func (google.adk.tools.functiontool attribute)":[[0,"google.adk.tools.FunctionTool.func",false]],"function_call_id (google.adk.tools.authtoolarguments attribute)":[[0,"google.adk.tools.AuthToolArguments.function_call_id",false]],"function_call_id (google.adk.tools.toolcontext attribute)":[[0,"google.adk.tools.ToolContext.function_call_id",false]],"functiontool (class in google.adk.tools)":[[0,"google.adk.tools.FunctionTool",false]],"gcsartifactservice (class in google.adk.artifacts)":[[0,"google.adk.artifacts.GcsArtifactService",false]],"gemini_to_json_schema() (in module google.adk.tools.mcp_tool)":[[0,"google.adk.tools.mcp_tool.gemini_to_json_schema",false]],"generate_content_async() (google.adk.models.basellm method)":[[0,"google.adk.models.BaseLlm.generate_content_async",false]],"generate_content_async() (google.adk.models.gemini method)":[[0,"google.adk.models.Gemini.generate_content_async",false]],"generate_content_config (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.generate_content_config",false]],"get() (google.adk.sessions.state method)":[[0,"google.adk.sessions.State.get",false]],"get_auth_response() (google.adk.tools.toolcontext method)":[[0,"google.adk.tools.ToolContext.get_auth_response",false]],"get_error_count() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.get_error_count",false]],"get_examples() (google.adk.examples.baseexampleprovider method)":[[0,"google.adk.examples.BaseExampleProvider.get_examples",false]],"get_examples() (google.adk.examples.vertexaiexamplestore method)":[[0,"google.adk.examples.VertexAiExampleStore.get_examples",false]],"get_execution_id() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.get_execution_id",false]],"get_function_calls (google.adk.events.event attribute)":[[0,"google.adk.events.Event.get_function_calls",false]],"get_function_calls() (google.adk.events.event method)":[[0,"id24",false]],"get_function_responses() (google.adk.events.event method)":[[0,"google.adk.events.Event.get_function_responses",false]],"get_input_files() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.get_input_files",false]],"get_processed_file_names() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.get_processed_file_names",false]],"get_session() (google.adk.sessions.basesessionservice method)":[[0,"google.adk.sessions.BaseSessionService.get_session",false]],"get_session() (google.adk.sessions.databasesessionservice method)":[[0,"google.adk.sessions.DatabaseSessionService.get_session",false]],"get_session() (google.adk.sessions.inmemorysessionservice method)":[[0,"google.adk.sessions.InMemorySessionService.get_session",false]],"get_session() (google.adk.sessions.vertexaisessionservice method)":[[0,"google.adk.sessions.VertexAiSessionService.get_session",false]],"get_state_delta() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.get_state_delta",false]],"get_tool() (google.adk.tools.apihubtoolset method)":[[0,"google.adk.tools.APIHubToolset.get_tool",false]],"get_tool() (google.adk.tools.openapi_tool.openapitoolset method)":[[0,"google.adk.tools.openapi_tool.OpenAPIToolset.get_tool",false]],"get_tools() (google.adk.tools.apihubtoolset method)":[[0,"google.adk.tools.APIHubToolset.get_tools",false]],"get_tools() (google.adk.tools.application_integration_tool.applicationintegrationtoolset method)":[[0,"google.adk.tools.application_integration_tool.ApplicationIntegrationToolset.get_tools",false]],"get_tools() (google.adk.tools.openapi_tool.openapitoolset method)":[[0,"google.adk.tools.openapi_tool.OpenAPIToolset.get_tools",false]],"global_instruction (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.global_instruction",false]],"google.adk.agents":[[0,"module-google.adk.agents",false]],"google.adk.artifacts":[[0,"module-google.adk.artifacts",false]],"google.adk.code_executors":[[0,"module-google.adk.code_executors",false]],"google.adk.evaluation":[[0,"module-google.adk.evaluation",false]],"google.adk.events":[[0,"module-google.adk.events",false]],"google.adk.examples":[[0,"module-google.adk.examples",false]],"google.adk.memory":[[0,"module-google.adk.memory",false]],"google.adk.models":[[0,"module-google.adk.models",false]],"google.adk.planners":[[0,"module-google.adk.planners",false]],"google.adk.runners":[[0,"module-google.adk.runners",false]],"google.adk.sessions":[[0,"module-google.adk.sessions",false]],"google.adk.tools":[[0,"module-google.adk.tools",false]],"google.adk.tools.application_integration_tool":[[0,"module-google.adk.tools.application_integration_tool",false]],"google.adk.tools.google_api_tool":[[0,"module-google.adk.tools.google_api_tool",false]],"google.adk.tools.mcp_tool":[[0,"module-google.adk.tools.mcp_tool",false]],"google.adk.tools.openapi_tool":[[0,"module-google.adk.tools.openapi_tool",false]],"google.adk.tools.retrieval":[[0,"module-google.adk.tools.retrieval",false]],"has_delta() (google.adk.sessions.state method)":[[0,"google.adk.sessions.State.has_delta",false]],"has_trailing_code_execution_result() (google.adk.events.event method)":[[0,"google.adk.events.Event.has_trailing_code_execution_result",false]],"id (google.adk.events.event attribute)":[[0,"google.adk.events.Event.id",false],[0,"id20",false]],"id (google.adk.sessions.session attribute)":[[0,"google.adk.sessions.Session.id",false],[0,"id38",false]],"image (google.adk.code_executors.containercodeexecutor attribute)":[[0,"google.adk.code_executors.ContainerCodeExecutor.image",false],[0,"id15",false]],"include_contents (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.include_contents",false]],"increment_error_count() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.increment_error_count",false]],"inmemorymemoryservice (class in google.adk.memory)":[[0,"google.adk.memory.InMemoryMemoryService",false]],"inmemoryrunner (class in google.adk.runners)":[[0,"google.adk.runners.InMemoryRunner",false]],"inmemorysessionservice (class in google.adk.sessions)":[[0,"google.adk.sessions.InMemorySessionService",false]],"input (google.adk.examples.example attribute)":[[0,"google.adk.examples.Example.input",false],[0,"id26",false]],"input_schema (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.input_schema",false]],"instruction (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.instruction",false]],"integrationconnectortool (class in google.adk.tools.application_integration_tool)":[[0,"google.adk.tools.application_integration_tool.IntegrationConnectorTool",false]],"invocation_context (google.adk.tools.toolcontext attribute)":[[0,"google.adk.tools.ToolContext.invocation_context",false]],"invocation_id (google.adk.events.event attribute)":[[0,"google.adk.events.Event.invocation_id",false],[0,"id21",false]],"is_final_response (google.adk.events.event attribute)":[[0,"google.adk.events.Event.is_final_response",false]],"is_final_response() (google.adk.events.event method)":[[0,"id25",false]],"is_long_running (google.adk.tools.basetool attribute)":[[0,"google.adk.tools.BaseTool.is_long_running",false]],"is_long_running (google.adk.tools.longrunningfunctiontool attribute)":[[0,"google.adk.tools.LongRunningFunctionTool.is_long_running",false]],"last_update_time (google.adk.sessions.session attribute)":[[0,"google.adk.sessions.Session.last_update_time",false],[0,"id39",false]],"list_artifact_keys() (google.adk.artifacts.baseartifactservice method)":[[0,"google.adk.artifacts.BaseArtifactService.list_artifact_keys",false]],"list_artifact_keys() (google.adk.artifacts.gcsartifactservice method)":[[0,"google.adk.artifacts.GcsArtifactService.list_artifact_keys",false]],"list_artifact_keys() (google.adk.artifacts.inmemoryartifactservice method)":[[0,"google.adk.artifacts.InMemoryArtifactService.list_artifact_keys",false]],"list_artifacts() (google.adk.tools.toolcontext method)":[[0,"google.adk.tools.ToolContext.list_artifacts",false]],"list_events() (google.adk.sessions.basesessionservice method)":[[0,"google.adk.sessions.BaseSessionService.list_events",false]],"list_events() (google.adk.sessions.databasesessionservice method)":[[0,"google.adk.sessions.DatabaseSessionService.list_events",false]],"list_events() (google.adk.sessions.inmemorysessionservice method)":[[0,"google.adk.sessions.InMemorySessionService.list_events",false]],"list_events() (google.adk.sessions.vertexaisessionservice method)":[[0,"google.adk.sessions.VertexAiSessionService.list_events",false]],"list_sessions() (google.adk.sessions.basesessionservice method)":[[0,"google.adk.sessions.BaseSessionService.list_sessions",false]],"list_sessions() (google.adk.sessions.databasesessionservice method)":[[0,"google.adk.sessions.DatabaseSessionService.list_sessions",false]],"list_sessions() (google.adk.sessions.inmemorysessionservice method)":[[0,"google.adk.sessions.InMemorySessionService.list_sessions",false]],"list_sessions() (google.adk.sessions.vertexaisessionservice method)":[[0,"google.adk.sessions.VertexAiSessionService.list_sessions",false]],"list_versions() (google.adk.artifacts.baseartifactservice method)":[[0,"google.adk.artifacts.BaseArtifactService.list_versions",false]],"list_versions() (google.adk.artifacts.gcsartifactservice method)":[[0,"google.adk.artifacts.GcsArtifactService.list_versions",false]],"list_versions() (google.adk.artifacts.inmemoryartifactservice method)":[[0,"google.adk.artifacts.InMemoryArtifactService.list_versions",false]],"llamaindexretrieval (class in google.adk.tools.retrieval)":[[0,"google.adk.tools.retrieval.LlamaIndexRetrieval",false]],"llmregistry (class in google.adk.models)":[[0,"google.adk.models.LLMRegistry",false]],"load_artifact() (google.adk.artifacts.baseartifactservice method)":[[0,"google.adk.artifacts.BaseArtifactService.load_artifact",false]],"load_artifact() (google.adk.artifacts.gcsartifactservice method)":[[0,"google.adk.artifacts.GcsArtifactService.load_artifact",false]],"load_artifact() (google.adk.artifacts.inmemoryartifactservice method)":[[0,"google.adk.artifacts.InMemoryArtifactService.load_artifact",false]],"load_tools() (google.adk.tools.mcp_tool.mcptoolset method)":[[0,"google.adk.tools.mcp_tool.MCPToolset.load_tools",false]],"long_running_tool_ids (google.adk.events.event attribute)":[[0,"google.adk.events.Event.long_running_tool_ids",false],[0,"id22",false]],"longrunningfunctiontool (class in google.adk.tools)":[[0,"google.adk.tools.LongRunningFunctionTool",false]],"max_iterations (google.adk.agents.loopagent attribute)":[[0,"google.adk.agents.LoopAgent.max_iterations",false]],"mcptool (class in google.adk.tools.mcp_tool)":[[0,"google.adk.tools.mcp_tool.MCPTool",false]],"mcptoolset (class in google.adk.tools.mcp_tool)":[[0,"google.adk.tools.mcp_tool.MCPToolset",false]],"memory_service (google.adk.runners.runner attribute)":[[0,"google.adk.runners.Runner.memory_service",false],[0,"id34",false]],"model (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.model",false]],"model (google.adk.models.basellm attribute)":[[0,"google.adk.models.BaseLlm.model",false],[0,"id28",false]],"model (google.adk.models.gemini attribute)":[[0,"google.adk.models.Gemini.model",false],[0,"id29",false]],"model_post_init() (google.adk.agents.baseagent method)":[[0,"google.adk.agents.BaseAgent.model_post_init",false]],"model_post_init() (google.adk.code_executors.containercodeexecutor method)":[[0,"google.adk.code_executors.ContainerCodeExecutor.model_post_init",false]],"model_post_init() (google.adk.code_executors.vertexaicodeexecutor method)":[[0,"google.adk.code_executors.VertexAiCodeExecutor.model_post_init",false]],"model_post_init() (google.adk.events.event method)":[[0,"google.adk.events.Event.model_post_init",false]],"module":[[0,"module-google.adk.agents",false],[0,"module-google.adk.artifacts",false],[0,"module-google.adk.code_executors",false],[0,"module-google.adk.evaluation",false],[0,"module-google.adk.events",false],[0,"module-google.adk.examples",false],[0,"module-google.adk.memory",false],[0,"module-google.adk.models",false],[0,"module-google.adk.planners",false],[0,"module-google.adk.runners",false],[0,"module-google.adk.sessions",false],[0,"module-google.adk.tools",false],[0,"module-google.adk.tools.application_integration_tool",false],[0,"module-google.adk.tools.google_api_tool",false],[0,"module-google.adk.tools.mcp_tool",false],[0,"module-google.adk.tools.openapi_tool",false],[0,"module-google.adk.tools.retrieval",false]],"name (google.adk.agents.baseagent attribute)":[[0,"google.adk.agents.BaseAgent.name",false]],"name (google.adk.tools.basetool attribute)":[[0,"google.adk.tools.BaseTool.name",false]],"new_id() (google.adk.events.event static method)":[[0,"google.adk.events.Event.new_id",false]],"new_llm() (google.adk.models.llmregistry static method)":[[0,"google.adk.models.LLMRegistry.new_llm",false]],"openapitoolset (class in google.adk.tools.openapi_tool)":[[0,"google.adk.tools.openapi_tool.OpenAPIToolset",false]],"optimize_data_file (google.adk.code_executors.basecodeexecutor attribute)":[[0,"google.adk.code_executors.BaseCodeExecutor.optimize_data_file",false],[0,"id11",false]],"optimize_data_file (google.adk.code_executors.containercodeexecutor attribute)":[[0,"google.adk.code_executors.ContainerCodeExecutor.optimize_data_file",false]],"optimize_data_file (google.adk.code_executors.unsafelocalcodeexecutor attribute)":[[0,"google.adk.code_executors.UnsafeLocalCodeExecutor.optimize_data_file",false]],"optional_fields (google.adk.tools.application_integration_tool.integrationconnectortool attribute)":[[0,"google.adk.tools.application_integration_tool.IntegrationConnectorTool.OPTIONAL_FIELDS",false]],"output (google.adk.examples.example attribute)":[[0,"google.adk.examples.Example.output",false],[0,"id27",false]],"output_key (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.output_key",false]],"output_schema (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.output_schema",false]],"parent_agent (google.adk.agents.baseagent attribute)":[[0,"google.adk.agents.BaseAgent.parent_agent",false]],"planner (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.planner",false]],"planreactplanner (class in google.adk.planners)":[[0,"google.adk.planners.PlanReActPlanner",false]],"process_llm_request() (google.adk.tools.basetool method)":[[0,"google.adk.tools.BaseTool.process_llm_request",false]],"process_llm_request() (google.adk.tools.exampletool method)":[[0,"google.adk.tools.ExampleTool.process_llm_request",false]],"process_llm_request() (google.adk.tools.retrieval.vertexairagretrieval method)":[[0,"google.adk.tools.retrieval.VertexAiRagRetrieval.process_llm_request",false]],"process_llm_request() (google.adk.tools.vertexaisearchtool method)":[[0,"google.adk.tools.VertexAiSearchTool.process_llm_request",false]],"process_planning_response() (google.adk.planners.baseplanner method)":[[0,"google.adk.planners.BasePlanner.process_planning_response",false]],"process_planning_response() (google.adk.planners.builtinplanner method)":[[0,"google.adk.planners.BuiltInPlanner.process_planning_response",false]],"process_planning_response() (google.adk.planners.planreactplanner method)":[[0,"google.adk.planners.PlanReActPlanner.process_planning_response",false]],"register() (google.adk.models.llmregistry static method)":[[0,"google.adk.models.LLMRegistry.register",false]],"request_credential() (google.adk.tools.toolcontext method)":[[0,"google.adk.tools.ToolContext.request_credential",false]],"requested_auth_configs (google.adk.events.eventactions attribute)":[[0,"google.adk.events.EventActions.requested_auth_configs",false]],"reset_error_count() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.reset_error_count",false]],"resolve() (google.adk.models.llmregistry static method)":[[0,"google.adk.models.LLMRegistry.resolve",false]],"resource_name (google.adk.code_executors.vertexaicodeexecutor attribute)":[[0,"google.adk.code_executors.VertexAiCodeExecutor.resource_name",false],[0,"id16",false]],"restapitool (class in google.adk.tools.openapi_tool)":[[0,"google.adk.tools.openapi_tool.RestApiTool",false]],"root_agent (google.adk.agents.baseagent property)":[[0,"google.adk.agents.BaseAgent.root_agent",false]],"run() (google.adk.runners.runner method)":[[0,"google.adk.runners.Runner.run",false]],"run_async() (google.adk.agents.baseagent method)":[[0,"google.adk.agents.BaseAgent.run_async",false]],"run_async() (google.adk.runners.runner method)":[[0,"google.adk.runners.Runner.run_async",false]],"run_async() (google.adk.tools.application_integration_tool.integrationconnectortool method)":[[0,"google.adk.tools.application_integration_tool.IntegrationConnectorTool.run_async",false]],"run_async() (google.adk.tools.basetool method)":[[0,"google.adk.tools.BaseTool.run_async",false]],"run_async() (google.adk.tools.functiontool method)":[[0,"google.adk.tools.FunctionTool.run_async",false]],"run_async() (google.adk.tools.mcp_tool.mcptool method)":[[0,"google.adk.tools.mcp_tool.MCPTool.run_async",false]],"run_async() (google.adk.tools.openapi_tool.restapitool method)":[[0,"google.adk.tools.openapi_tool.RestApiTool.run_async",false]],"run_async() (google.adk.tools.retrieval.llamaindexretrieval method)":[[0,"google.adk.tools.retrieval.LlamaIndexRetrieval.run_async",false]],"run_async() (google.adk.tools.retrieval.vertexairagretrieval method)":[[0,"google.adk.tools.retrieval.VertexAiRagRetrieval.run_async",false]],"run_live() (google.adk.agents.baseagent method)":[[0,"google.adk.agents.BaseAgent.run_live",false]],"run_live() (google.adk.runners.runner method)":[[0,"google.adk.runners.Runner.run_live",false]],"runner (class in google.adk.runners)":[[0,"google.adk.runners.Runner",false]],"save_artifact() (google.adk.artifacts.baseartifactservice method)":[[0,"google.adk.artifacts.BaseArtifactService.save_artifact",false]],"save_artifact() (google.adk.artifacts.gcsartifactservice method)":[[0,"google.adk.artifacts.GcsArtifactService.save_artifact",false]],"save_artifact() (google.adk.artifacts.inmemoryartifactservice method)":[[0,"google.adk.artifacts.InMemoryArtifactService.save_artifact",false]],"search_engine_id (google.adk.tools.vertexaisearchtool attribute)":[[0,"google.adk.tools.VertexAiSearchTool.search_engine_id",false]],"search_memory() (google.adk.memory.basememoryservice method)":[[0,"google.adk.memory.BaseMemoryService.search_memory",false]],"search_memory() (google.adk.memory.inmemorymemoryservice method)":[[0,"google.adk.memory.InMemoryMemoryService.search_memory",false]],"search_memory() (google.adk.memory.vertexairagmemoryservice method)":[[0,"google.adk.memory.VertexAiRagMemoryService.search_memory",false]],"search_memory() (google.adk.tools.toolcontext method)":[[0,"google.adk.tools.ToolContext.search_memory",false]],"session (google.adk.tools.mcp_tool.mcptoolset attribute)":[[0,"google.adk.tools.mcp_tool.MCPToolset.session",false]],"session_events (google.adk.memory.inmemorymemoryservice attribute)":[[0,"google.adk.memory.InMemoryMemoryService.session_events",false]],"session_service (google.adk.runners.runner attribute)":[[0,"google.adk.runners.Runner.session_service",false],[0,"id35",false]],"set_execution_id() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.set_execution_id",false]],"skip_summarization (google.adk.events.eventactions attribute)":[[0,"google.adk.events.EventActions.skip_summarization",false]],"state (class in google.adk.sessions)":[[0,"google.adk.sessions.State",false]],"state (google.adk.sessions.session attribute)":[[0,"google.adk.sessions.Session.state",false],[0,"id40",false]],"state_delta (google.adk.events.eventactions attribute)":[[0,"google.adk.events.EventActions.state_delta",false]],"stateful (google.adk.code_executors.basecodeexecutor attribute)":[[0,"google.adk.code_executors.BaseCodeExecutor.stateful",false],[0,"id12",false]],"stateful (google.adk.code_executors.containercodeexecutor attribute)":[[0,"google.adk.code_executors.ContainerCodeExecutor.stateful",false]],"stateful (google.adk.code_executors.unsafelocalcodeexecutor attribute)":[[0,"google.adk.code_executors.UnsafeLocalCodeExecutor.stateful",false]],"sub_agents (google.adk.agents.baseagent attribute)":[[0,"google.adk.agents.BaseAgent.sub_agents",false]],"supported_models() (google.adk.models.basellm class method)":[[0,"google.adk.models.BaseLlm.supported_models",false]],"supported_models() (google.adk.models.gemini static method)":[[0,"google.adk.models.Gemini.supported_models",false]],"temp_prefix (google.adk.sessions.state attribute)":[[0,"google.adk.sessions.State.TEMP_PREFIX",false]],"thinking_config (google.adk.planners.builtinplanner attribute)":[[0,"google.adk.planners.BuiltInPlanner.thinking_config",false],[0,"id30",false]],"timestamp (google.adk.events.event attribute)":[[0,"google.adk.events.Event.timestamp",false],[0,"id23",false]],"to_dict() (google.adk.sessions.state method)":[[0,"google.adk.sessions.State.to_dict",false]],"toolcontext (class in google.adk.tools)":[[0,"google.adk.tools.ToolContext",false]],"tools (google.adk.agents.llmagent attribute)":[[0,"google.adk.agents.LlmAgent.tools",false]],"transfer_to_agent (google.adk.events.eventactions attribute)":[[0,"google.adk.events.EventActions.transfer_to_agent",false]],"transfer_to_agent() (in module google.adk.tools)":[[0,"google.adk.tools.transfer_to_agent",false]],"update() (google.adk.sessions.state method)":[[0,"google.adk.sessions.State.update",false]],"update_code_execution_result() (google.adk.code_executors.codeexecutorcontext method)":[[0,"google.adk.code_executors.CodeExecutorContext.update_code_execution_result",false]],"user_id (google.adk.sessions.session attribute)":[[0,"google.adk.sessions.Session.user_id",false],[0,"id41",false]],"user_prefix (google.adk.sessions.state attribute)":[[0,"google.adk.sessions.State.USER_PREFIX",false]],"vertexaiexamplestore (class in google.adk.examples)":[[0,"google.adk.examples.VertexAiExampleStore",false]],"vertexairagmemoryservice (class in google.adk.memory)":[[0,"google.adk.memory.VertexAiRagMemoryService",false]],"vertexairagretrieval (class in google.adk.tools.retrieval)":[[0,"google.adk.tools.retrieval.VertexAiRagRetrieval",false]],"vertexaisearchtool (class in google.adk.tools)":[[0,"google.adk.tools.VertexAiSearchTool",false]],"vertexaisessionservice (class in google.adk.sessions)":[[0,"google.adk.sessions.VertexAiSessionService",false]]},"objects":{"google.adk":[[0,0,0,"-","agents"],[0,0,0,"-","artifacts"],[0,0,0,"-","code_executors"],[0,0,0,"-","evaluation"],[0,0,0,"-","events"],[0,0,0,"-","examples"],[0,0,0,"-","memory"],[0,0,0,"-","models"],[0,0,0,"-","planners"],[0,0,0,"-","runners"],[0,0,0,"-","sessions"],[0,0,0,"-","tools"]],"google.adk.agents":[[0,1,1,"","Agent"],[0,2,1,"","BaseAgent"],[0,2,1,"","LlmAgent"],[0,2,1,"","LoopAgent"],[0,2,1,"","ParallelAgent"],[0,2,1,"","SequentialAgent"]],"google.adk.agents.BaseAgent":[[0,3,1,"","after_agent_callback"],[0,3,1,"","before_agent_callback"],[0,3,1,"","description"],[0,4,1,"","find_agent"],[0,4,1,"","find_sub_agent"],[0,4,1,"","model_post_init"],[0,3,1,"","name"],[0,3,1,"","parent_agent"],[0,5,1,"","root_agent"],[0,4,1,"","run_async"],[0,4,1,"","run_live"],[0,3,1,"","sub_agents"]],"google.adk.agents.LlmAgent":[[0,3,1,"","after_model_callback"],[0,3,1,"","after_tool_callback"],[0,3,1,"","before_model_callback"],[0,3,1,"","before_tool_callback"],[0,5,1,"","canonical_after_model_callbacks"],[0,5,1,"","canonical_before_model_callbacks"],[0,4,1,"","canonical_global_instruction"],[0,4,1,"","canonical_instruction"],[0,5,1,"","canonical_model"],[0,5,1,"","canonical_tools"],[0,3,1,"","code_executor"],[0,3,1,"","disallow_transfer_to_parent"],[0,3,1,"","disallow_transfer_to_peers"],[0,3,1,"","examples"],[0,3,1,"","generate_content_config"],[0,3,1,"","global_instruction"],[0,3,1,"","include_contents"],[0,3,1,"","input_schema"],[0,3,1,"","instruction"],[0,3,1,"","model"],[0,3,1,"","output_key"],[0,3,1,"","output_schema"],[0,3,1,"","planner"],[0,3,1,"","tools"]],"google.adk.agents.LoopAgent":[[0,3,1,"","max_iterations"]],"google.adk.artifacts":[[0,6,1,"","BaseArtifactService"],[0,6,1,"","GcsArtifactService"],[0,2,1,"","InMemoryArtifactService"]],"google.adk.artifacts.BaseArtifactService":[[0,4,1,"","delete_artifact"],[0,4,1,"","list_artifact_keys"],[0,4,1,"","list_versions"],[0,4,1,"","load_artifact"],[0,4,1,"","save_artifact"]],"google.adk.artifacts.GcsArtifactService":[[0,4,1,"","delete_artifact"],[0,4,1,"","list_artifact_keys"],[0,4,1,"","list_versions"],[0,4,1,"","load_artifact"],[0,4,1,"","save_artifact"]],"google.adk.artifacts.InMemoryArtifactService":[[0,3,1,"","artifacts"],[0,4,1,"","delete_artifact"],[0,4,1,"","list_artifact_keys"],[0,4,1,"","list_versions"],[0,4,1,"","load_artifact"],[0,4,1,"","save_artifact"]],"google.adk.code_executors":[[0,2,1,"","BaseCodeExecutor"],[0,6,1,"","CodeExecutorContext"],[0,2,1,"","ContainerCodeExecutor"],[0,2,1,"","UnsafeLocalCodeExecutor"],[0,2,1,"","VertexAiCodeExecutor"]],"google.adk.code_executors.BaseCodeExecutor":[[0,3,1,"id0","code_block_delimiters"],[0,3,1,"id9","error_retry_attempts"],[0,4,1,"","execute_code"],[0,3,1,"id10","execution_result_delimiters"],[0,3,1,"id11","optimize_data_file"],[0,3,1,"id12","stateful"]],"google.adk.code_executors.CodeExecutorContext":[[0,4,1,"","add_input_files"],[0,4,1,"","add_processed_file_names"],[0,4,1,"","clear_input_files"],[0,4,1,"","get_error_count"],[0,4,1,"","get_execution_id"],[0,4,1,"","get_input_files"],[0,4,1,"","get_processed_file_names"],[0,4,1,"","get_state_delta"],[0,4,1,"","increment_error_count"],[0,4,1,"","reset_error_count"],[0,4,1,"","set_execution_id"],[0,4,1,"","update_code_execution_result"]],"google.adk.code_executors.ContainerCodeExecutor":[[0,3,1,"id13","base_url"],[0,3,1,"id14","docker_path"],[0,4,1,"","execute_code"],[0,3,1,"id15","image"],[0,4,1,"","model_post_init"],[0,3,1,"","optimize_data_file"],[0,3,1,"","stateful"]],"google.adk.code_executors.UnsafeLocalCodeExecutor":[[0,4,1,"","execute_code"],[0,3,1,"","optimize_data_file"],[0,3,1,"","stateful"]],"google.adk.code_executors.VertexAiCodeExecutor":[[0,4,1,"","execute_code"],[0,4,1,"","model_post_init"],[0,3,1,"id16","resource_name"]],"google.adk.evaluation":[[0,6,1,"","AgentEvaluator"]],"google.adk.evaluation.AgentEvaluator":[[0,4,1,"","evaluate"],[0,4,1,"","find_config_for_test_file"]],"google.adk.events":[[0,2,1,"","Event"],[0,2,1,"","EventActions"]],"google.adk.events.Event":[[0,3,1,"id17","actions"],[0,3,1,"id18","author"],[0,3,1,"id19","branch"],[0,4,1,"id24","get_function_calls"],[0,4,1,"","get_function_responses"],[0,4,1,"","has_trailing_code_execution_result"],[0,3,1,"id20","id"],[0,3,1,"id21","invocation_id"],[0,4,1,"id25","is_final_response"],[0,3,1,"id22","long_running_tool_ids"],[0,4,1,"","model_post_init"],[0,4,1,"","new_id"],[0,3,1,"id23","timestamp"]],"google.adk.events.EventActions":[[0,3,1,"","artifact_delta"],[0,3,1,"","escalate"],[0,3,1,"","requested_auth_configs"],[0,3,1,"","skip_summarization"],[0,3,1,"","state_delta"],[0,3,1,"","transfer_to_agent"]],"google.adk.examples":[[0,6,1,"","BaseExampleProvider"],[0,2,1,"","Example"],[0,6,1,"","VertexAiExampleStore"]],"google.adk.examples.BaseExampleProvider":[[0,4,1,"","get_examples"]],"google.adk.examples.Example":[[0,3,1,"id26","input"],[0,3,1,"id27","output"]],"google.adk.examples.VertexAiExampleStore":[[0,4,1,"","get_examples"]],"google.adk.memory":[[0,6,1,"","BaseMemoryService"],[0,6,1,"","InMemoryMemoryService"],[0,6,1,"","VertexAiRagMemoryService"]],"google.adk.memory.BaseMemoryService":[[0,4,1,"","add_session_to_memory"],[0,4,1,"","search_memory"]],"google.adk.memory.InMemoryMemoryService":[[0,4,1,"","add_session_to_memory"],[0,4,1,"","search_memory"],[0,1,1,"","session_events"]],"google.adk.memory.VertexAiRagMemoryService":[[0,4,1,"","add_session_to_memory"],[0,4,1,"","search_memory"]],"google.adk.models":[[0,2,1,"","BaseLlm"],[0,2,1,"","Gemini"],[0,6,1,"","LLMRegistry"]],"google.adk.models.BaseLlm":[[0,4,1,"","connect"],[0,4,1,"","generate_content_async"],[0,3,1,"id28","model"],[0,4,1,"","supported_models"]],"google.adk.models.Gemini":[[0,5,1,"","api_client"],[0,4,1,"","connect"],[0,4,1,"","generate_content_async"],[0,3,1,"id29","model"],[0,4,1,"","supported_models"]],"google.adk.models.LLMRegistry":[[0,4,1,"","new_llm"],[0,4,1,"","register"],[0,4,1,"","resolve"]],"google.adk.planners":[[0,6,1,"","BasePlanner"],[0,6,1,"","BuiltInPlanner"],[0,6,1,"","PlanReActPlanner"]],"google.adk.planners.BasePlanner":[[0,4,1,"","build_planning_instruction"],[0,4,1,"","process_planning_response"]],"google.adk.planners.BuiltInPlanner":[[0,4,1,"","apply_thinking_config"],[0,4,1,"","build_planning_instruction"],[0,4,1,"","process_planning_response"],[0,1,1,"id30","thinking_config"]],"google.adk.planners.PlanReActPlanner":[[0,4,1,"","build_planning_instruction"],[0,4,1,"","process_planning_response"]],"google.adk.runners":[[0,6,1,"","InMemoryRunner"],[0,6,1,"","Runner"]],"google.adk.runners.InMemoryRunner":[[0,1,1,"","agent"],[0,1,1,"","app_name"]],"google.adk.runners.Runner":[[0,1,1,"id31","agent"],[0,1,1,"id32","app_name"],[0,1,1,"id33","artifact_service"],[0,4,1,"","close_session"],[0,1,1,"id34","memory_service"],[0,4,1,"","run"],[0,4,1,"","run_async"],[0,4,1,"","run_live"],[0,1,1,"id35","session_service"]],"google.adk.sessions":[[0,6,1,"","BaseSessionService"],[0,6,1,"","DatabaseSessionService"],[0,6,1,"","InMemorySessionService"],[0,2,1,"","Session"],[0,6,1,"","State"],[0,6,1,"","VertexAiSessionService"]],"google.adk.sessions.BaseSessionService":[[0,4,1,"","append_event"],[0,4,1,"","close_session"],[0,4,1,"","create_session"],[0,4,1,"","delete_session"],[0,4,1,"","get_session"],[0,4,1,"","list_events"],[0,4,1,"","list_sessions"]],"google.adk.sessions.DatabaseSessionService":[[0,4,1,"","append_event"],[0,4,1,"","create_session"],[0,4,1,"","delete_session"],[0,4,1,"","get_session"],[0,4,1,"","list_events"],[0,4,1,"","list_sessions"]],"google.adk.sessions.InMemorySessionService":[[0,4,1,"","append_event"],[0,4,1,"","create_session"],[0,4,1,"","delete_session"],[0,4,1,"","get_session"],[0,4,1,"","list_events"],[0,4,1,"","list_sessions"]],"google.adk.sessions.Session":[[0,3,1,"id36","app_name"],[0,3,1,"id37","events"],[0,3,1,"id38","id"],[0,3,1,"id39","last_update_time"],[0,3,1,"id40","state"],[0,3,1,"id41","user_id"]],"google.adk.sessions.State":[[0,1,1,"","APP_PREFIX"],[0,1,1,"","TEMP_PREFIX"],[0,1,1,"","USER_PREFIX"],[0,4,1,"","get"],[0,4,1,"","has_delta"],[0,4,1,"","to_dict"],[0,4,1,"","update"]],"google.adk.sessions.VertexAiSessionService":[[0,4,1,"","append_event"],[0,4,1,"","create_session"],[0,4,1,"","delete_session"],[0,4,1,"","get_session"],[0,4,1,"","list_events"],[0,4,1,"","list_sessions"]],"google.adk.tools":[[0,6,1,"","APIHubToolset"],[0,2,1,"","AuthToolArguments"],[0,6,1,"","BaseTool"],[0,6,1,"","ExampleTool"],[0,6,1,"","FunctionTool"],[0,6,1,"","LongRunningFunctionTool"],[0,6,1,"","ToolContext"],[0,6,1,"","VertexAiSearchTool"],[0,0,0,"-","application_integration_tool"],[0,7,1,"","exit_loop"],[0,0,0,"-","google_api_tool"],[0,0,0,"-","mcp_tool"],[0,0,0,"-","openapi_tool"],[0,0,0,"-","retrieval"],[0,7,1,"","transfer_to_agent"]],"google.adk.tools.APIHubToolset":[[0,4,1,"","get_tool"],[0,4,1,"","get_tools"]],"google.adk.tools.AuthToolArguments":[[0,3,1,"","auth_config"],[0,3,1,"","function_call_id"]],"google.adk.tools.BaseTool":[[0,1,1,"","description"],[0,1,1,"","is_long_running"],[0,1,1,"","name"],[0,4,1,"","process_llm_request"],[0,4,1,"","run_async"]],"google.adk.tools.ExampleTool":[[0,1,1,"","examples"],[0,4,1,"","process_llm_request"]],"google.adk.tools.FunctionTool":[[0,1,1,"","func"],[0,4,1,"","run_async"]],"google.adk.tools.LongRunningFunctionTool":[[0,1,1,"","is_long_running"]],"google.adk.tools.ToolContext":[[0,5,1,"","actions"],[0,1,1,"","event_actions"],[0,1,1,"","function_call_id"],[0,4,1,"","get_auth_response"],[0,1,1,"","invocation_context"],[0,4,1,"","list_artifacts"],[0,4,1,"","request_credential"],[0,4,1,"","search_memory"]],"google.adk.tools.VertexAiSearchTool":[[0,1,1,"","data_store_id"],[0,4,1,"","process_llm_request"],[0,1,1,"","search_engine_id"]],"google.adk.tools.application_integration_tool":[[0,6,1,"","ApplicationIntegrationToolset"],[0,6,1,"","IntegrationConnectorTool"]],"google.adk.tools.application_integration_tool.ApplicationIntegrationToolset":[[0,4,1,"","get_tools"]],"google.adk.tools.application_integration_tool.IntegrationConnectorTool":[[0,1,1,"","EXCLUDE_FIELDS"],[0,1,1,"","OPTIONAL_FIELDS"],[0,4,1,"","run_async"]],"google.adk.tools.mcp_tool":[[0,6,1,"","MCPTool"],[0,6,1,"","MCPToolset"],[0,7,1,"","adk_to_mcp_tool_type"],[0,7,1,"","gemini_to_json_schema"]],"google.adk.tools.mcp_tool.MCPTool":[[0,4,1,"","run_async"]],"google.adk.tools.mcp_tool.MCPToolset":[[0,1,1,"","connection_params"],[0,1,1,"","exit_stack"],[0,4,1,"","from_server"],[0,4,1,"","load_tools"],[0,1,1,"","session"]],"google.adk.tools.openapi_tool":[[0,6,1,"","OpenAPIToolset"],[0,6,1,"","RestApiTool"]],"google.adk.tools.openapi_tool.OpenAPIToolset":[[0,4,1,"","get_tool"],[0,4,1,"","get_tools"]],"google.adk.tools.openapi_tool.RestApiTool":[[0,4,1,"","call"],[0,4,1,"","configure_auth_credential"],[0,4,1,"","configure_auth_scheme"],[0,4,1,"","from_parsed_operation"],[0,4,1,"","from_parsed_operation_str"],[0,4,1,"","run_async"]],"google.adk.tools.retrieval":[[0,6,1,"","BaseRetrievalTool"],[0,6,1,"","FilesRetrieval"],[0,6,1,"","LlamaIndexRetrieval"],[0,6,1,"","VertexAiRagRetrieval"]],"google.adk.tools.retrieval.LlamaIndexRetrieval":[[0,4,1,"","run_async"]],"google.adk.tools.retrieval.VertexAiRagRetrieval":[[0,4,1,"","process_llm_request"],[0,4,1,"","run_async"]]},"objnames":{"0":["py","module","Python module"],"1":["py","attribute","Python attribute"],"2":["py","pydantic_model","Python model"],"3":["py","pydantic_field","Python field"],"4":["py","method","Python method"],"5":["py","property","Python property"],"6":["py","class","Python class"],"7":["py","function","Python function"]},"objtypes":{"0":"py:module","1":"py:attribute","2":"py:pydantic_model","3":"py:pydantic_field","4":"py:method","5":"py:property","6":"py:class","7":"py:function"},"terms":{"":0,"0":0,"001":0,"03":0,"1":0,"10":0,"101":0,"123":0,"1234":0,"128":0,"2":0,"201":0,"256":0,"3":0,"301":0,"4":0,"40":0,"456":0,"5":0,"500":0,"509":0,"512":0,"639":0,"64":0,"8":0,"8090":0,"9":0,"A":0,"By":0,"FOR":0,"For":0,"If":0,"In":0,"It":0,"Not":0,"One":0,"Or":0,"The":0,"Then":0,"There":0,"To":0,"Will":0,"With":0,"_":0,"__init__":0,"__model_validator_aft":0,"__validate_generate_content_config":0,"__validate_nam":0,"_baseagent__context":0,"_event__context":0,"_io":0,"_singleaftermodelcallback":0,"_singlebeforemodelcallback":0,"a_long_running_funct":0,"abc":0,"about":0,"abstract":0,"abstractmethod":0,"accept":0,"access":0,"access_token":0,"account":0,"aclos":0,"act":0,"action":[0,1],"action1":0,"activ":0,"actual":0,"ad":0,"add":0,"add_input_fil":[0,1],"add_processed_file_nam":[0,1],"add_session_to_memori":[0,1],"addit":0,"additionalproperti":0,"address":0,"adher":0,"adjust":0,"adk":1,"adk_to_mcp_tool_typ":[0,1],"after":0,"after_agent_callback":[0,1],"after_model_callback":[0,1],"after_tool_callback":[0,1],"afteragentcallback":0,"aftermodelcallback":0,"aftertoolcallback":0,"against":0,"agent":1,"agent_1":0,"agent_2":0,"agent_3":0,"agent_modul":0,"agent_nam":0,"agentevalu":[0,1],"ai":0,"algorithm":0,"alia":0,"all":0,"allow":0,"allowedfunctionnam":0,"along":0,"alpha":0,"alphanumer":0,"alreadi":0,"also":0,"alwai":0,"amount":0,"an":0,"ancestor":0,"ani":0,"anoth":0,"answer":0,"anyof":0,"apart":0,"api":0,"api_cli":[0,1],"api_kei":0,"api_trigg":0,"apige":0,"apihub_cli":0,"apihub_resource_nam":0,"apihub_tool":0,"apihub_toolset":0,"apihubtool":0,"apihubtoolset":[0,1],"apikei":0,"apikeyin":0,"apinam":0,"apivers":0,"app":0,"app_nam":[0,1],"app_prefix":[0,1],"appear":0,"append":0,"append_ev":[0,1],"appli":0,"applic":0,"application_integration_tool":0,"application_integration_toolset":0,"applicationintegrationtool":0,"applicationintegrationtoolset":[0,1],"apply_thinking_config":[0,1],"approach":0,"ar":0,"arg":0,"argument":0,"arrai":0,"artifact":1,"artifact_delta":[0,1],"artifact_servic":[0,1],"ask":0,"assess":0,"assign":0,"associ":0,"assum":0,"async":0,"async_exit_stack":0,"asyncclientarg":0,"asyncexitstack":0,"asyncgener":0,"asynchron":0,"attach":0,"attempt":0,"attribut":0,"audio":0,"audiotimestamp":0,"augment":0,"auth":0,"auth_cod":0,"auth_config":[0,1],"auth_credenti":0,"auth_help":0,"auth_provider_x509_cert_url":0,"auth_response_uri":0,"auth_schem":0,"auth_tool":0,"auth_typ":0,"auth_uri":0,"authconfig":0,"authcredenti":0,"authcredentialtyp":0,"authent":0,"author":[0,1],"authorization_endpoint":0,"authorizationcod":0,"authorizationurl":0,"authschem":0,"authtoolargu":[0,1],"auto":0,"autom":0,"automat":0,"automatic_function_calling_histori":0,"automaticfunctioncal":0,"automaticfunctioncallingconfig":0,"automod":0,"avail":0,"await":0,"back":0,"balanc":0,"base":0,"base64":0,"base64url":0,"base_ag":0,"base_url":[0,1],"baseag":[0,1],"baseartifactservic":[0,1],"basecodeexecutor":[0,1],"baseexampleprovid":[0,1],"basellm":[0,1],"basellmconnect":0,"basememoryservic":[0,1],"basemodel":0,"baseplann":[0,1],"baseretrievaltool":[0,1],"basesessionservic":[0,1],"basetool":[0,1],"baseurl":0,"basic":0,"bearer":0,"bearerformat":0,"been":0,"befor":0,"before_agent_callback":[0,1],"before_model_callback":[0,1],"before_tool_callback":[0,1],"beforeagentcallback":0,"beforemodelcallback":0,"beforetoolcallback":0,"begin":0,"behav":0,"behavior":0,"being":0,"belong":0,"benefici":0,"best":0,"better":0,"between":0,"bill":0,"blob":0,"block":0,"block_low_and_abov":0,"block_medium_and_abov":0,"block_non":0,"block_only_high":0,"bodi":0,"bool":0,"boolean":0,"both":0,"branch":[0,1],"break":0,"bucket":0,"bucket_nam":0,"budget":0,"build":0,"build_planning_instruct":[0,1],"builder":0,"built":0,"built_in_code_execut":0,"built_in_plann":0,"builtinplann":[0,1],"byte":0,"cach":0,"cachedcont":0,"call":[0,1],"callabl":0,"callback":0,"callback_context":0,"callbackcontext":0,"can":0,"cancel":0,"candid":0,"candidatecount":0,"cannot":0,"canonical_after_model_callback":[0,1],"canonical_before_model_callback":[0,1],"canonical_global_instruct":[0,1],"canonical_instruct":[0,1],"canonical_model":[0,1],"canonical_tool":[0,1],"capabl":0,"carri":0,"case":0,"categori":0,"central1":0,"cert":0,"chang":0,"char":0,"charact":0,"charg":0,"check":0,"chosen":0,"chunk":0,"citat":0,"claim":0,"class":0,"classmethod":0,"clear_input_fil":[0,1],"cli":0,"client":0,"client_email":0,"client_id":0,"client_secret":0,"client_x509_cert_url":0,"clientarg":0,"clientcredenti":0,"close":0,"close_sess":[0,1],"cloud":0,"code":0,"code_block_delimit":[0,1],"code_execution_input":0,"code_executor":1,"codeexecut":0,"codeexecutionresult":0,"codeexecutor":0,"codeexecutorcontext":[0,1],"collect":0,"com":0,"command":0,"commit":0,"common":0,"commun":0,"compar":0,"complet":0,"concis":0,"confid":0,"confidencescor":0,"config":0,"configur":0,"configure_auth_credenti":[0,1],"configure_auth_schem":[0,1],"connect":[0,1],"connection_host":0,"connection_nam":0,"connection_param":[0,1],"connection_service_nam":0,"connectionschemametadata":0,"connector":0,"consecut":0,"consid":0,"consol":0,"const":0,"constitut":0,"constrain":0,"construct":0,"contain":0,"containercodeexecutor":[0,1],"content":0,"context":0,"contextlib":0,"control":0,"convei":0,"conveni":0,"convent":0,"convers":0,"convert":0,"cooki":0,"coordin":0,"core":0,"corpora":0,"corpu":0,"correspond":0,"could":0,"count":0,"creat":0,"create_sess":[0,1],"createtim":0,"creativ":0,"credenti":0,"csv":0,"ctx":0,"current":0,"custom":0,"custom_metadata":0,"dash":0,"data":0,"data_store_id":[0,1],"databas":0,"databasesessionservic":[0,1],"dataset":0,"datastor":0,"datatyp":0,"date":0,"db_url":0,"decid":0,"declar":0,"def":0,"default":0,"defin":0,"definit":0,"degre":0,"deleg":0,"delet":0,"delete_artifact":[0,1],"delete_sess":[0,1],"delimit":0,"delta":0,"dens":0,"deprec":0,"deriv":0,"descend":0,"describ":0,"descript":[0,1],"detail":0,"determin":0,"develop":0,"dict":0,"dictionari":0,"didn":0,"differ":0,"direct":0,"directli":0,"directori":0,"disabl":0,"disableattribut":0,"disallow":0,"disallow_transfer_to_p":[0,1],"disallow_transfer_to_par":[0,1],"displai":0,"displaynam":0,"distanc":0,"divers":0,"do":0,"doc":0,"docker":0,"docker_path":[0,1],"dockerfil":0,"doe":0,"domain":0,"don":0,"dot":0,"doubl":0,"down":0,"download":0,"downloaduri":0,"dure":0,"dynam":0,"dynamicretrievalconfig":0,"dynamicretrievalconfigmod":0,"dynamicthreshold":0,"e":0,"each":0,"east":0,"effect":0,"effort":0,"either":0,"element":0,"email":0,"embed":0,"empti":0,"en":0,"enabl":0,"enclos":0,"encod":0,"encount":0,"end":0,"endindex":0,"endoffset":0,"endpoint":0,"enforc":0,"engin":0,"enough":0,"enter_async_context":0,"entir":0,"entiti":0,"entity_oper":0,"entityid1":0,"entityid2":0,"entri":0,"enum":0,"environ":0,"equal":0,"equival":0,"errlog":0,"error":0,"error_cod":0,"error_messag":0,"error_retry_attempt":[0,1],"escal":[0,1],"etc":0,"eval":0,"eval_dataset":0,"eval_dataset_file_path_or_dir":0,"evalu":1,"event":1,"event_act":[0,1],"eventact":[0,1],"exampl":1,"example_stor":0,"examples_store_nam":0,"examplestor":0,"examplesunion":0,"exampletool":[0,1],"except":0,"exchang":0,"exchange_credenti":0,"exchanged_auth_credenti":0,"exclud":0,"exclude_field":[0,1],"exclus":0,"execut":0,"executablecod":0,"execute_cod":[0,1],"execution_result_delimit":[0,1],"executor":0,"exist":0,"exit":0,"exit_loop":[0,1],"exit_stack":[0,1],"expect":0,"experiment":0,"expir":0,"expirationtim":0,"explor":0,"express":0,"extens":0,"extern":0,"extract":0,"eyakaknabna":0,"fail":0,"fals":0,"featur":0,"feature_selection_preference_unspecifi":0,"featureselectionprefer":0,"fetch":0,"few":0,"field":0,"file":0,"file_nam":0,"filedata":0,"filenam":0,"filesourc":0,"filesretriev":[0,1],"filest":0,"filestatu":0,"filesystem":0,"fileuri":0,"fill":0,"filter":0,"final":0,"find":0,"find_ag":[0,1],"find_config_for_test_fil":[0,1],"find_sub_ag":[0,1],"finish":0,"first":0,"fix":0,"flash":0,"float":0,"flow":0,"folder":0,"follow":0,"format":0,"found":0,"framework":0,"frequencypenalti":0,"from":0,"from_parsed_oper":[0,1],"from_parsed_operation_str":[0,1],"from_serv":[0,1],"full":0,"fulli":0,"func":[0,1],"function":0,"function_cal":0,"function_call_id":[0,1],"function_respons":0,"functioncal":0,"functioncallingconfig":0,"functioncallingconfigmod":0,"functiondeclar":0,"functionrespons":0,"functiontool":[0,1],"further":0,"futur":0,"g":0,"gc":0,"gcloud":0,"gcp":0,"gcsartifactservic":[0,1],"gemini":[0,1],"gemini_schema":0,"gemini_to_json_schema":[0,1],"genai":0,"gener":0,"generate_content_async":[0,1],"generate_content_config":[0,1],"generatecontentconfig":0,"generatecontentrespons":0,"generationconfigroutingconfig":0,"generationconfigroutingconfigautoroutingmod":0,"generationconfigroutingconfigmanualroutingmod":0,"get":[0,1],"get_auth_respons":[0,1],"get_error_count":[0,1],"get_exampl":[0,1],"get_execution_id":[0,1],"get_function_cal":[0,1],"get_function_respons":[0,1],"get_input_fil":[0,1],"get_processed_file_nam":[0,1],"get_sess":[0,1],"get_state_delta":[0,1],"get_tool":[0,1],"github":0,"given":0,"global":0,"global_instruct":[0,1],"go":0,"good":0,"googleapi":0,"googlesearch":0,"googlesearchdynamicretrievalscor":0,"googlesearchretriev":0,"grant":0,"grant_types_support":0,"ground":0,"grounding_chunk":0,"grounding_chunk_indic":0,"grounding_metadata":0,"groundingchunk":0,"groundingchunkindic":0,"groundingchunkretrievedcontext":0,"groundingchunkweb":0,"groundingmetadata":0,"groundingsupport":0,"gserviceaccount":0,"guid":0,"ha":0,"handl":0,"harm":0,"harm_block_method_unspecifi":0,"harm_block_threshold_unspecifi":0,"harm_category_civic_integr":0,"harm_category_dangerous_cont":0,"harm_category_harass":0,"harm_category_hate_speech":0,"harm_category_sexually_explicit":0,"harm_category_unspecifi":0,"harmblockmethod":0,"harmblockthreshold":0,"harmcategori":0,"has_delta":[0,1],"has_trailing_code_execution_result":[0,1],"hash":0,"hasn":0,"have":0,"header":0,"hello":0,"help":0,"helper":0,"here":0,"higher":0,"highest":0,"hint":0,"histori":0,"host":0,"hostnam":0,"how":0,"http":0,"httpauth":0,"httpbase":0,"httpbearer":0,"httpcredenti":0,"httpoption":0,"hub":0,"human":0,"hybrid":0,"hybridsearch":0,"i":0,"iam":0,"iana":0,"id":[0,1],"ident":0,"identifi":0,"ignor":0,"ignore_call_histori":0,"ignorecallhistori":0,"imag":[0,1],"immedi":0,"implement":0,"implicit":0,"import":0,"includ":0,"include_cont":[0,1],"includethought":0,"inclus":0,"incorpor":0,"increas":0,"increment":0,"increment_error_count":[0,1],"indefinit":0,"index":0,"indic":0,"inform":0,"ingest":0,"inherit":0,"initi":0,"initial_session_fil":0,"initialis":0,"inlin":0,"inlinedata":0,"inmemoryartifactservic":[0,1],"inmemorymemoryservic":[0,1],"inmemoryrunn":[0,1],"inmemorysessionservic":[0,1],"input":[0,1],"input_audio_transcript":0,"input_dir":0,"input_fil":0,"input_schema":[0,1],"instanc":0,"instead":0,"instruct":[0,1],"instructionprovid":0,"int":0,"int32":0,"int64":0,"integ":0,"integr":0,"integrationconnectortool":[0,1],"intend":0,"interact":0,"interfac":0,"intern":0,"interpret":0,"interrupt":0,"invalid":0,"invoc":0,"invocation_context":[0,1],"invocation_id":[0,1],"invocationcontext":0,"invok":0,"ip":0,"is_final_respons":[0,1],"is_long_run":[0,1],"iso":0,"isol":0,"item":0,"iter":0,"its":0,"json":0,"just":0,"k":0,"kei":0,"keyword":0,"kit":0,"know":0,"knowledg":0,"kwarg":0,"label":0,"languag":0,"language_unspecifi":0,"languagecod":0,"larger":0,"last":0,"last_update_tim":[0,1],"later":0,"latest":0,"lazili":0,"lazy_load_spec":0,"lead":0,"least":0,"left":0,"length":0,"less":0,"letter":0,"level":0,"lifecycl":0,"lifetim":0,"lightweight":0,"like":0,"line":0,"list":0,"list_artifact":[0,1],"list_artifact_kei":[0,1],"list_ev":[0,1],"list_sess":[0,1],"list_vers":[0,1],"listeventsrespons":0,"listsessionsrespons":0,"liter":0,"live":0,"live_request_queu":0,"llamaindexretriev":[0,1],"llm":0,"llm_cl":0,"llm_request":0,"llm_respons":0,"llmagent":[0,1],"llmranker":0,"llmregistri":[0,1],"llmrequest":0,"llmrespons":0,"load":0,"load_artifact":[0,1],"load_tool":[0,1],"local":0,"locat":0,"log":0,"logic":0,"logprob":0,"long":0,"long_running_tool_id":[0,1],"longer":0,"longrunningfunctiontool":[0,1],"look":0,"loop":0,"loopag":[0,1],"lower":0,"lowercas":0,"mai":0,"main":0,"mainli":0,"maintain":0,"make":0,"manag":0,"manner":0,"manual":0,"manualmod":0,"map":0,"match":0,"max":0,"max_iter":[0,1],"max_llm_cal":0,"maximum":0,"maximumremotecal":0,"maxitem":0,"maxlength":0,"maxoutputtoken":0,"maxproperti":0,"mcp":0,"mcp_session":0,"mcp_session_manag":0,"mcp_tool":0,"mcptool":[0,1],"mcptoolset":[0,1],"md":0,"mean":0,"meant":0,"measur":0,"media":0,"media_resolution_high":0,"media_resolution_low":0,"media_resolution_medium":0,"media_resolution_unspecifi":0,"mediaresolut":0,"memori":1,"memory_servic":[0,1],"merg":0,"messag":0,"metadata":0,"metadatafilt":0,"method":0,"millisecond":0,"mime":0,"mimetyp":0,"minimum":0,"minitem":0,"minlength":0,"minproperti":0,"modal":0,"mode":0,"mode_dynam":0,"mode_unspecifi":0,"model":1,"model_construct":0,"model_post_init":[0,1],"modelcontextprotocol":0,"modelnam":0,"modelroutingprefer":0,"modelselectionconfig":0,"modul":1,"more":0,"most":0,"multi":0,"multimod":0,"multipl":0,"must":0,"mutabl":0,"mutat":0,"mutual":0,"my_tool":0,"n":0,"nadk":0,"nalwai":0,"name":[0,1],"nand":0,"nattribut":0,"nauthcredenti":0,"nbe":0,"ncode":0,"ncredentialexchang":0,"ndatastor":0,"need":0,"neither":0,"new":0,"new_id":[0,1],"new_llm":[0,1],"new_messag":0,"newli":0,"nexactli":0,"nexampl":0,"next":0,"nfor":0,"ngener":0,"nhttp":0,"ninclud":0,"ninstanc":0,"nit":0,"nobject":0,"nof":0,"non":0,"none":0,"nor":0,"north":0,"note":0,"npx":0,"nrepres":0,"nrequest":0,"nsee":0,"nspecifi":0,"ntaken":0,"nthe":0,"nthi":0,"nto":0,"null":0,"nullabl":0,"num_run":0,"number":0,"o":0,"oa":0,"oai":0,"oauth":0,"oauth2":0,"oauth2auth":0,"oauthflow":0,"oauthflowauthorizationcod":0,"oauthflowclientcredenti":0,"oauthflowimplicit":0,"oauthflowpassword":0,"object":0,"observ":0,"off":0,"offset":0,"ok":0,"onc":0,"one":0,"onli":0,"open":0,"open_id_connect":0,"openapi":0,"openapi_spec_dict":0,"openapi_spec_str":0,"openapi_tool":0,"openapi_toolset":0,"openapitoolset":[0,1],"openid":0,"openidconnect":0,"openidconnecturl":0,"openidconnectwithconfig":0,"oper":0,"operationgener":0,"operationid":0,"optim":0,"optimize_data_fil":[0,1],"option":0,"optional_field":[0,1],"order":0,"org":0,"origin":0,"other":0,"otherwis":0,"out":0,"outcom":0,"outcome_deadline_exceed":0,"outcome_fail":0,"outcome_ok":0,"outcome_unspecifi":0,"outgo":0,"output":[0,1],"output_audio_transcript":0,"output_kei":[0,1],"output_schema":[0,1],"outsid":0,"overrid":0,"own":0,"packag":1,"page":0,"page_s":0,"page_token":0,"parallel":0,"parallelag":[0,1],"param":0,"param1":0,"param2":0,"paramet":0,"parent":0,"parent_ag":[0,1],"parent_context":0,"pars":0,"parsed_operation_str":0,"parsedoper":0,"part":0,"partial":0,"partindex":0,"pass":0,"password":0,"path":0,"pattern":0,"peer":0,"penal":0,"pend":0,"perform":0,"persist":0,"person":0,"perspect":0,"place":0,"plan":0,"planner":1,"planreactplann":[0,1],"platform":0,"pleas":0,"point":0,"popul":0,"posit":0,"possibl":0,"post":0,"power":0,"prebuilt":0,"prebuiltvoiceconfig":0,"predefin":0,"predict":0,"predictor":0,"prefer":0,"prefix":0,"prefixitem":0,"prepar":0,"preprocess":0,"presencepenalti":0,"present":0,"pretrain":0,"primit":0,"print":0,"prioritize_cost":0,"prioritize_qu":0,"privat":0,"private_kei":0,"private_key_id":0,"pro":0,"probabl":0,"process":0,"process_llm_request":[0,1],"process_planning_respons":[0,1],"produc":0,"product":0,"program":0,"project":0,"project_id":0,"prompt":0,"properti":0,"propertyord":0,"protocol":0,"prototyp":0,"provid":0,"public":0,"purpos":0,"pydant":0,"python":0,"python3":0,"qualifi":0,"queri":0,"question":0,"queue":0,"rag":0,"rag_corpora":0,"rag_corpu":0,"rag_corpus_id":0,"rag_file_id":0,"rag_resourc":0,"ragcorpora":0,"ragcorpu":0,"ragfil":0,"ragfileid":0,"ragresourc":0,"ragretrievalconfig":0,"ragretrievalconfigfilt":0,"ragretrievalconfighybridsearch":0,"ragretrievalconfigrank":0,"ragretrievalconfigrankingllmrank":0,"ragretrievalconfigrankingrankservic":0,"rais":0,"random":0,"rang":0,"rank":0,"ranker":0,"rankservic":0,"raw":0,"raw_auth_credenti":0,"re":0,"reach":0,"readabl":0,"readonli":0,"readonly_context":0,"recurs":0,"redirect":0,"redirect_uri":0,"ref":0,"refer":0,"reflect":0,"refresh":0,"refresh_token":0,"refreshurl":0,"regex":0,"regist":[0,1],"registri":0,"regular":0,"relat":0,"releas":0,"remot":0,"remov":0,"renderedcont":0,"repeat":0,"repeatedli":0,"repli":0,"repres":0,"represent":0,"request":0,"request_credenti":[0,1],"requested_auth_config":[0,1],"requir":0,"rerank":0,"reserv":0,"reset":0,"reset_error_count":[0,1],"resolut":0,"resolv":[0,1],"resourc":0,"resource1":0,"resource_nam":[0,1],"resource_ref":0,"respons":0,"response_mod":0,"response_part":0,"responselogprob":0,"responsemimetyp":0,"responsemod":0,"responseschema":0,"rest":0,"rest_api_tool":0,"restapitool":[0,1],"restrict":0,"result":0,"result_stderr":0,"result_stdout":0,"retri":0,"retriev":0,"retrieval_queri":0,"retrievalmetadata":0,"retrievalqueri":0,"retrievedcontext":0,"return":0,"review":0,"revis":0,"revoc":0,"revocation_endpoint":0,"robot":0,"role":0,"root":0,"root_ag":[0,1],"rout":0,"router":0,"routingconfig":0,"rtype":0,"run":[0,1],"run_async":[0,1],"run_config":0,"run_liv":[0,1],"runconfig":0,"runner":1,"safeti":0,"safetyset":0,"same":0,"sampl":0,"save":0,"save_artifact":[0,1],"save_input_blobs_as_artifact":0,"scenario":0,"schedul":0,"schema":0,"scheme":0,"scope":0,"scope1":0,"scope2":0,"score":0,"sdk":0,"sdkblob":0,"search":0,"search_engine_id":[0,1],"search_memori":[0,1],"searchentrypoint":0,"searchmemoryrespons":0,"secret":0,"secur":0,"securityschem":0,"securityschemetyp":0,"see":0,"seed":0,"segment":0,"select":0,"self":0,"semant":0,"send":0,"sensit":0,"sent":0,"sequenc":0,"sequentialag":[0,1],"seri":0,"server":0,"servic":0,"service_account":0,"service_account_credenti":0,"service_account_json":0,"service_nam":0,"serviceaccount":0,"serviceaccountconfig":0,"serviceaccountcredenti":0,"session":1,"session_ev":[0,1],"session_id":0,"session_servic":[0,1],"session_st":0,"set":0,"set_execution_id":[0,1],"sever":0,"sha":0,"sha256hash":0,"share":0,"shell":0,"shot":0,"should":0,"should_parse_oper":0,"shouldn":0,"show":0,"shut":0,"side":0,"signatur":0,"signific":0,"similar":0,"similarity_top_k":0,"similaritytopk":0,"simultan":0,"sinc":0,"singl":0,"size":0,"sizebyt":0,"skip":0,"skip_summar":[0,1],"smaller":0,"snake":0,"snippet":0,"so":0,"some":0,"sourc":0,"source_unspecifi":0,"south":0,"space":0,"spars":0,"speaker":0,"spec":0,"spec_dict":0,"spec_str":0,"spec_str_typ":0,"special":0,"specif":0,"specifi":0,"speech":0,"speech_config":0,"speechconfig":0,"sse":0,"sseserverparam":0,"stabl":0,"stack":0,"standard":0,"start":0,"startindex":0,"startoffset":0,"state":[0,1],"state_delta":[0,1],"state_unspecifi":0,"static":0,"statu":0,"stderr":0,"stdioserverparamet":0,"stdout":0,"steer":0,"step":0,"stop":0,"stopsequ":0,"storag":0,"store":0,"str":0,"stream":0,"streaming_mod":0,"streamingmod":0,"string":0,"structur":0,"sub":0,"sub_ag":[0,1],"subclass":0,"submodul":1,"subschema":0,"subsequ":0,"subset":0,"success":0,"suffix":0,"sum":0,"summar":0,"summari":0,"support":0,"support_cfc":0,"supported_model":[0,1],"sync":0,"synthes":0,"system":0,"systeminstruct":0,"t":0,"tag":0,"take":0,"taken":0,"target":0,"task":0,"technic":0,"tell":0,"temp":0,"temp_prefix":[0,1],"temperatur":0,"term":0,"test":0,"test_config":0,"test_fil":0,"test_trigg":0,"text":0,"textiowrapp":0,"than":0,"thei":0,"them":0,"thi":0,"think":0,"thinking_config":[0,1],"thinkingbudget":0,"thinkingconfig":0,"thought":0,"threshold":0,"through":0,"time":0,"timeout":0,"timestamp":[0,1],"titl":0,"to_dict":[0,1],"token":0,"token_endpoint":0,"token_endpoint_auth_methods_support":0,"token_uri":0,"tokenurl":0,"tool":1,"tool_cod":0,"tool_context":0,"tool_instruct":0,"tool_nam":0,"tool_output":0,"tool_respons":0,"toolcodeexecut":0,"toolconfig":0,"toolcontext":[0,1],"toolset":0,"toolunion":0,"top":0,"top_k":0,"top_p":0,"topk":0,"topp":0,"toward":0,"trail":0,"transfer":0,"transfer_to_ag":[0,1],"transform":0,"treat":0,"tree":0,"trigger":0,"true":0,"tupl":0,"turn":0,"turn_complet":0,"twice":0,"two":0,"type":0,"type_":0,"type_unspecifi":0,"typeerror":0,"typic":0,"u":0,"underli":0,"underscor":0,"union":0,"uniqu":0,"uniqueitem":0,"univers":0,"universe_domain":0,"unknown":0,"unsaf":0,"unsafelocalcodeexecutor":[0,1],"unset":0,"until":0,"up":0,"updat":[0,1],"update_code_execution_result":[0,1],"updatetim":0,"upload":0,"upper":0,"uri":0,"url":0,"us":0,"usabl":0,"usag":0,"use_default_credenti":0,"user":0,"user_id":[0,1],"user_prefix":[0,1],"userinfo":0,"userinfo_endpoint":0,"usernam":0,"usual":0,"utf":0,"v1":0,"v3":0,"valid":0,"valu":0,"valueerror":0,"variat":0,"variou":0,"vector":0,"vector_distance_threshold":0,"vectordistancethreshold":0,"vectorsimilaritythreshold":0,"version":0,"vertex":0,"vertexaicodeexecutor":[0,1],"vertexaiexamplestor":[0,1],"vertexairagmemoryservic":[0,1],"vertexairagretriev":[0,1],"vertexaisearch":0,"vertexaisearchtool":[0,1],"vertexaisessionservic":[0,1],"vertexragdataservic":0,"vertexragstor":0,"vertexragstoreragresourc":0,"via":0,"video":0,"videometadata":0,"voic":0,"voiceconfig":0,"voicenam":0,"w":0,"wa":0,"want":0,"we":0,"web":0,"websearchqueri":0,"webview":0,"weight":0,"welcom":0,"well":0,"were":0,"west":0,"what":0,"when":0,"where":0,"whether":0,"which":0,"while":0,"who":0,"whole":0,"within":0,"won":0,"wrap":0,"www":0,"x":0,"x509":0,"xxx":0,"y":0,"yaml":0,"yield":0,"you":0,"your":0,"your_client_id":0,"your_private_key_id":0,"your_project_id":0,"z":0,"zero":0},"titles":["Submodules","google"],"titleterms":{"adk":0,"agent":0,"artifact":0,"chang":[],"code_executor":0,"evalu":0,"event":0,"exampl":0,"from":[],"googl":[0,1],"memori":0,"model":0,"modul":0,"packag":0,"planner":0,"runner":0,"session":0,"submodul":0,"tool":0}})