Thank you for your interest in contributing to the Agent Development Kit (ADK)! We welcome contributions to both the core framework (Python and Java) and its documentation.

This guide provides information on how to get involved.

## 1. [`google/adk-python`](https://github.com/google/adk-python)

Contains the core Python library source code.

## 2. [`google/adk-java`](https://github.com/google/adk-java)

Contains the core Java library source code.

## 3. [`google/adk-docs`](https://github.com/google/adk-docs)

Contains the source for the documentation site you are currently reading.

## 4. [`google/adk-web`](https://github.com/google/adk-web)

Contains the source for the `adk web` dev UI.

## Before you begin

### ✏️ Sign our Contributor License Agreement

Contributions to this project must be accompanied by a
[Contributor License Agreement](https://cla.developers.google.com/about) (CLA).
You (or your employer) retain the copyright to your contribution; this simply
gives us permission to use and redistribute your contributions as part of the
project.

If you or your current employer have already signed the Google CLA (even if it
was for a different project), you probably don't need to do it again.

Visit <https://cla.developers.google.com/> to see your current agreements or to
sign a new one.

### 📜 Review our community guidelines

This project follows
[Google's Open Source Community Guidelines](https://opensource.google/conduct/).

## 💬 Join the Discussion!

Have questions, want to share ideas, or discuss how you're using the ADK? Head over to our **[Python](https://github.com/google/adk-python/discussions)** or **[Java](https://github.com/google/adk-java/discussions)** Discussions!

This is the primary place for:

* Asking questions and getting help from the community and maintainers.
* Sharing your projects or use cases (`Show and Tell`).
* Discussing potential features or improvements before creating a formal issue.
* General conversation about the ADK.

## How to Contribute

There are several ways you can contribute to the ADK:

### 1. Reporting Issues (Bugs & Errors)

If you find a bug in the framework or an error in the documentation:

* **Framework Bugs:** Open an issue in [`google/adk-python`](https://github.com/google/adk-python/issues/new) or in [`google/adk-java`](https://github.com/google/adk-java/issues/new)
* **Documentation Errors:** [Open an issue in `google/adk-docs` (use bug template)](https://github.com/google/adk-docs/issues/new?template=bug_report.md)

### 2. Suggesting Enhancements

Have an idea for a new feature or an improvement to an existing one?

* **Framework Enhancements:** Open an issue in [`google/adk-python`](https://github.com/google/adk-python/issues/new) or in [`google/adk-java`](https://github.com/google/adk-java/issues/new)
* **Documentation Enhancements:** [Open an issue in `google/adk-docs`](https://github.com/google/adk-docs/issues/new)

### 3. Improving Documentation

Found a typo, unclear explanation, or missing information? Submit your changes directly:

* **How:** Submit a Pull Request (PR) with your suggested improvements.
* **Where:** [Create a Pull Request in `google/adk-docs`](https://github.com/google/adk-docs/pulls)

### 4. Writing Code

Help fix bugs, implement new features or contribute code samples for the documentation:

**How:** Submit a Pull Request (PR) with your code changes.

* **Python Framework:** [Create a Pull Request in `google/adk-python`](https://github.com/google/adk-python/pulls)
* **Java Framework:** [Create a Pull Request in `google/adk-java`](https://github.com/google/adk-java/pulls)
* **Documentation:** [Create a Pull Request in `google/adk-docs`](https://github.com/google/adk-docs/pulls)

### Code Reviews

* All contributions, including those from project members, undergo a review process.

* We use GitHub Pull Requests (PRs) for code submission and review. Please ensure your PR clearly describes the changes you are making.

## License

By contributing, you agree that your contributions will be licensed under the project's [Apache 2.0 License](https://github.com/google/adk-docs/blob/main/LICENSE).

## Questions?

If you get stuck or have questions, feel free to open an issue on the relevant repository's issue tracker.
