# Get Started

Agent Development Kit (ADK) is designed to empower developers
to build, manage, evaluate and deploy AI-powered agents. It provides a robust
and flexible environment for creating both conversational and non-conversational
agents, capable of handling complex tasks and workflows.

<div class="grid cards" markdown>

-   :material-console-line: **Installation**

    ---

    Install `google-adk` for Python or Java and get up and running in minutes.

    [:octicons-arrow-right-24: More information](installation.md)

-   :material-console-line: **Quickstart**

    ---

    Create your first ADK agent with tools in minutes.

    [:octicons-arrow-right-24: More information](quickstart.md)

-   :material-console-line: **Quickstart (streaming)**

    ---

    Create your first streaming ADK agent.

    [:octicons-arrow-right-24: More information](streaming/quickstart-streaming.md)

-   :material-console-line: **Tutorial**

    ---

    Create your first ADK multi-agent.

    [:octicons-arrow-right-24: More information](../tutorials/index.md)

-   :material-rocket-launch-outline: **Discover sample agents**

    ---

    Discover sample agents for retail, travel, customer service, and more!

    [:octicons-arrow-right-24: Discover adk-samples](https://github.com/google/adk-samples){:target="_blank"}

-   :material-graph: **About**

    ---

    Learn about the key components of building and deploying ADK agents.

    [:octicons-arrow-right-24: More information](about.md)

</div>
