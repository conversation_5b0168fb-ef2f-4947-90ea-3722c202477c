/**
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* Index page styling */

.md-grid { max-width: 80%; }
.footer { padding-bottom: 30vh; }
.centered-logo-text-group { display: inline-flex; align-items: center; gap: 1.5em; margin-bottom: 0.5em; vertical-align: middle; }
.centered-logo-text-group img { height: auto; }
.centered-logo-text-group h1 { margin: 0; text-align: left; }
.install-command-container { max-width: 600px; margin: 2.5em auto; padding: 1.5em 2em; background-color: var(--md-code-bg-color, #f5f5f5); border-radius: 8px; box-shadow: 0 3px 6px rgba(0,0,0,0.05); border-left: 5px solid var(--md-primary-fg-color, #526cfe); margin-top: 30px; }
.install-command-container p { font-size: 1.1em; color: var(--md-default-fg-color); margin-bottom: -10px; margin-top: -10px; }
.install-command-container p code { font-size: 1.1em; font-weight: 600; padding: 0.3em 0.6em; background-color: var(--md-code-fg-color--light); text-align: center; border-radius: 4px; display: inline-block; line-height: 1.4; }
#centered-install-tabs .tabbed-labels.tabbed-labels--linked { display: flex; justify-content: center; width: 100%; } /* Centered install tabs in the homepage*/
#centered-install-tabs .tabbed-labels { display: flex; justify-content: center; width: 100%; } /* Centered install tabs in the homepage*/

/* Youtube Video Grid Layout - Community page*/
.video-grid { display: flex; flex-wrap: wrap; gap: 20px; margin-top: 1.5em; margin-bottom: 1.5em; }
/* For 3 items per row: 100% / 3 = 33.333% */
.video-item { flex: 0 1 calc(33.333% - 13.333px); box-sizing: border-box; min-width: 250px; }
.video-container { position: relative; padding-bottom: 56.25%; height: 0; overflow: hidden; max-width: 100%; background: #f0f0f0; border-radius: 4px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
.video-container iframe { position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: 0; }
@media (max-width: 992px) {  .video-item { flex: 0 1 calc(50% - 10px); }  }
@media (max-width: 600px) {  .video-item { flex: 0 1 100%; }  }
/* Youtube icon */
.youtube-red-icon svg { fill: #FF0000 !important; }

/* Custom styles for handling two repository links */
.md-header__source {
  max-width: 480px;
  display: flex;
  align-items: center;
}

/* Hide repository stats but keep repository names visible */
.md-source__facts {
  display: none !important;
}

/* Ensure repository names are fully visible */
.md-source__repository {
  white-space: nowrap;
  max-width: none !important;
  overflow: visible !important;
}