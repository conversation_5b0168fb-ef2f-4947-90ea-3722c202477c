# ADK Tutorials!

Get started with the Agent Development Kit (ADK) through our collection of
practical guides. These tutorials are designed in a simple, progressive,
step-by-step fashion, introducing you to different ADK features and
capabilities.

This approach allows you to learn and build incrementally – starting with
foundational concepts and gradually tackling more advanced agent development
techniques. You'll explore how to apply these features effectively across
various use cases, equipping you to build your own sophisticated agentic
applications with ADK. Explore our collection below and happy building:

<div class="grid cards" markdown>

-   :material-console-line: **Agent Team**

    ---

    Learn to build an intelligent multi-agent weather bot and master key ADK
    features: defining Tools, using multiple LLMs (Gemini, GPT, Claude) with
    LiteLLM, orchestrating agent delegation, adding memory with session state,
    and ensuring safety via callbacks.

    [:octicons-arrow-right-24: Start learning here](agent-team.md)

</div>
