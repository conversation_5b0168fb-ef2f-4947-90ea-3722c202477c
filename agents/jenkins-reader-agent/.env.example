# Jenkins Reader Agent Configuration
# Copy this file to .env and configure your settings

# Google Cloud Configuration
GOOGLE_GENAI_USE_VERTEXAI=1
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1

# Authentication
# Path to service account key file (mount as Docker volume in production)
GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json
SERVICE_ACCOUNT_KEY_PATH=/app/service-account-key.json

# Jenkins Configuration
JENKINS_URL=https://your-jenkins-server.com
JENKINS_USERNAME=your-username
JENKINS_API_TOKEN=your-api-token

# Model Configuration
GEMINI_PRO_MODEL=gemini-2.5-pro-preview-05-06
GEMINI_FLASH_MODEL=gemini-2.5-flash-preview-05-20

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# Security Configuration
ALLOWED_JENKINS_DOMAINS=your-jenkins-domain.com

# Rate Limiting
RATE_LIMIT_PER_MINUTE=100
MAX_CONCURRENT_REQUESTS=10

# Development Configuration
DEBUG=false
TESTING=false

# Optional: Override default settings
# TEMPERATURE=0.1
# MAX_TOKENS=8192
