# Contributing to AI-Analyst

Thank you for your interest in contributing to AI-Analyst! This document provides guidelines for contributing to this multi-agent platform.

## Development Setup

### Prerequisites

- Python 3.12+
- Docker and Docker Compose
- Google Cloud SDK
- Git

### Environment Setup

1. **Clone the repository:**
   ```bash
   <NAME_EMAIL>:truxt-ai/AI-Analyst.git
   cd AI-Analyst
   ```

2. **Set up Python environment:**
   ```bash
   cd agents/jenkins-reader-agent
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

## Adding New Agents

### Agent Structure

Each agent should follow this structure:
```
agents/your-agent-name/
├── your_agent/
│   ├── __init__.py
│   ├── agent.py              # Main agent with root_agent
│   ├── prompts.py           # System instructions
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py      # Configuration management
│   │   └── schemas.py       # Data models
│   ├── tools/
│   │   ├── __init__.py
│   │   └── *.py            # Agent-specific tools
│   └── utils/
│       ├── __init__.py
│       ├── auth.py         # Authentication utilities
│       ├── logging.py      # Logging configuration
│       └── validation.py   # Input validation
├── docs/
│   ├── setup.md
│   ├── design.md
│   └── api.md
├── tests/
├── Dockerfile.adk
├── docker-compose.yml
├── requirements.txt
├── .env.example
└── README.md
```

### Agent Requirements

1. **Security First:**
   - Implement read-only operations only
   - Use service account authentication
   - Add comprehensive audit logging
   - Validate all inputs
   - Sanitize all outputs

2. **ADK Integration:**
   - Define `root_agent` in `agent.py`
   - Use ADK's Agent class
   - Implement proper tool registration
   - Follow ADK naming conventions

3. **Error Handling:**
   - Graceful error handling
   - Meaningful error messages
   - Proper logging
   - Fallback mechanisms

4. **Documentation:**
   - Comprehensive README
   - API documentation
   - Setup instructions
   - Usage examples

## Code Standards

### Python Style

- Follow PEP 8
- Use type hints
- Add docstrings for all functions and classes
- Maximum line length: 88 characters (Black formatter)

### Security Guidelines

- Never commit credentials or secrets
- Use environment variables for configuration
- Implement proper input validation
- Add rate limiting where appropriate
- Log security-relevant events

### Testing

- Write unit tests for all functions
- Add integration tests for tools
- Test error conditions
- Maintain >80% code coverage

## Pull Request Process

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes:**
   - Follow coding standards
   - Add tests
   - Update documentation

3. **Test your changes:**
   ```bash
   # Run tests
   pytest tests/
   
   # Test with ADK
   adk run your_agent
   
   # Test Docker build
   docker build -f Dockerfile.adk -t your-agent:latest .
   ```

4. **Commit your changes:**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

5. **Push and create PR:**
   ```bash
   git push origin feature/your-feature-name
   ```

## Commit Message Format

Use conventional commits:
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Test additions/changes
- `chore:` - Maintenance tasks

## Review Process

1. All PRs require review
2. Tests must pass
3. Documentation must be updated
4. Security review for new agents
5. Performance impact assessment

## Getting Help

- Create an issue for bugs or feature requests
- Join our development discussions
- Review existing documentation
- Check the ADK documentation

## License

By contributing, you agree that your contributions will be licensed under the same license as the project.
