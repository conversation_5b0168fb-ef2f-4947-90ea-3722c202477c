# Deployment Guide

This guide covers deployment options for AI-Analyst agents in various environments.

## Prerequisites

- <PERSON><PERSON> and Docker Compose
- Google Cloud Project with Vertex AI enabled
- Service Account with appropriate permissions
- Jenkins server access (for Jenkins Reader Agent)

## Local Development

### Single Agent Deployment

1. **Navigate to agent directory:**
   ```bash
   cd agents/jenkins-reader-agent
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Run with Docker:**
   ```bash
   docker build -f Dockerfile.adk -t jenkins-reader-agent:latest .
   docker run -p 8000:8000 \
     -v $(pwd)/service-account-key.json:/app/service-account-key.json:ro \
     jenkins-reader-agent:latest
   ```

4. **Access the interface:**
   ```
   http://localhost:8000
   ```

### Using Docker Compose

```bash
cd agents/jenkins-reader-agent
docker-compose -f docker-compose.prod.yml up
```

## Production Deployment

### Google Cloud Run

1. **Build and push image:**
   ```bash
   # Build image
   docker build -f Dockerfile.adk -t gcr.io/PROJECT_ID/jenkins-reader-agent:latest .
   
   # Push to Container Registry
   docker push gcr.io/PROJECT_ID/jenkins-reader-agent:latest
   ```

2. **Deploy to Cloud Run:**
   ```bash
   gcloud run deploy jenkins-reader-agent \
     --image gcr.io/PROJECT_ID/jenkins-reader-agent:latest \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated \
     --port 8000 \
     --memory 2Gi \
     --cpu 1 \
     --set-env-vars GOOGLE_CLOUD_PROJECT=PROJECT_ID \
     --set-env-vars GOOGLE_CLOUD_LOCATION=us-central1
   ```

### Kubernetes

1. **Create namespace:**
   ```bash
   kubectl create namespace ai-analyst
   ```

2. **Create secret for service account:**
   ```bash
   kubectl create secret generic gcp-credentials \
     --from-file=service-account-key.json \
     --namespace ai-analyst
   ```

3. **Deploy agent:**
   ```yaml
   # deployment.yaml
   apiVersion: apps/v1
   kind: Deployment
   metadata:
     name: jenkins-reader-agent
     namespace: ai-analyst
   spec:
     replicas: 2
     selector:
       matchLabels:
         app: jenkins-reader-agent
     template:
       metadata:
         labels:
           app: jenkins-reader-agent
       spec:
         containers:
         - name: jenkins-reader-agent
           image: gcr.io/PROJECT_ID/jenkins-reader-agent:latest
           ports:
           - containerPort: 8000
           env:
           - name: GOOGLE_APPLICATION_CREDENTIALS
             value: /app/service-account-key.json
           - name: GOOGLE_CLOUD_PROJECT
             value: PROJECT_ID
           - name: GOOGLE_CLOUD_LOCATION
             value: us-central1
           volumeMounts:
           - name: gcp-credentials
             mountPath: /app/service-account-key.json
             subPath: service-account-key.json
             readOnly: true
           resources:
             requests:
               memory: "1Gi"
               cpu: "500m"
             limits:
               memory: "2Gi"
               cpu: "1000m"
         volumes:
         - name: gcp-credentials
           secret:
             secretName: gcp-credentials
   ---
   apiVersion: v1
   kind: Service
   metadata:
     name: jenkins-reader-agent-service
     namespace: ai-analyst
   spec:
     selector:
       app: jenkins-reader-agent
     ports:
     - port: 80
       targetPort: 8000
     type: LoadBalancer
   ```

   ```bash
   kubectl apply -f deployment.yaml
   ```

## Environment Configuration

### Required Environment Variables

```bash
# Google Cloud Configuration
GOOGLE_GENAI_USE_VERTEXAI=1
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
GOOGLE_APPLICATION_CREDENTIALS=/app/service-account-key.json

# Jenkins Configuration
JENKINS_URL=https://your-jenkins-server.com
JENKINS_USERNAME=your-username
JENKINS_API_TOKEN=your-api-token

# Model Configuration
GEMINI_PRO_MODEL=gemini-2.5-pro-preview-05-06
GEMINI_FLASH_MODEL=gemini-2.5-flash-preview-05-20

# Security Configuration
ALLOWED_JENKINS_DOMAINS=your-jenkins-domain.com
```

### Service Account Permissions

Your service account needs these IAM roles:
- `roles/aiplatform.user` - For Vertex AI access
- `roles/logging.logWriter` - For Cloud Logging
- `roles/monitoring.metricWriter` - For Cloud Monitoring

## Security Considerations

### Network Security

- Use private networks where possible
- Implement proper firewall rules
- Use HTTPS/TLS for all communications
- Restrict access to management interfaces

### Secrets Management

- Use Google Secret Manager for production
- Never commit credentials to version control
- Rotate credentials regularly
- Use least privilege principle

### Monitoring and Logging

- Enable Cloud Logging
- Set up monitoring and alerting
- Monitor resource usage
- Track security events

## Scaling

### Horizontal Scaling

- Use multiple replicas in Kubernetes
- Implement load balancing
- Consider regional deployments

### Vertical Scaling

- Monitor resource usage
- Adjust CPU and memory limits
- Use autoscaling where available

## Troubleshooting

### Common Issues

1. **Authentication Errors:**
   - Verify service account key
   - Check IAM permissions
   - Validate project ID

2. **Connection Issues:**
   - Check network connectivity
   - Verify firewall rules
   - Test DNS resolution

3. **Performance Issues:**
   - Monitor resource usage
   - Check rate limits
   - Optimize queries

### Health Checks

```bash
# Check agent health
curl http://localhost:8000/

# Check logs
docker logs container_name

# Check Kubernetes pods
kubectl get pods -n ai-analyst
kubectl logs -f deployment/jenkins-reader-agent -n ai-analyst
```

## Backup and Recovery

- Backup configuration files
- Document deployment procedures
- Test recovery procedures
- Maintain deployment scripts

## Updates and Maintenance

1. **Update process:**
   - Test in development
   - Deploy to staging
   - Gradual production rollout
   - Monitor for issues

2. **Maintenance windows:**
   - Schedule regular updates
   - Communicate with users
   - Have rollback plan ready
