# Jenkins Read-Only Agent Design Document

## Executive Summary

This document outlines the design and implementation plan for a Jenkins Read-Only Agent using Google's Agent Development Kit (ADK). The agent will provide comprehensive, secure, and scalable access to Jenkins data while maintaining enterprise-grade security standards.

## 1. Architecture Overview

### 1.1 High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Google Cloud Platform                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   IAM & RBAC    │  │  Secret Manager │  │   Cloud Logging │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Jenkins Read-Only Agent                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ Main Agent  │  │ Sub-Agents  │  │   Tool Framework    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Jenkins API Integration Layer                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ Auth Module │  │ API Client  │  │  Data Validation    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Jenkins Server                             │
│  ┌─────────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │      Jobs       │  │   Builds    │  │     Artifacts       │ │
│  └─────────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 Core Components

1. **Main Jenkins Agent**: Orchestrates all Jenkins operations
2. **Sub-Agents**: Specialized agents for different Jenkins data types
3. **Tool Framework**: Google function calling tools for Jenkins API
4. **Security Layer**: IAM, RBAC, and credential management
5. **Monitoring & Logging**: Comprehensive observability

## 2. Component Design

### 2.1 Main Agent Structure

```python
jenkins_agent = Agent(
    model="gemini-2.5-pro",
    name="jenkins_read_only_agent",
    instruction=jenkins_system_instructions(),
    global_instruction=jenkins_global_context(),
    sub_agents=[
        job_analyzer_agent,
        build_history_agent,
        pipeline_analyzer_agent,
        artifact_manager_agent
    ],
    tools=[
        get_jenkins_jobs,
        get_job_config,
        get_build_history,
        get_pipeline_info,
        get_artifacts,
        validate_jenkins_connection
    ],
    before_agent_callback=security_validation_callback,
    after_agent_callback=audit_logging_callback,
    generate_content_config=GenerateContentConfig(
        temperature=0.1,
        response_schema=jenkins_response_schema
    )
)
```

### 2.2 Sub-Agents Architecture

#### 2.2.1 Job Analyzer Agent
- **Purpose**: Analyze Jenkins job configurations and relationships
- **Capabilities**: 
  - Job configuration parsing
  - Upstream/downstream dependency mapping
  - Parameter analysis
  - Trigger configuration analysis

#### 2.2.2 Build History Agent
- **Purpose**: Analyze build history and trends
- **Capabilities**:
  - Build status analysis
  - Performance metrics
  - Failure pattern detection
  - Build artifact tracking

#### 2.2.3 Pipeline Analyzer Agent
- **Purpose**: Analyze Jenkins pipelines and workflows
- **Capabilities**:
  - Pipeline stage analysis
  - Groovy script parsing
  - Shared library usage
  - Pipeline performance metrics

#### 2.2.4 Artifact Manager Agent
- **Purpose**: Manage and analyze build artifacts
- **Capabilities**:
  - Artifact metadata extraction
  - Storage analysis
  - Dependency tracking
  - Artifact lifecycle management

## 3. Security Architecture

### 3.1 Authentication & Authorization

```python
# IAM Role Structure
jenkins_reader_roles = {
    "jenkins.viewer": {
        "permissions": [
            "jenkins.jobs.read",
            "jenkins.builds.read",
            "jenkins.config.read"
        ]
    },
    "jenkins.admin": {
        "permissions": [
            "jenkins.jobs.read",
            "jenkins.builds.read",
            "jenkins.config.read",
            "jenkins.system.read"
        ]
    }
}
```

### 3.2 Credential Management

```python
# Secret Manager Integration
async def get_jenkins_credentials(tool_context: ToolContext) -> JenkinsCredentials:
    """Securely retrieve Jenkins credentials from Secret Manager."""
    user_identity = tool_context.auth_context.user_identity
    
    # Validate user permissions
    if not has_jenkins_access(user_identity):
        raise UnauthorizedError("User lacks Jenkins access permissions")
    
    # Retrieve credentials from Secret Manager
    credentials = await secret_manager.get_secret(
        f"jenkins-credentials-{user_identity.project_id}"
    )
    
    return JenkinsCredentials.from_secret(credentials)
```

### 3.3 Data Validation & Sanitization

```python
# Input validation schema
class JenkinsQueryRequest(BaseModel):
    jenkins_url: HttpUrl
    query_type: Literal["jobs", "builds", "config", "artifacts"]
    filters: Optional[Dict[str, Any]] = None
    max_results: int = Field(default=100, le=1000)
    
    @validator('jenkins_url')
    def validate_jenkins_url(cls, v):
        # Validate against allowed Jenkins instances
        allowed_domains = get_allowed_jenkins_domains()
        if v.host not in allowed_domains:
            raise ValueError(f"Jenkins URL not in allowed domains: {allowed_domains}")
        return v
```

## 4. Implementation Plan

### Phase 1: Foundation (Weeks 1-2)
1. **Project Setup**
   - Create Google Cloud project
   - Configure IAM roles and permissions
   - Set up Secret Manager for credentials
   - Initialize ADK project structure

2. **Basic Jenkins Integration**
   - Implement python-jenkins client wrapper
   - Create basic authentication module
   - Develop connection validation tool
   - Set up error handling framework

### Phase 2: Core Functionality (Weeks 3-4)
1. **Main Agent Development**
   - Implement main Jenkins agent
   - Create system instructions and prompts
   - Develop response schemas
   - Implement basic callback system

2. **Core Tools Implementation**
   - Jenkins connection tool
   - Job information retrieval tool
   - Build history analysis tool
   - Basic configuration parser

### Phase 3: Advanced Features (Weeks 5-6)
1. **Sub-Agents Development**
   - Job analyzer sub-agent
   - Build history sub-agent
   - Pipeline analyzer sub-agent
   - Artifact manager sub-agent

2. **Advanced Tools**
   - Dependency analysis tool
   - Performance metrics tool
   - Security analysis tool
   - Report generation tool

### Phase 4: Security & Monitoring (Weeks 7-8)
1. **Security Implementation**
   - Complete RBAC system
   - Audit logging framework
   - Input validation and sanitization
   - Security testing and validation

2. **Monitoring & Observability**
   - Cloud Logging integration
   - Performance monitoring
   - Error tracking and alerting
   - Usage analytics

### Phase 5: Testing & Documentation (Weeks 9-10)
1. **Comprehensive Testing**
   - Unit tests for all components
   - Integration tests with test Jenkins
   - Security penetration testing
   - Performance and load testing

2. **Documentation & Deployment**
   - API documentation
   - User guides and tutorials
   - Deployment automation
   - Production readiness checklist

## 5. Technical Specifications

### 5.1 Dependencies

```toml
[tool.poetry.dependencies]
python = "^3.11"
google-adk = "^0.1.0"
python-jenkins = "^1.8.0"
google-cloud-secret-manager = "^2.16.0"
google-cloud-iam = "^2.12.0"
google-cloud-logging = "^3.8.0"
pydantic = "^2.5.0"
httpx = "^0.25.0"
tenacity = "^8.2.0"
structlog = "^23.2.0"
```

### 5.2 Environment Configuration

```python
# Required environment variables
GOOGLE_CLOUD_PROJECT = "your-project-id"
GOOGLE_CLOUD_LOCATION = "us-central1"
JENKINS_CREDENTIALS_SECRET = "jenkins-credentials"
LOG_LEVEL = "INFO"
MAX_CONCURRENT_REQUESTS = "10"
RATE_LIMIT_PER_MINUTE = "100"
```

### 5.3 Performance Requirements

- **Response Time**: < 5 seconds for standard queries
- **Throughput**: Support 100+ concurrent users
- **Availability**: 99.9% uptime SLA
- **Scalability**: Auto-scale based on demand
- **Rate Limiting**: 100 requests/minute per user

## 6. Testing Strategy

### 6.1 Test Environment Setup

```python
# Test Jenkins Configuration
TEST_JENKINS_URL = "https://jenkins.truxt.ai/"
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "Truxt@2025"
}

# Test Data Requirements
test_scenarios = [
    "Basic job listing",
    "Job configuration retrieval", 
    "Build history analysis",
    "Pipeline dependency mapping",
    "Error handling and recovery",
    "Security validation",
    "Performance under load"
]
```

## 7. Tool Framework Design

### 7.1 Core Jenkins Tools

#### 7.1.1 Jenkins Connection Tool
```python
async def validate_jenkins_connection(
    jenkins_url: str,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """Validate connection to Jenkins server and retrieve basic info."""

    credentials = await get_jenkins_credentials(tool_context)

    try:
        jenkins_client = jenkins.Jenkins(
            jenkins_url,
            username=credentials.username,
            password=credentials.password
        )

        # Test connection
        version = jenkins_client.get_version()
        user_info = jenkins_client.get_whoami()

        return {
            "status": "connected",
            "version": version,
            "user": user_info,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        await log_security_event(
            event_type="jenkins_connection_failed",
            user=tool_context.auth_context.user_identity,
            error=str(e)
        )
        raise ConnectionError(f"Failed to connect to Jenkins: {e}")
```

#### 7.1.2 Job Information Tool
```python
async def get_jenkins_jobs(
    job_filter: Optional[str] = None,
    include_config: bool = False,
    tool_context: ToolContext = None
) -> List[Dict[str, Any]]:
    """Retrieve Jenkins job information with optional filtering."""

    # Pre-execution validation
    await validate_request_permissions(tool_context, "jobs.read")

    credentials = await get_jenkins_credentials(tool_context)
    jenkins_client = create_jenkins_client(credentials, tool_context)

    try:
        # Get all jobs
        jobs = jenkins_client.get_all_jobs()

        # Apply filtering
        if job_filter:
            jobs = [job for job in jobs if job_filter.lower() in job['name'].lower()]

        # Enhance with additional information
        enhanced_jobs = []
        for job in jobs:
            job_info = {
                "name": job['name'],
                "url": job['url'],
                "color": job.get('color', 'unknown'),
                "buildable": job.get('buildable', False)
            }

            if include_config:
                config = jenkins_client.get_job_config(job['name'])
                job_info['config'] = sanitize_job_config(config)

            enhanced_jobs.append(job_info)

        # Log successful access
        await log_audit_event(
            event_type="jenkins_jobs_accessed",
            user=tool_context.auth_context.user_identity,
            job_count=len(enhanced_jobs)
        )

        return enhanced_jobs

    except Exception as e:
        await log_error_event(
            event_type="jenkins_jobs_error",
            user=tool_context.auth_context.user_identity,
            error=str(e)
        )
        raise
```

#### 7.1.3 Build History Tool
```python
async def get_build_history(
    job_name: str,
    max_builds: int = 50,
    include_details: bool = False,
    tool_context: ToolContext = None
) -> List[Dict[str, Any]]:
    """Retrieve build history for a specific Jenkins job."""

    # Validate inputs
    if max_builds > 200:
        raise ValueError("max_builds cannot exceed 200")

    await validate_request_permissions(tool_context, "builds.read")

    credentials = await get_jenkins_credentials(tool_context)
    jenkins_client = create_jenkins_client(credentials, tool_context)

    try:
        # Get job info first
        job_info = jenkins_client.get_job_info(job_name)
        builds = job_info.get('builds', [])[:max_builds]

        build_history = []
        for build in builds:
            build_number = build['number']
            build_info = jenkins_client.get_build_info(job_name, build_number)

            build_data = {
                "number": build_number,
                "result": build_info.get('result'),
                "timestamp": build_info.get('timestamp'),
                "duration": build_info.get('duration'),
                "url": build_info.get('url')
            }

            if include_details:
                build_data.update({
                    "parameters": extract_build_parameters(build_info),
                    "artifacts": extract_build_artifacts(build_info),
                    "test_results": extract_test_results(build_info)
                })

            build_history.append(build_data)

        return build_history

    except Exception as e:
        await log_error_event(
            event_type="jenkins_build_history_error",
            user=tool_context.auth_context.user_identity,
            job_name=job_name,
            error=str(e)
        )
        raise
```

### 7.2 Advanced Analysis Tools

#### 7.2.1 Dependency Analyzer Tool
```python
async def analyze_job_dependencies(
    job_name: str,
    depth: int = 3,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """Analyze upstream and downstream dependencies for a Jenkins job."""

    await validate_request_permissions(tool_context, "jobs.read")

    credentials = await get_jenkins_credentials(tool_context)
    jenkins_client = create_jenkins_client(credentials, tool_context)

    try:
        dependency_graph = {
            "job_name": job_name,
            "upstream_jobs": [],
            "downstream_jobs": [],
            "dependency_depth": depth
        }

        # Analyze upstream dependencies
        upstream_jobs = await find_upstream_jobs(jenkins_client, job_name, depth)
        dependency_graph["upstream_jobs"] = upstream_jobs

        # Analyze downstream dependencies
        downstream_jobs = await find_downstream_jobs(jenkins_client, job_name, depth)
        dependency_graph["downstream_jobs"] = downstream_jobs

        return dependency_graph

    except Exception as e:
        await log_error_event(
            event_type="jenkins_dependency_analysis_error",
            user=tool_context.auth_context.user_identity,
            job_name=job_name,
            error=str(e)
        )
        raise
```

## 8. System Instructions & Prompts

### 8.1 Main Agent System Instructions

```python
def jenkins_system_instructions() -> str:
    return """
    You are a Jenkins Read-Only Agent, an expert system for analyzing and reporting on Jenkins CI/CD infrastructure.

    ## Core Responsibilities:
    1. **Data Retrieval**: Safely extract comprehensive information from Jenkins servers
    2. **Analysis**: Provide insights on job configurations, build patterns, and system health
    3. **Security**: Maintain strict read-only access and validate all operations
    4. **Reporting**: Generate clear, actionable reports on Jenkins infrastructure

    ## Security Guidelines:
    - NEVER perform write operations (create, update, delete)
    - Always validate user permissions before accessing Jenkins data
    - Sanitize all output to prevent information leakage
    - Log all access attempts for audit purposes

    ## Data Handling:
    - Respect rate limits and avoid overwhelming Jenkins servers
    - Cache frequently accessed data when appropriate
    - Validate all input parameters before processing
    - Handle errors gracefully and provide meaningful feedback

    ## Output Format:
    - Use structured JSON responses for programmatic consumption
    - Include metadata about data freshness and completeness
    - Provide clear error messages with actionable guidance
    - Maintain consistent field naming and data types

    ## Enterprise Standards:
    - Follow principle of least privilege
    - Implement comprehensive logging and monitoring
    - Ensure scalability and reliability
    - Maintain backward compatibility

    ## Available Tools:
    - validate_jenkins_connection: Test connectivity and retrieve server info
    - get_jenkins_jobs: List and filter Jenkins jobs
    - get_build_history: Analyze build history and trends
    - analyze_job_dependencies: Map job relationships
    - get_job_config: Retrieve job configurations
    - get_artifacts: Access build artifacts and metadata

    ## Response Guidelines:
    - Always include status, data, and metadata in responses
    - Provide context for any limitations or partial data
    - Include relevant timestamps and version information
    - Suggest next steps or related queries when appropriate
    """
```

### 8.2 Sub-Agent Instructions

#### 8.2.1 Job Analyzer Instructions
```python
def job_analyzer_instructions() -> str:
    return """
    You are a Jenkins Job Analyzer, specialized in understanding job configurations and relationships.

    ## Primary Functions:
    - Parse and analyze Jenkins job configurations (XML/JSON)
    - Identify job parameters, triggers, and build steps
    - Map upstream and downstream job dependencies
    - Analyze SCM configurations and branch strategies
    - Evaluate job security settings and permissions

    ## Analysis Focus:
    - Job type identification (freestyle, pipeline, multibranch)
    - Build trigger analysis (SCM, timer, manual, upstream)
    - Parameter validation and usage patterns
    - Plugin usage and configuration
    - Security and access control settings

    ## Output Requirements:
    - Structured job metadata with clear categorization
    - Dependency graphs with relationship types
    - Configuration summaries with key insights
    - Risk assessments for security and maintenance
    """
```

#### 8.2.2 Build History Analyzer Instructions
```python
def build_history_instructions() -> str:
    return """
    You are a Jenkins Build History Analyzer, focused on build trends and performance analysis.

    ## Primary Functions:
    - Analyze build success/failure patterns
    - Calculate performance metrics and trends
    - Identify build bottlenecks and optimization opportunities
    - Track artifact generation and storage patterns
    - Evaluate test result trends and quality metrics

    ## Analysis Capabilities:
    - Build duration analysis and trend identification
    - Failure pattern recognition and root cause analysis
    - Resource utilization and capacity planning
    - Quality gate compliance and test coverage trends
    - Deployment frequency and lead time metrics

    ## Reporting Focus:
    - Performance dashboards with key metrics
    - Trend analysis with predictive insights
    - Failure analysis with actionable recommendations
    - Capacity planning and resource optimization
    """
```

### 8.3 Response Schema

```python
jenkins_response_schema = {
    "type": "object",
    "properties": {
        "status": {
            "type": "string",
            "enum": ["success", "error", "warning", "partial"]
        },
        "data": {
            "type": "object",
            "description": "The requested Jenkins data"
        },
        "metadata": {
            "type": "object",
            "properties": {
                "timestamp": {"type": "string", "format": "date-time"},
                "jenkins_version": {"type": "string"},
                "jenkins_url": {"type": "string"},
                "data_freshness": {"type": "string"},
                "total_records": {"type": "integer"},
                "query_duration_ms": {"type": "integer"},
                "rate_limit_remaining": {"type": "integer"}
            },
            "required": ["timestamp", "total_records"]
        },
        "errors": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "code": {"type": "string"},
                    "message": {"type": "string"},
                    "details": {"type": "object"}
                }
            }
        },
        "warnings": {
            "type": "array",
            "items": {"type": "string"}
        },
        "pagination": {
            "type": "object",
            "properties": {
                "page": {"type": "integer"},
                "page_size": {"type": "integer"},
                "total_pages": {"type": "integer"},
                "has_next": {"type": "boolean"}
            }
        }
    },
    "required": ["status", "data", "metadata"]
}
```

### 8.4 Callback Functions

#### 8.4.1 Security Validation Callback
```python
async def security_validation_callback(callback_context: CallbackContext):
    """Pre-execution security validation."""

    # Validate user authentication
    if not callback_context.auth_context.is_authenticated:
        raise UnauthorizedError("User not authenticated")

    # Check rate limiting
    user_id = callback_context.auth_context.user_identity.user_id
    if await is_rate_limited(user_id):
        raise RateLimitError("Rate limit exceeded")

    # Validate Jenkins URL if provided
    if "jenkins_url" in callback_context.invocation_context.args:
        jenkins_url = callback_context.invocation_context.args["jenkins_url"]
        if not is_allowed_jenkins_url(jenkins_url):
            raise SecurityError(f"Jenkins URL not allowed: {jenkins_url}")

    # Log access attempt
    await log_access_attempt(
        user=callback_context.auth_context.user_identity,
        agent=callback_context.invocation_context.agent.name,
        timestamp=datetime.utcnow()
    )
```

#### 8.4.2 Audit Logging Callback
```python
async def audit_logging_callback(callback_context: CallbackContext):
    """Post-execution audit logging."""

    # Extract execution metadata
    execution_time = callback_context.invocation_context.execution_time
    response_size = len(str(callback_context.response))

    # Log successful execution
    await log_audit_event(
        event_type="jenkins_agent_execution",
        user=callback_context.auth_context.user_identity,
        agent=callback_context.invocation_context.agent.name,
        execution_time_ms=execution_time,
        response_size_bytes=response_size,
        status=callback_context.response.get("status", "unknown")
    )

    # Update usage metrics
    await update_usage_metrics(
        user_id=callback_context.auth_context.user_identity.user_id,
        operation_count=1,
        data_transferred=response_size
    )
```
