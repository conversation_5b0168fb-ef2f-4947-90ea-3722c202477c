# Jenkins Reader Agent Environment Configuration

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1

# Jenkins Configuration
JENKINS_CREDENTIALS_SECRET=jenkins-credentials
ALLOWED_JENKINS_DOMAINS=jenkins.truxt.ai,localhost,jenkins.example.com

# Application Configuration
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10
RATE_LIMIT_PER_MINUTE=100

# Security Configuration
ENABLE_AUDIT_LOGGING=true
ENABLE_RATE_LIMITING=true
MAX_RESULTS_PER_QUERY=1000

# Performance Configuration
CONNECTION_TIMEOUT=30
READ_TIMEOUT=60
MAX_RETRIES=3

# Development Configuration
DEBUG=false
TESTING=false

# Optional: Override default model settings
# GEMINI_MODEL=gemini-2.5-pro
# TEMPERATURE=0.1
# MAX_TOKENS=8192

# Optional: Custom authentication settings
# SERVICE_ACCOUNT_KEY_PATH=/path/to/service-account.json
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/credentials.json
