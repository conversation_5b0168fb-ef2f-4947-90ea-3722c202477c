# Jenkins Reader Agent Dockerfile
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV POETRY_NO_INTERACTION=1
ENV POETRY_VENV_IN_PROJECT=1
ENV POETRY_CACHE_DIR=/opt/poetry_cache

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Set work directory
WORKDIR /app

# Copy Poetry files
COPY pyproject.toml poetry.lock* ./

# Install dependencies
RUN poetry install --without dev && rm -rf $POETRY_CACHE_DIR

# Copy application code
COPY jenkins_agent/ ./jenkins_agent/
COPY test_agent.py ./

# Create non-root user
RUN useradd --create-home --shell /bin/bash jenkins-agent
RUN chown -R jenkins-agent:jenkins-agent /app
USER jenkins-agent

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD poetry run python -m jenkins_agent.main health

# Default command
CMD ["poetry", "run", "python", "-m", "jenkins_agent.main", "server"]

# Expose port (if running as server)
EXPOSE 8080
