# Jenkins Reader Agent

A comprehensive Jenkins read-only agent built using Google's Agent Development Kit (ADK) that provides secure, scalable access to Jenkins CI/CD infrastructure data.

## Overview

The Jenkins Reader Agent is an enterprise-grade solution for extracting, analyzing, and reporting on Jenkins data while maintaining strict security standards and read-only access. Built on Google Cloud Platform using ADK, it provides intelligent insights into Jenkins jobs, builds, pipelines, and artifacts.

## Key Features

- **Read-Only Access**: Secure, non-destructive Jenkins data extraction
- **Enterprise Security**: Google IAM integration, RBAC, and comprehensive audit logging
- **Intelligent Analysis**: AI-powered insights using Gemini models with thinking capabilities
- **Multi-Agent Architecture**: Specialized sub-agents for different data types
- **Scalable Design**: Auto-scaling deployment on Google Cloud Platform
- **Comprehensive Monitoring**: Full observability with Cloud Logging and Monitoring

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Google Cloud Platform                        │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   IAM & RBAC    │  │  Secret Manager │  │   Cloud Logging │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Jenkins Read-Only Agent                        │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ Main Agent  │  │ Sub-Agents  │  │   Tool Framework    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │              Jenkins API Integration Layer                  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │ │
│  │  │ Auth Module │  │ API Client  │  │  Data Validation    │ │ │
│  │  └─────────────┘  └─────────────┘  └─────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                      Jenkins Server                             │
│  ┌─────────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │      Jobs       │  │   Builds    │  │     Artifacts       │ │
│  └─────────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Components

### Main Agent
- **Purpose**: Orchestrates all Jenkins operations and coordinates sub-agents
- **Model**: Gemini 2.5 Pro with thinking capabilities
- **Features**: Controlled generation, system instructions, callback validation

### Sub-Agents
1. **Job Analyzer Agent**: Job configurations, dependencies, parameters
2. **Build History Agent**: Build trends, performance metrics, failure analysis
3. **Pipeline Analyzer Agent**: Pipeline stages, Groovy scripts, shared libraries
4. **Artifact Manager Agent**: Artifact metadata, storage analysis, lifecycle

### Tool Framework
- **Connection Tools**: Jenkins server validation and connectivity
- **Data Extraction Tools**: Jobs, builds, configurations, artifacts
- **Analysis Tools**: Dependencies, performance, security assessment
- **Reporting Tools**: Structured data output and visualization

## Security Features

- **Google IAM Integration**: Role-based access control
- **Secret Manager**: Secure credential storage and rotation
- **Input Validation**: Comprehensive parameter sanitization
- **Audit Logging**: Complete access and operation tracking
- **Rate Limiting**: Abuse prevention and resource protection
- **Data Sanitization**: Sensitive information filtering

## Quick Start

### Prerequisites
- Google Cloud Project with required APIs enabled
- Jenkins server with API access
- Python 3.11+
- Poetry for dependency management

### Installation

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd jenkins-reader-agent
   poetry install
   ```

2. **Configure Environment**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Set up Google Cloud**
   ```bash
   gcloud auth application-default login
   gcloud config set project YOUR_PROJECT_ID
   ```

4. **Deploy Credentials**
   ```bash
   # Store Jenkins credentials in Secret Manager
   gcloud secrets create jenkins-credentials --data-file=credentials.json
   ```

5. **Run Agent**
   ```bash
   poetry run python -m jenkins_agent.main
   ```

## Configuration

### Environment Variables
```bash
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=us-central1
JENKINS_CREDENTIALS_SECRET=jenkins-credentials
LOG_LEVEL=INFO
MAX_CONCURRENT_REQUESTS=10
RATE_LIMIT_PER_MINUTE=100
```

### Jenkins Credentials Format
```json
{
  "url": "https://jenkins.example.com",
  "username": "admin",
  "password": "your-password"
}
```

## Usage Examples

### Basic Job Listing
```python
from jenkins_agent import JenkinsAgent

agent = JenkinsAgent()
jobs = await agent.get_jenkins_jobs(
    jenkins_url="https://jenkins.example.com",
    job_filter="production"
)
```

### Build History Analysis
```python
build_history = await agent.get_build_history(
    job_name="production-deploy",
    max_builds=50,
    include_details=True
)
```

### Dependency Analysis
```python
dependencies = await agent.analyze_job_dependencies(
    job_name="api-service",
    depth=3
)
```

## API Reference

### Core Tools
- `validate_jenkins_connection()`: Test connectivity and server info
- `get_jenkins_jobs()`: List and filter Jenkins jobs
- `get_build_history()`: Analyze build history and trends
- `analyze_job_dependencies()`: Map job relationships
- `get_job_config()`: Retrieve job configurations
- `get_artifacts()`: Access build artifacts and metadata

### Response Format
```json
{
  "status": "success",
  "data": { ... },
  "metadata": {
    "timestamp": "2024-01-01T00:00:00Z",
    "jenkins_version": "2.401.3",
    "total_records": 42,
    "query_duration_ms": 1250
  },
  "errors": [],
  "warnings": []
}
```

## Development

### Project Structure
```
jenkins-reader-agent/
├── jenkins_agent/           # Main package
│   ├── agent.py            # Main agent implementation
│   ├── prompts.py          # System instructions
│   ├── sub_agents/         # Specialized sub-agents
│   ├── tools/              # Jenkins API tools
│   ├── utils/              # Utilities and helpers
│   └── config/             # Configuration management
├── tests/                  # Test suite
├── deployment/             # Deployment configurations
├── docs/                   # Documentation
├── eval/                   # Evaluation scripts
└── examples/               # Usage examples
```

### Running Tests
```bash
poetry run pytest tests/ -v
poetry run pytest tests/ --cov=jenkins_agent
```

### Development Setup
```bash
# Install development dependencies
poetry install --with dev

# Run linting
poetry run black jenkins_agent/
poetry run isort jenkins_agent/
poetry run flake8 jenkins_agent/

# Run type checking
poetry run mypy jenkins_agent/
```

## Deployment

### Google Cloud Run
```bash
# Build and deploy
gcloud run deploy jenkins-reader-agent \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

### Docker
```bash
# Build image
docker build -t jenkins-reader-agent .

# Run container
docker run -p 8080:8080 \
  -e GOOGLE_CLOUD_PROJECT=your-project \
  jenkins-reader-agent
```

## Monitoring

### Key Metrics
- Request count and duration
- Error rates and types
- Jenkins connection status
- User activity and usage patterns
- Resource utilization

### Alerts
- High error rate (>5%)
- Jenkins connection failures
- High response time (>10s)
- Rate limit violations

## Security

### Best Practices
- Use service accounts with minimal permissions
- Rotate credentials regularly
- Monitor access patterns
- Implement rate limiting
- Validate all inputs
- Sanitize sensitive data

### Compliance
- SOC 2 Type II controls
- GDPR data protection
- Audit logging requirements
- Access control standards

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Submit a pull request

### Code Standards
- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive tests
- Document all public APIs
- Use meaningful commit messages

## Support

### Documentation
- [Design Document](docs/design.md)
- [Implementation Tasks](docs/tasks.md)
- [API Reference](docs/api.md)
- [Security Guide](docs/security.md)

### Getting Help
- Check the documentation
- Review existing issues
- Create a new issue with details
- Contact the development team

## License

This project is licensed under the Apache License 2.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Google Cloud ADK team for the framework
- Jenkins community for API documentation
- Contributors and testers

---

**Note**: This is a read-only agent designed for data extraction and analysis. It does not perform any write operations on Jenkins servers.
