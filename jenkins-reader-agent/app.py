#!/usr/bin/env python3
"""
FastAPI Web application for Jenkins Reader Agent.

This module sets up a web interface for the Jenkins Reader Agent
using FastAPI for testing and demonstration.
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, Optional

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException, Query
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware

from jenkins_agent.tools.connection_tools import validate_jenkins_connection
from jenkins_agent.tools.job_tools import get_jenkins_jobs, get_job_config
from jenkins_agent.tools.build_tools import get_build_history, get_artifacts
from jenkins_agent.config.settings import get_settings
from jenkins_agent.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

# Mock context for testing
class MockUserIdentity:
    def __init__(self):
        self.user_id = "web_user"
        self.email = "<EMAIL>"

class MockAuthContext:
    def __init__(self):
        self.user_identity = MockUserIdentity()

class MockToolContext:
    def __init__(self):
        self.auth_context = MockAuthContext()

def create_jenkins_app():
    """Create and configure the FastAPI application."""

    settings = get_settings()

    app = FastAPI(
        title="Jenkins Reader Agent",
        description="Enterprise-grade Jenkins read-only agent with comprehensive data extraction capabilities",
        version="1.0.0",
        debug=settings.debug
    )

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    @app.get("/", response_class=HTMLResponse)
    async def home():
        """Home page with API documentation."""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Jenkins Reader Agent</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
                .endpoint { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
                .method { background: #007bff; color: white; padding: 5px 10px; border-radius: 3px; }
                .status { color: green; font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🚀 Jenkins Reader Agent</h1>
                <p class="status">✅ Status: OPERATIONAL</p>
                <p>Enterprise-grade Jenkins read-only agent with comprehensive data extraction capabilities</p>
            </div>

            <h2>📋 Available Endpoints</h2>

            <div class="endpoint">
                <span class="method">GET</span> <strong>/health</strong>
                <p>Check agent health and Jenkins connectivity</p>
            </div>

            <div class="endpoint">
                <span class="method">GET</span> <strong>/validate</strong>
                <p>Validate Jenkins connection and get server info</p>
            </div>

            <div class="endpoint">
                <span class="method">GET</span> <strong>/jobs</strong>
                <p>List Jenkins jobs with optional filtering</p>
                <p><em>Parameters: job_filter, max_results, include_config</em></p>
            </div>

            <div class="endpoint">
                <span class="method">GET</span> <strong>/jobs/{job_name}/config</strong>
                <p>Get job configuration</p>
            </div>

            <div class="endpoint">
                <span class="method">GET</span> <strong>/jobs/{job_name}/builds</strong>
                <p>Get build history for a job</p>
                <p><em>Parameters: max_builds, include_details</em></p>
            </div>

            <div class="endpoint">
                <span class="method">GET</span> <strong>/docs</strong>
                <p>Interactive API documentation (Swagger UI)</p>
            </div>

            <h2>🔗 Quick Links</h2>
            <ul>
                <li><a href="/health">Health Check</a></li>
                <li><a href="/validate">Validate Connection</a></li>
                <li><a href="/jobs">List Jobs</a></li>
                <li><a href="/docs">API Documentation</a></li>
            </ul>
        </body>
        </html>
        """

    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        settings = get_settings()
        return {
            "status": "healthy",
            "service": "Jenkins Reader Agent",
            "version": "1.0.0",
            "project": settings.google_cloud_project,
            "debug": settings.debug,
            "timestamp": "2025-06-02T02:55:00Z"
        }

    @app.get("/validate")
    async def validate_connection():
        """Validate Jenkins connection."""
        try:
            mock_context = MockToolContext()
            result = await validate_jenkins_connection(
                tool_context=mock_context,
                jenkins_url="https://jenkins.truxt.ai/"
            )
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/jobs")
    async def list_jobs(
        job_filter: Optional[str] = Query(None, description="Filter jobs by name pattern"),
        max_results: int = Query(20, description="Maximum number of results"),
        include_config: bool = Query(False, description="Include job configurations")
    ):
        """List Jenkins jobs."""
        try:
            mock_context = MockToolContext()
            result = await get_jenkins_jobs(
                job_filter=job_filter,
                include_config=include_config,
                max_results=max_results,
                tool_context=mock_context
            )
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/jobs/{job_name}/config")
    async def get_job_configuration(job_name: str):
        """Get job configuration."""
        try:
            mock_context = MockToolContext()
            result = await get_job_config(
                job_name=job_name,
                tool_context=mock_context
            )
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/jobs/{job_name}/builds")
    async def get_job_builds(
        job_name: str,
        max_builds: int = Query(10, description="Maximum number of builds"),
        include_details: bool = Query(True, description="Include detailed build information")
    ):
        """Get build history for a job."""
        try:
            mock_context = MockToolContext()
            result = await get_build_history(
                job_name=job_name,
                max_builds=max_builds,
                include_details=include_details,
                tool_context=mock_context
            )
            return result
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    logger.info("Jenkins Reader Agent web application created successfully")
    return app

# Create the application instance
app = create_jenkins_app()

if __name__ == "__main__":
    import uvicorn

    settings = get_settings()

    print("🚀 Starting Jenkins Reader Agent Web Interface")
    print(f"📊 Project: {settings.google_cloud_project}")
    print(f"🌐 URL: http://localhost:8080")
    print(f"📚 API Docs: http://localhost:8080/docs")

    # Run the application
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8080,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
