#!/usr/bin/env python3
"""
ADK Web application for Jenkins Reader Agent.

This module sets up the web interface for the Jenkins Reader Agent
using Google ADK Web framework.
"""

import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from google.adk.web import create_app
from jenkins_agent.agent import jenkins_agent
from jenkins_agent.config.settings import get_settings
from jenkins_agent.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)

def create_jenkins_app():
    """Create and configure the ADK Web application."""
    
    # Get settings
    settings = get_settings()
    
    logger.info(
        "Creating Jenkins Reader Agent web application",
        extra={
            "project": settings.google_cloud_project,
            "debug": settings.debug
        }
    )
    
    # Create ADK Web app with our Jenkins agent
    app = create_app(
        agent=jenkins_agent,
        title="Jenkins Reader Agent",
        description="Enterprise-grade Jenkins read-only agent with comprehensive data extraction capabilities",
        version="1.0.0",
        debug=settings.debug
    )
    
    logger.info("Jenkins Reader Agent web application created successfully")
    
    return app

# Create the application instance
app = create_jenkins_app()

if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    
    # Run the application
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8080,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
