# Jenkins Reader Agent - Setup Guide

## Prerequisites

### System Requirements
- Python 3.11 or higher
- Poetry for dependency management
- Google Cloud SDK (gcloud CLI)
- Access to a Google Cloud Project
- Jenkins server with API access

### Google Cloud Setup
1. **Create or select a Google Cloud Project**
2. **Enable required APIs**
3. **Set up authentication**
4. **Configure IAM permissions**

## Installation

### 1. <PERSON><PERSON> and Setup Project

```bash
# Navigate to the project directory
cd jenkins-reader-agent

# Install dependencies using Poetry
poetry install

# Activate the virtual environment
poetry shell
```

### 2. Environment Configuration

```bash
# Copy the example environment file
cp .env.example .env

# Edit the environment file with your configuration
nano .env
```

### 3. Google Cloud Configuration

#### Enable Required APIs
```bash
# Set your project ID
export PROJECT_ID="your-project-id"
gcloud config set project $PROJECT_ID

# Enable required APIs
gcloud services enable \
  iam.googleapis.com \
  secretmanager.googleapis.com \
  logging.googleapis.com \
  monitoring.googleapis.com \
  run.googleapis.com \
  cloudbuild.googleapis.com
```

#### Set up Authentication
```bash
# Authenticate with Google Cloud
gcloud auth application-default login

# Or use a service account key
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
```

#### Create IAM Roles
```bash
# Create custom IAM roles for Jenkins access
gcloud iam roles create jenkinsViewer \
  --project=$PROJECT_ID \
  --title="Jenkins Viewer" \
  --description="Read-only access to Jenkins data" \
  --permissions="secretmanager.versions.access,logging.logEntries.create"

gcloud iam roles create jenkinsAdmin \
  --project=$PROJECT_ID \
  --title="Jenkins Admin" \
  --description="Administrative access to Jenkins data" \
  --permissions="secretmanager.versions.access,secretmanager.secrets.create,logging.logEntries.create"
```

#### Create Service Account
```bash
# Create service account for the agent
gcloud iam service-accounts create jenkins-reader-agent \
  --display-name="Jenkins Reader Agent" \
  --description="Service account for Jenkins Reader Agent"

# Grant necessary permissions
gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:jenkins-reader-agent@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="projects/$PROJECT_ID/roles/jenkinsViewer"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:jenkins-reader-agent@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"

gcloud projects add-iam-policy-binding $PROJECT_ID \
  --member="serviceAccount:jenkins-reader-agent@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/logging.logWriter"
```

### 4. Jenkins Credentials Setup

#### Create Jenkins Credentials JSON
```bash
# Create credentials file
cat > jenkins-credentials.json << EOF
{
  "username": "admin",
  "password": "Truxt@2025",
  "url": "https://jenkins.truxt.ai/"
}
EOF
```

#### Store in Secret Manager
```bash
# Create secret in Secret Manager
gcloud secrets create jenkins-credentials \
  --data-file=jenkins-credentials.json

# Grant access to service account
gcloud secrets add-iam-policy-binding jenkins-credentials \
  --member="serviceAccount:jenkins-reader-agent@$PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"

# Clean up local credentials file
rm jenkins-credentials.json
```

### 5. Verify Setup

#### Test Google Cloud Connection
```bash
# Test authentication
gcloud auth list

# Test Secret Manager access
gcloud secrets versions access latest --secret="jenkins-credentials"
```

#### Test Jenkins Connection
```bash
# Run the connection test
poetry run python -c "
from jenkins_agent.utils.auth import create_test_credentials, validate_credentials
import asyncio

async def test():
    creds = create_test_credentials()
    result = await validate_credentials(creds)
    print(f'Jenkins connection test: {\"SUCCESS\" if result else \"FAILED\"}')

asyncio.run(test())
"
```

## Development Setup

### 1. Install Development Dependencies
```bash
# Install with development dependencies
poetry install --with dev,test

# Install pre-commit hooks
pre-commit install
```

### 2. Run Tests
```bash
# Run unit tests
poetry run pytest tests/unit/ -v

# Run with coverage
poetry run pytest tests/unit/ --cov=jenkins_agent --cov-report=html

# Run integration tests (requires Jenkins access)
poetry run pytest tests/integration/ -v
```

### 3. Code Quality Checks
```bash
# Format code
poetry run black jenkins_agent/
poetry run isort jenkins_agent/

# Lint code
poetry run flake8 jenkins_agent/

# Type checking
poetry run mypy jenkins_agent/

# Security scanning
poetry run bandit -r jenkins_agent/
```

## Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `GOOGLE_CLOUD_PROJECT` | Google Cloud project ID | - | Yes |
| `GOOGLE_CLOUD_LOCATION` | Google Cloud region | `us-central1` | No |
| `JENKINS_CREDENTIALS_SECRET` | Secret Manager secret name | `jenkins-credentials` | No |
| `ALLOWED_JENKINS_DOMAINS` | Comma-separated allowed domains | `jenkins.truxt.ai,localhost` | No |
| `LOG_LEVEL` | Logging level | `INFO` | No |
| `MAX_CONCURRENT_REQUESTS` | Max concurrent requests | `10` | No |
| `RATE_LIMIT_PER_MINUTE` | Rate limit per minute | `100` | No |

### Jenkins Credentials Format

The Jenkins credentials should be stored in Secret Manager as JSON:

```json
{
  "username": "your-jenkins-username",
  "password": "your-jenkins-password-or-token",
  "url": "https://your-jenkins-server.com/"
}
```

## Deployment

### Local Development
```bash
# Run the agent locally
poetry run python -m jenkins_agent.main
```

### Google Cloud Run
```bash
# Build and deploy to Cloud Run
gcloud run deploy jenkins-reader-agent \
  --source . \
  --platform managed \
  --region us-central1 \
  --service-account jenkins-reader-agent@$PROJECT_ID.iam.gserviceaccount.com \
  --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID
```

### Docker
```bash
# Build Docker image
docker build -t jenkins-reader-agent .

# Run container
docker run -p 8080:8080 \
  -e GOOGLE_CLOUD_PROJECT=$PROJECT_ID \
  -v ~/.config/gcloud:/root/.config/gcloud \
  jenkins-reader-agent
```

## Troubleshooting

### Common Issues

#### Authentication Errors
```bash
# Check authentication
gcloud auth list
gcloud auth application-default print-access-token

# Re-authenticate if needed
gcloud auth application-default login
```

#### Secret Manager Access
```bash
# Check secret exists
gcloud secrets list

# Check permissions
gcloud secrets get-iam-policy jenkins-credentials

# Test access
gcloud secrets versions access latest --secret="jenkins-credentials"
```

#### Jenkins Connection Issues
```bash
# Test Jenkins connectivity
curl -u username:password https://jenkins.truxt.ai/api/json

# Check network connectivity
ping jenkins.truxt.ai
nslookup jenkins.truxt.ai
```

### Logging and Monitoring

#### View Logs
```bash
# View Cloud Logging
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=jenkins-reader-agent" --limit=50

# View local logs
tail -f logs/jenkins-agent.log
```

#### Monitor Performance
```bash
# View Cloud Monitoring metrics
gcloud monitoring metrics list --filter="metric.type:custom.googleapis.com/jenkins_agent"
```

## Security Considerations

### Best Practices
1. **Use service accounts** with minimal required permissions
2. **Rotate credentials** regularly
3. **Monitor access logs** for suspicious activity
4. **Use HTTPS** for all Jenkins connections
5. **Validate input** parameters thoroughly
6. **Sanitize output** data to prevent information leakage

### Security Checklist
- [ ] Service account has minimal permissions
- [ ] Jenkins credentials stored in Secret Manager
- [ ] Audit logging enabled
- [ ] Rate limiting configured
- [ ] Input validation implemented
- [ ] Output sanitization enabled
- [ ] HTTPS enforced for Jenkins connections
- [ ] Regular security scans performed

## Support

### Getting Help
1. Check the [documentation](../README.md)
2. Review [common issues](troubleshooting.md)
3. Check the [API reference](api.md)
4. Create an issue in the repository

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests and documentation
5. Submit a pull request

---

This setup guide provides comprehensive instructions for installing, configuring, and deploying the Jenkins Reader Agent in various environments.
