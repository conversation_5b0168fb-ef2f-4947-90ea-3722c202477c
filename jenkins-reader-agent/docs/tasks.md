# Jenkins Reader Agent - Implementation Tasks

## Overview
This document provides a comprehensive breakdown of implementation tasks for the Jenkins Reader Agent using Google's ADK framework.

## Phase 1: Foundation Setup (Weeks 1-2)

### Task 1.1: Google Cloud Project Setup
**Duration**: 2 days | **Priority**: Critical | **Dependencies**: None

#### Subtasks:
1. **Create Google Cloud Project**
   ```bash
   gcloud projects create jenkins-reader-agent-prod
   gcloud config set project jenkins-reader-agent-prod
   gcloud billing projects link jenkins-reader-agent-prod --billing-account=BILLING_ACCOUNT_ID
   ```

2. **Enable Required APIs**
   ```bash
   gcloud services enable \
     iam.googleapis.com \
     secretmanager.googleapis.com \
     logging.googleapis.com \
     monitoring.googleapis.com \
     run.googleapis.com \
     cloudbuild.googleapis.com
   ```

3. **Configure IAM Roles**
   ```bash
   # Create custom roles
   gcloud iam roles create jenkinsViewer \
     --project=jenkins-reader-agent-prod \
     --file=roles/jenkins-viewer.yaml
   
   gcloud iam roles create jenkinsAdmin \
     --project=jenkins-reader-agent-prod \
     --file=roles/jenkins-admin.yaml
   ```

4. **Set up Secret Manager**
   ```bash
   # Create secrets for Jenkins credentials
   gcloud secrets create jenkins-credentials \
     --data-file=credentials/jenkins-creds.json
   
   # Set up access policies
   gcloud secrets add-iam-policy-binding jenkins-credentials \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/secretmanager.secretAccessor"
   ```

#### Deliverables:
- ✅ GCP project with all required APIs enabled
- ✅ IAM configuration with custom roles
- ✅ Secret Manager setup with test credentials
- ✅ Project security baseline documentation

### Task 1.2: ADK Project Structure
**Duration**: 3 days | **Priority**: Critical | **Dependencies**: Task 1.1

#### Subtasks:
1. **Initialize Project Structure**
   ```
   jenkins-reader-agent/
   ├── jenkins_agent/
   │   ├── __init__.py
   │   ├── agent.py                 # Main agent implementation
   │   ├── prompts.py              # System instructions
   │   ├── sub_agents/             # Specialized sub-agents
   │   │   ├── __init__.py
   │   │   ├── job_analyzer/
   │   │   │   ├── __init__.py
   │   │   │   ├── agent.py
   │   │   │   └── prompts.py
   │   │   ├── build_history/
   │   │   ├── pipeline_analyzer/
   │   │   └── artifact_manager/
   │   ├── tools/                  # Jenkins API tools
   │   │   ├── __init__.py
   │   │   ├── jenkins_client.py
   │   │   ├── connection_tools.py
   │   │   ├── job_tools.py
   │   │   ├── build_tools.py
   │   │   └── security_tools.py
   │   ├── utils/                  # Utilities and helpers
   │   │   ├── __init__.py
   │   │   ├── auth.py
   │   │   ├── validation.py
   │   │   ├── logging.py
   │   │   └── exceptions.py
   │   └── config/                 # Configuration management
   │       ├── __init__.py
   │       ├── settings.py
   │       └── schemas.py
   ├── tests/                      # Test suite
   │   ├── unit/
   │   ├── integration/
   │   └── fixtures/
   ├── deployment/                 # Deployment configurations
   │   ├── docker/
   │   ├── kubernetes/
   │   └── terraform/
   ├── docs/                       # Documentation
   │   ├── api.md
   │   ├── security.md
   │   └── examples/
   ├── eval/                       # Evaluation scripts
   ├── examples/                   # Usage examples
   ├── pyproject.toml
   ├── Dockerfile
   ├── .env.example
   └── README.md
   ```

2. **Configure Dependencies**
   ```toml
   [tool.poetry.dependencies]
   python = "^3.11"
   google-adk = "^0.1.0"
   python-jenkins = "^1.8.0"
   google-cloud-secret-manager = "^2.16.0"
   google-cloud-iam = "^2.12.0"
   google-cloud-logging = "^3.8.0"
   pydantic = "^2.5.0"
   httpx = "^0.25.0"
   tenacity = "^8.2.0"
   structlog = "^23.2.0"
   
   [tool.poetry.group.dev.dependencies]
   pytest = "^7.4.0"
   pytest-asyncio = "^0.21.0"
   pytest-cov = "^4.1.0"
   black = "^23.7.0"
   isort = "^5.12.0"
   flake8 = "^6.0.0"
   mypy = "^1.5.0"
   ```

3. **Environment Configuration**
   ```python
   # jenkins_agent/config/settings.py
   from pydantic import BaseSettings
   
   class Settings(BaseSettings):
       google_cloud_project: str
       google_cloud_location: str = "us-central1"
       jenkins_credentials_secret: str = "jenkins-credentials"
       log_level: str = "INFO"
       max_concurrent_requests: int = 10
       rate_limit_per_minute: int = 100
       
       class Config:
           env_file = ".env"
   ```

#### Deliverables:
- ✅ Complete project structure with all directories
- ✅ Poetry configuration with dependencies
- ✅ Environment configuration system
- ✅ Basic project documentation

### Task 1.3: Jenkins API Integration Foundation
**Duration**: 3 days | **Priority**: Critical | **Dependencies**: Task 1.2

#### Subtasks:
1. **Jenkins Client Wrapper**
   ```python
   # jenkins_agent/tools/jenkins_client.py
   import jenkins
   from tenacity import retry, stop_after_attempt, wait_exponential
   
   class JenkinsClientWrapper:
       def __init__(self, url: str, credentials: JenkinsCredentials):
           self.client = jenkins.Jenkins(
               url, 
               username=credentials.username, 
               password=credentials.password.get_secret_value()
           )
           self.url = url
           self.rate_limiter = RateLimiter(calls=100, period=60)
       
       @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
       async def get_version(self) -> str:
           """Get Jenkins version with retry logic."""
           async with self.rate_limiter:
               return self.client.get_version()
       
       @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
       async def get_jobs(self, depth: int = 1) -> List[Dict]:
           """Get all jobs with enhanced error handling."""
           async with self.rate_limiter:
               return self.client.get_all_jobs(depth=depth)
   ```

2. **Authentication Module**
   ```python
   # jenkins_agent/utils/auth.py
   from google.cloud import secretmanager
   from pydantic import BaseModel, SecretStr, HttpUrl
   
   class JenkinsCredentials(BaseModel):
       username: str
       password: SecretStr
       url: HttpUrl
       
   async def get_jenkins_credentials(project_id: str, secret_name: str) -> JenkinsCredentials:
       """Retrieve credentials from Secret Manager."""
       client = secretmanager.SecretManagerServiceClient()
       name = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
       
       response = client.access_secret_version(request={"name": name})
       secret_data = response.payload.data.decode("UTF-8")
       
       return JenkinsCredentials.parse_raw(secret_data)
   ```

3. **Error Handling Framework**
   ```python
   # jenkins_agent/utils/exceptions.py
   class JenkinsAgentError(Exception):
       """Base exception for Jenkins Agent."""
       pass
       
   class ConnectionError(JenkinsAgentError):
       """Jenkins connection failed."""
       pass
       
   class AuthenticationError(JenkinsAgentError):
       """Authentication failed."""
       pass
       
   class RateLimitError(JenkinsAgentError):
       """Rate limit exceeded."""
       pass
       
   class ValidationError(JenkinsAgentError):
       """Input validation failed."""
       pass
   ```

#### Deliverables:
- ✅ Jenkins client wrapper with rate limiting
- ✅ Authentication and credential management
- ✅ Comprehensive error handling system
- ✅ Unit tests for core functionality

## Phase 2: Core Functionality (Weeks 3-4)

### Task 2.1: Main Agent Development
**Duration**: 4 days | **Priority**: Critical | **Dependencies**: Task 1.3

#### Subtasks:
1. **Main Agent Implementation**
   ```python
   # jenkins_agent/agent.py
   from google.adk.agents import Agent
   from google.genai.types import GenerateContentConfig
   
   jenkins_agent = Agent(
       model="gemini-2.5-pro",
       name="jenkins_read_only_agent",
       instruction=jenkins_system_instructions(),
       global_instruction=jenkins_global_context(),
       sub_agents=[
           job_analyzer_agent,
           build_history_agent,
           pipeline_analyzer_agent,
           artifact_manager_agent
       ],
       tools=[
           validate_jenkins_connection,
           get_jenkins_jobs,
           get_build_history,
           analyze_job_dependencies,
           get_job_config,
           get_artifacts
       ],
       before_agent_callback=security_validation_callback,
       after_agent_callback=audit_logging_callback,
       generate_content_config=GenerateContentConfig(
           temperature=0.1,
           response_schema=jenkins_response_schema
       )
   )
   ```

2. **System Instructions**
   ```python
   # jenkins_agent/prompts.py
   def jenkins_system_instructions() -> str:
       return """
       You are a Jenkins Read-Only Agent, an expert system for analyzing Jenkins CI/CD infrastructure.
       
       ## Core Responsibilities:
       1. **Data Retrieval**: Extract comprehensive Jenkins information safely
       2. **Analysis**: Provide insights on configurations, builds, and performance
       3. **Security**: Maintain strict read-only access with validation
       4. **Reporting**: Generate clear, actionable reports
       
       ## Available Tools:
       - validate_jenkins_connection: Test connectivity and server info
       - get_jenkins_jobs: List and filter Jenkins jobs
       - get_build_history: Analyze build history and trends
       - analyze_job_dependencies: Map job relationships
       - get_job_config: Retrieve job configurations
       - get_artifacts: Access build artifacts and metadata
       """
   ```

3. **Response Schema**
   ```python
   # jenkins_agent/config/schemas.py
   jenkins_response_schema = {
       "type": "object",
       "properties": {
           "status": {
               "type": "string",
               "enum": ["success", "error", "warning", "partial"]
           },
           "data": {
               "type": "object",
               "description": "Jenkins data payload"
           },
           "metadata": {
               "type": "object",
               "properties": {
                   "timestamp": {"type": "string", "format": "date-time"},
                   "jenkins_version": {"type": "string"},
                   "total_records": {"type": "integer"},
                   "query_duration_ms": {"type": "integer"}
               }
           }
       },
       "required": ["status", "data", "metadata"]
   }
   ```

#### Deliverables:
- ✅ Complete main agent implementation
- ✅ System instructions and prompts
- ✅ Response schema validation
- ✅ Agent configuration documentation

### Task 2.2: Core Tools Implementation
**Duration**: 5 days | **Priority**: Critical | **Dependencies**: Task 2.1

#### Subtasks:
1. **Connection Validation Tool**
   ```python
   # jenkins_agent/tools/connection_tools.py
   async def validate_jenkins_connection(
       jenkins_url: str,
       tool_context: ToolContext
   ) -> Dict[str, Any]:
       """Validate Jenkins connection and retrieve server info."""
       
       credentials = await get_jenkins_credentials(tool_context)
       
       try:
           jenkins_client = JenkinsClientWrapper(jenkins_url, credentials)
           version = await jenkins_client.get_version()
           user_info = await jenkins_client.get_whoami()
           
           return {
               "status": "connected",
               "version": version,
               "user": user_info,
               "timestamp": datetime.utcnow().isoformat()
           }
       except Exception as e:
           await log_security_event(
               event_type="jenkins_connection_failed",
               user=tool_context.auth_context.user_identity,
               error=str(e)
           )
           raise ConnectionError(f"Failed to connect to Jenkins: {e}")
   ```

2. **Job Information Tool**
   ```python
   # jenkins_agent/tools/job_tools.py
   async def get_jenkins_jobs(
       job_filter: Optional[str] = None,
       include_config: bool = False,
       tool_context: ToolContext = None
   ) -> List[Dict[str, Any]]:
       """Retrieve Jenkins job information with filtering."""
       
       await validate_request_permissions(tool_context, "jobs.read")
       
       credentials = await get_jenkins_credentials(tool_context)
       jenkins_client = JenkinsClientWrapper(tool_context.jenkins_url, credentials)
       
       try:
           jobs = await jenkins_client.get_jobs()
           
           if job_filter:
               jobs = [job for job in jobs if job_filter.lower() in job['name'].lower()]
           
           enhanced_jobs = []
           for job in jobs:
               job_info = {
                   "name": job['name'],
                   "url": job['url'],
                   "color": job.get('color', 'unknown'),
                   "buildable": job.get('buildable', False)
               }
               
               if include_config:
                   config = await jenkins_client.get_job_config(job['name'])
                   job_info['config'] = sanitize_job_config(config)
               
               enhanced_jobs.append(job_info)
           
           await log_audit_event(
               event_type="jenkins_jobs_accessed",
               user=tool_context.auth_context.user_identity,
               job_count=len(enhanced_jobs)
           )
           
           return enhanced_jobs
           
       except Exception as e:
           await log_error_event(
               event_type="jenkins_jobs_error",
               user=tool_context.auth_context.user_identity,
               error=str(e)
           )
           raise
   ```

3. **Build History Tool**
   ```python
   # jenkins_agent/tools/build_tools.py
   async def get_build_history(
       job_name: str,
       max_builds: int = 50,
       include_details: bool = False,
       tool_context: ToolContext = None
   ) -> List[Dict[str, Any]]:
       """Retrieve build history for specific job."""
       
       if max_builds > 200:
           raise ValidationError("max_builds cannot exceed 200")
       
       await validate_request_permissions(tool_context, "builds.read")
       
       credentials = await get_jenkins_credentials(tool_context)
       jenkins_client = JenkinsClientWrapper(tool_context.jenkins_url, credentials)
       
       try:
           job_info = await jenkins_client.get_job_info(job_name)
           builds = job_info.get('builds', [])[:max_builds]
           
           build_history = []
           for build in builds:
               build_number = build['number']
               build_info = await jenkins_client.get_build_info(job_name, build_number)
               
               build_data = {
                   "number": build_number,
                   "result": build_info.get('result'),
                   "timestamp": build_info.get('timestamp'),
                   "duration": build_info.get('duration'),
                   "url": build_info.get('url')
               }
               
               if include_details:
                   build_data.update({
                       "parameters": extract_build_parameters(build_info),
                       "artifacts": extract_build_artifacts(build_info),
                       "test_results": extract_test_results(build_info)
                   })
               
               build_history.append(build_data)
           
           return build_history
           
       except Exception as e:
           await log_error_event(
               event_type="jenkins_build_history_error",
               user=tool_context.auth_context.user_identity,
               job_name=job_name,
               error=str(e)
           )
           raise
   ```

#### Deliverables:
- ✅ All core tools implemented with error handling
- ✅ Input validation and sanitization
- ✅ Comprehensive logging and monitoring
- ✅ Tool documentation and examples

## Testing Strategy

### Unit Testing
```bash
# Run unit tests
poetry run pytest tests/unit/ -v --cov=jenkins_agent

# Test specific components
poetry run pytest tests/unit/test_tools.py -v
poetry run pytest tests/unit/test_auth.py -v
```

### Integration Testing
```bash
# Test with real Jenkins instance
poetry run pytest tests/integration/ -v \
  --jenkins-url=https://jenkins.truxt.ai/ \
  --jenkins-user=admin \
  --jenkins-password=Truxt@2025
```

### Security Testing
```bash
# Run security tests
poetry run pytest tests/security/ -v
poetry run bandit -r jenkins_agent/
```

## Success Criteria

### Functional Requirements
- ✅ Connect to Jenkins servers securely
- ✅ Extract comprehensive job and build data
- ✅ Analyze dependencies and relationships
- ✅ Generate structured reports
- ✅ Maintain read-only access

### Performance Requirements
- ✅ Response time < 5 seconds
- ✅ Support 100+ concurrent users
- ✅ 99.9% availability
- ✅ Auto-scaling capabilities

### Security Requirements
- ✅ Role-based access control
- ✅ Secure credential management
- ✅ Comprehensive audit logging
- ✅ Input validation and sanitization

---

This task breakdown provides a clear roadmap for implementing the Jenkins Reader Agent with enterprise-grade security and scalability.
