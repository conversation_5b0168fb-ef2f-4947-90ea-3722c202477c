"""
Jenkins Reader Agent - A comprehensive Jenkins read-only agent built using Google's ADK.

This package provides secure, scalable access to Jenkins CI/CD infrastructure data
while maintaining enterprise-grade security standards.
"""

__version__ = "0.1.0"
__author__ = "Jenkins Reader Agent Team"

from .agent import jenkins_agent
from .config.settings import Settings

__all__ = ["jenkins_agent", "Settings"]
