"""Main Jenkins Read-Only Agent implementation using Google ADK."""

from typing import Any, Dict, Optional

from google.adk.agents import Agent
from google.adk.tools import ToolContext
from google.genai.types import GenerateContentConfig

# from .config.schemas import Jenkins<PERSON><PERSON>po<PERSON>  # Not needed for ADK agent
from .config.settings import get_settings
from .prompts import (
    jenkins_system_instructions,
    jenkins_global_context,
    format_response_guidelines
)
from .tools.connection_tools import validate_jenkins_connection, get_jenkins_server_status
from .tools.job_tools import get_jenkins_jobs, get_job_config
from .tools.build_tools import get_build_history, get_artifacts
from .utils.logging import (
    get_logger,
    log_audit_event,
    log_security_event,
    setup_logging
)
from .utils.validation import validate_request_permissions

# Initialize logging
setup_logging()
logger = get_logger(__name__)


async def security_validation_callback(callback_context) -> Optional[Any]:
    """
    Pre-execution security validation callback.
    
    This callback runs before each agent execution to validate security
    requirements and log access attempts.
    
    Args:
        callback_context: ADK callback context
        
    Returns:
        None to continue execution, or response to override
    """
    try:
        # Extract context information
        user_identity = callback_context.auth_context.user_identity
        agent_name = callback_context.invocation_context.agent.name
        
        logger.info(
            "Security validation started",
            extra={
                "agent": agent_name,
                "user_id": getattr(user_identity, 'user_id', 'unknown')
            }
        )
        
        # Validate user authentication
        if not hasattr(user_identity, 'user_id') or not user_identity.user_id:
            await log_security_event(
                event_type="authentication_failed",
                severity="ERROR",
                agent=agent_name
            )
            logger.error("User not authenticated")
            return {
                "status": "error",
                "data": {"error": "User not authenticated"},
                "metadata": {"timestamp": "now", "total_records": 0},
                "errors": [{"code": "AUTHENTICATION_ERROR", "message": "User not authenticated"}]
            }
        
        # Check rate limiting (basic implementation)
        settings = get_settings()
        if settings.enable_rate_limiting:
            # TODO: Implement actual rate limiting logic
            # For now, just log the attempt
            logger.debug(
                "Rate limiting check passed",
                extra={"user_id": user_identity.user_id}
            )
        
        # Log access attempt for audit
        await log_audit_event(
            event_type="jenkins_agent_access",
            user=user_identity,
            agent=agent_name
        )
        
        logger.info(
            "Security validation passed",
            extra={
                "agent": agent_name,
                "user_id": user_identity.user_id
            }
        )
        
        # Return None to continue with normal execution
        return None
        
    except Exception as e:
        logger.error(
            "Security validation failed",
            extra={
                "error": str(e),
                "agent": getattr(callback_context.invocation_context.agent, 'name', 'unknown')
            }
        )
        
        await log_security_event(
            event_type="security_validation_error",
            severity="ERROR",
            error=str(e)
        )
        
        return {
            "status": "error",
            "data": {"error": "Security validation failed"},
            "metadata": {"timestamp": "now", "total_records": 0},
            "errors": [{"code": "SECURITY_ERROR", "message": "Security validation failed"}]
        }


async def audit_logging_callback(callback_context) -> Optional[Any]:
    """
    Post-execution audit logging callback.
    
    This callback runs after each agent execution to log the results
    and update usage metrics.
    
    Args:
        callback_context: ADK callback context
        
    Returns:
        None to continue with normal response
    """
    try:
        # Extract context information
        user_identity = callback_context.auth_context.user_identity
        agent_name = callback_context.invocation_context.agent.name
        response = callback_context.response
        
        # Calculate execution metrics
        execution_time = getattr(callback_context.invocation_context, 'execution_time', 0)
        response_size = len(str(response)) if response else 0
        
        # Determine operation status
        status = "unknown"
        if isinstance(response, dict):
            status = response.get("status", "unknown")
        
        # Log successful execution
        await log_audit_event(
            event_type="jenkins_agent_execution_complete",
            user=user_identity,
            agent=agent_name,
            execution_time_ms=execution_time,
            response_size_bytes=response_size,
            status=status
        )
        
        logger.info(
            "Agent execution completed",
            extra={
                "agent": agent_name,
                "user_id": getattr(user_identity, 'user_id', 'unknown'),
                "status": status,
                "execution_time_ms": execution_time,
                "response_size_bytes": response_size
            }
        )
        
        # Return None to continue with normal response
        return None
        
    except Exception as e:
        logger.error(
            "Audit logging failed",
            extra={
                "error": str(e),
                "agent": getattr(callback_context.invocation_context.agent, 'name', 'unknown')
            }
        )
        
        # Don't fail the request due to logging issues
        return None


# Create the main Jenkins Read-Only Agent
jenkins_agent = Agent(
    model="gemini-2.5-pro",
    name="jenkins_read_only_agent",
    instruction=jenkins_system_instructions(),
    global_instruction=jenkins_global_context(),
    tools=[
        # Connection and validation tools
        validate_jenkins_connection,
        get_jenkins_server_status,

        # Job management tools
        get_jenkins_jobs,
        get_job_config,

        # Build and artifact tools
        get_build_history,
        get_artifacts,
    ],
    before_agent_callback=security_validation_callback,
    after_agent_callback=audit_logging_callback
)


async def create_jenkins_agent_session(user_context: Optional[Dict[str, Any]] = None) -> Agent:
    """
    Create a Jenkins agent session with user-specific context.
    
    Args:
        user_context: Optional user-specific context and preferences
        
    Returns:
        Agent: Configured Jenkins agent instance
    """
    logger.info(
        "Creating Jenkins agent session",
        extra={"user_context": user_context}
    )
    
    # For now, return the main agent
    # In the future, this could customize the agent based on user context
    return jenkins_agent


def get_agent_info() -> Dict[str, Any]:
    """
    Get information about the Jenkins agent capabilities.
    
    Returns:
        Dict containing agent information and capabilities
    """
    return {
        "name": "Jenkins Read-Only Agent",
        "version": "0.1.0",
        "description": "Enterprise-grade Jenkins analysis and reporting agent",
        "model": "gemini-2.5-pro",
        "capabilities": [
            "Jenkins server connectivity validation",
            "Job listing and filtering",
            "Job configuration analysis",
            "Build history analysis",
            "Artifact management",
            "Performance monitoring",
            "Security auditing",
            "Dependency mapping"
        ],
        "tools": [
            {
                "name": "validate_jenkins_connection",
                "description": "Test connectivity and retrieve server info"
            },
            {
                "name": "get_jenkins_server_status",
                "description": "Get comprehensive server status and health"
            },
            {
                "name": "get_jenkins_jobs",
                "description": "List and filter Jenkins jobs"
            },
            {
                "name": "get_job_config",
                "description": "Retrieve job configurations"
            },
            {
                "name": "get_build_history",
                "description": "Analyze build history and trends"
            },
            {
                "name": "get_artifacts",
                "description": "Access build artifacts and metadata"
            }
        ],
        "security_features": [
            "Read-only access enforcement",
            "User authentication and authorization",
            "Comprehensive audit logging",
            "Input validation and sanitization",
            "Rate limiting and abuse prevention",
            "Secure credential management"
        ],
        "enterprise_features": [
            "Google Cloud IAM integration",
            "Structured logging and monitoring",
            "Auto-scaling and high availability",
            "Compliance and audit trails",
            "Performance optimization",
            "Error handling and recovery"
        ]
    }


# Export the main agent and utility functions
__all__ = [
    "jenkins_agent",
    "create_jenkins_agent_session",
    "get_agent_info",
    "security_validation_callback",
    "audit_logging_callback"
]
