"""Data schemas and validation models for Jenkins Reader Agent."""

from datetime import datetime
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field, HttpUrl, validator


class JenkinsCredentials(BaseModel):
    """Jenkins authentication credentials."""
    
    username: str = <PERSON>(..., description="<PERSON> username")
    password: str = Field(..., description="Jenkins password or API token")
    url: HttpUrl = Field(..., description="Jenkins server URL")
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            HttpUrl: str
        }


class JenkinsQueryRequest(BaseModel):
    """Request model for <PERSON> queries."""
    
    jenkins_url: HttpUrl = Field(..., description="Jenkins server URL")
    query_type: Literal["jobs", "builds", "config", "artifacts", "dependencies"] = Field(
        ..., description="Type of data to query"
    )
    filters: Optional[Dict[str, Any]] = Field(
        default=None, description="Optional filters for the query"
    )
    max_results: int = Field(
        default=100, le=1000, description="Maximum number of results to return"
    )
    include_details: bool = Field(
        default=False, description="Whether to include detailed information"
    )
    
    @validator('jenkins_url')
    def validate_jenkins_url(cls, v: HttpUrl) -> HttpUrl:
        """Validate Jenkins URL against allowed domains."""
        from .settings import validate_jenkins_url
        
        if not validate_jenkins_url(str(v)):
            from .settings import get_allowed_jenkins_domains
            allowed_domains = get_allowed_jenkins_domains()
            raise ValueError(f"Jenkins URL not in allowed domains: {allowed_domains}")
        return v
    
    @validator('max_results')
    def validate_max_results(cls, v: int) -> int:
        """Validate max results is within reasonable bounds."""
        if v < 1:
            raise ValueError("max_results must be at least 1")
        return v


class ErrorInfo(BaseModel):
    """Error information model."""
    
    code: str = Field(..., description="Error code")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional error details"
    )


class ResponseMetadata(BaseModel):
    """Response metadata model."""

    timestamp: str = Field(..., description="ISO format timestamp")
    jenkins_version: Optional[str] = Field(default=None)
    jenkins_url: Optional[str] = Field(default=None)
    data_freshness: Optional[str] = Field(default=None)
    total_records: int = Field(..., description="Total number of records")
    query_duration_ms: Optional[int] = Field(default=None)
    rate_limit_remaining: Optional[int] = Field(default=None)


class JenkinsResponse(BaseModel):
    """Standard response model for Jenkins operations."""
    
    status: Literal["success", "error", "warning", "partial"] = Field(
        ..., description="Response status"
    )
    data: Dict[str, Any] = Field(..., description="Response data")
    metadata: ResponseMetadata = Field(..., description="Response metadata")
    errors: List[ErrorInfo] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    @validator('data')
    def validate_data_not_empty_on_success(cls, v: Dict[str, Any], values: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure data is not empty for successful responses."""
        status = values.get('status')
        if status == 'success' and not v:
            raise ValueError("Data cannot be empty for successful responses")
        return v


class JobInfo(BaseModel):
    """Jenkins job information model."""
    
    name: str = Field(..., description="Job name")
    url: HttpUrl = Field(..., description="Job URL")
    description: Optional[str] = Field(default=None)
    buildable: bool = Field(default=True)
    color: str = Field(default="notbuilt")
    last_build_number: Optional[int] = Field(default=None)
    last_successful_build: Optional[int] = Field(default=None)
    last_failed_build: Optional[int] = Field(default=None)
    upstream_jobs: List[str] = Field(default_factory=list)
    downstream_jobs: List[str] = Field(default_factory=list)
    
    class Config:
        """Pydantic configuration."""
        json_encoders = {
            HttpUrl: str
        }


class BuildInfo(BaseModel):
    """Jenkins build information model."""

    number: int = Field(..., description="Build number")
    result: Optional[str] = Field(default=None)
    timestamp: Optional[str] = Field(default=None, description="ISO format timestamp")
    duration: Optional[int] = Field(default=None)
    url: HttpUrl = Field(..., description="Build URL")
    parameters: Dict[str, Any] = Field(default_factory=dict)
    artifacts: List[str] = Field(default_factory=list)
    test_results: Optional[Dict[str, Any]] = Field(default=None)

    class Config:
        """Pydantic configuration."""
        json_encoders = {
            HttpUrl: str
        }


class DependencyGraph(BaseModel):
    """Job dependency graph model."""
    
    job_name: str = Field(..., description="Target job name")
    upstream_jobs: List[str] = Field(default_factory=list)
    downstream_jobs: List[str] = Field(default_factory=list)
    dependency_depth: int = Field(default=1)
    circular_dependencies: List[str] = Field(default_factory=list)


# Response schema for ADK controlled generation
jenkins_response_schema = {
    "type": "object",
    "properties": {
        "status": {
            "type": "string",
            "enum": ["success", "error", "warning", "partial"],
            "description": "Response status indicator"
        },
        "data": {
            "type": "object",
            "description": "The requested Jenkins data payload"
        },
        "metadata": {
            "type": "object",
            "properties": {
                "timestamp": {
                    "type": "string",
                    "format": "date-time",
                    "description": "Response timestamp"
                },
                "jenkins_version": {
                    "type": "string",
                    "description": "Jenkins server version"
                },
                "jenkins_url": {
                    "type": "string",
                    "description": "Jenkins server URL"
                },
                "data_freshness": {
                    "type": "string",
                    "description": "Data freshness indicator"
                },
                "total_records": {
                    "type": "integer",
                    "description": "Total number of records returned"
                },
                "query_duration_ms": {
                    "type": "integer",
                    "description": "Query execution time in milliseconds"
                },
                "rate_limit_remaining": {
                    "type": "integer",
                    "description": "Remaining rate limit quota"
                }
            },
            "required": ["timestamp", "total_records"]
        },
        "errors": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "code": {"type": "string"},
                    "message": {"type": "string"},
                    "details": {"type": "object"}
                },
                "required": ["code", "message"]
            },
            "description": "List of errors encountered"
        },
        "warnings": {
            "type": "array",
            "items": {"type": "string"},
            "description": "List of warnings"
        }
    },
    "required": ["status", "data", "metadata"],
    "additionalProperties": False
}
