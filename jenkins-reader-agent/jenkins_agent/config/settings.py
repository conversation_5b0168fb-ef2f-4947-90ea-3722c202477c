"""Settings and configuration for Jenkins Reader Agent."""

import os
from typing import List, Optional

from pydantic import Field, validator, ConfigDict
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings with environment variable support."""

    # Google Cloud Configuration
    google_cloud_project: str = Field(default="test-project", env="GOOGLE_CLOUD_PROJECT")
    google_cloud_location: str = Field(default="us-central1", env="GOOGLE_CLOUD_LOCATION")
    
    # Jenkins Configuration
    jenkins_credentials_secret: str = Field(
        default="jenkins-credentials", env="JENKINS_CREDENTIALS_SECRET"
    )
    allowed_jenkins_domains: str = Field(
        default="jenkins.truxt.ai,localhost",
        env="ALLOWED_JENKINS_DOMAINS"
    )
    
    # Application Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    max_concurrent_requests: int = Field(default=10, env="MAX_CONCURRENT_REQUESTS")
    rate_limit_per_minute: int = Field(default=100, env="RATE_LIMIT_PER_MINUTE")
    
    # Security Configuration
    enable_audit_logging: bool = Field(default=True, env="ENABLE_AUDIT_LOGGING")
    enable_rate_limiting: bool = Field(default=True, env="ENABLE_RATE_LIMITING")
    max_results_per_query: int = Field(default=1000, env="MAX_RESULTS_PER_QUERY")
    
    # Performance Configuration
    connection_timeout: int = Field(default=30, env="CONNECTION_TIMEOUT")
    read_timeout: int = Field(default=60, env="READ_TIMEOUT")
    max_retries: int = Field(default=3, env="MAX_RETRIES")
    
    # Development Configuration
    debug: bool = Field(default=False, env="DEBUG")
    testing: bool = Field(default=False, env="TESTING")

    # Additional environment variables that might be present
    google_genai_use_vertexai: Optional[str] = Field(default=None, env="GOOGLE_GENAI_USE_VERTEXAI")
    jenkins_password: Optional[str] = Field(default=None, env="JENKINS_PASSWORD")
    
    @validator("log_level")
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is one of the allowed values."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v.upper()
    
    @validator("allowed_jenkins_domains")
    def validate_jenkins_domains(cls, v: str) -> str:
        """Validate Jenkins domains are properly formatted."""
        if not v or not v.strip():
            raise ValueError("At least one Jenkins domain must be specified")
        return v
    
    @validator("max_concurrent_requests")
    def validate_max_concurrent_requests(cls, v: int) -> int:
        """Validate max concurrent requests is reasonable."""
        if v < 1 or v > 100:
            raise ValueError("Max concurrent requests must be between 1 and 100")
        return v
    
    @validator("rate_limit_per_minute")
    def validate_rate_limit(cls, v: int) -> int:
        """Validate rate limit is reasonable."""
        if v < 1 or v > 1000:
            raise ValueError("Rate limit must be between 1 and 1000 requests per minute")
        return v

    model_config = ConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"  # Ignore extra environment variables
    )


# Global settings instance (lazy loaded)
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get the global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def is_development() -> bool:
    """Check if running in development mode."""
    s = get_settings()
    return s.debug or s.testing


def is_production() -> bool:
    """Check if running in production mode."""
    return not is_development()


def get_allowed_jenkins_domains() -> List[str]:
    """Get the list of allowed Jenkins domains."""
    domains_str = get_settings().allowed_jenkins_domains
    return [domain.strip() for domain in domains_str.split(",") if domain.strip()]


def validate_jenkins_url(url: str) -> bool:
    """Validate if a Jenkins URL is allowed."""
    from urllib.parse import urlparse
    
    parsed = urlparse(url)
    domain = parsed.hostname
    
    if not domain:
        return False
    
    return domain in get_allowed_jenkins_domains()
