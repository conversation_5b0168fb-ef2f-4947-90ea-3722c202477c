"""Build management tools for Jenkins Reader Agent."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.schemas import JenkinsResponse, ResponseMetadata, BuildInfo
from ..config.settings import get_settings
from ..utils.auth import get_jenkins_credentials
from ..utils.exceptions import ValidationError
from ..utils.logging import get_logger, log_audit_event, log_error_event
from ..utils.validation import validate_request_permissions, validate_input_parameters
from .jenkins_client import JenkinsClientWrapper

logger = get_logger(__name__)


async def get_build_history(
    job_name: str,
    max_builds: int = 50,
    include_details: bool = False,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Retrieve build history for a specific Jenkins job.
    
    This tool fetches the build history for a Jenkins job, including build results,
    timestamps, durations, and optionally detailed information like parameters,
    artifacts, and test results.
    
    Args:
        job_name: Name of the <PERSON> job
        max_builds: Maximum number of builds to retrieve (default: 50, max: 200)
        include_details: Whether to include detailed build information
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing build history, metadata, and any warnings/errors
        
    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting build history retrieval",
        extra={
            "job_name": job_name,
            "max_builds": max_builds,
            "include_details": include_details,
            "user_id": getattr(tool_context.auth_context.user_identity, 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    settings = get_settings()
    
    # Validate input parameters
    if not job_name or not job_name.strip():
        raise ValidationError(
            "Job name is required",
            field="job_name",
            value=job_name
        )
    
    job_name = job_name.strip()
    
    if max_builds <= 0 or max_builds > 200:
        raise ValidationError(
            "max_builds must be between 1 and 200",
            field="max_builds",
            value=max_builds
        )
    
    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")
    
    try:
        # Get Jenkins credentials and create client
        credentials = await get_jenkins_credentials(tool_context)
        
        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Get job information first
            logger.debug(f"Fetching job info for: {job_name}")
            job_info = await jenkins_client.get_job_info(job_name, depth=1)
            
            # Extract builds list
            builds_list = job_info.get('builds', [])
            if not builds_list:
                logger.info(f"No builds found for job: {job_name}")
                
                # Return empty result
                response_data = {
                    "job_name": job_name,
                    "builds": [],
                    "summary": {
                        "total_builds": 0,
                        "builds_returned": 0,
                        "latest_build": None,
                        "oldest_build": None
                    }
                }
                
                end_time = datetime.utcnow()
                response_time_ms = int((end_time - start_time).total_seconds() * 1000)
                
                metadata = ResponseMetadata(
                    timestamp=end_time,
                    jenkins_url=str(credentials.url),
                    total_records=0,
                    query_duration_ms=response_time_ms
                )
                
                return JenkinsResponse(
                    status="success",
                    data=response_data,
                    metadata=metadata,
                    warnings=["No builds found for this job"]
                ).dict()
            
            # Limit builds to requested amount
            limited_builds = builds_list[:max_builds]
            
            # Process builds
            processed_builds = []
            detail_errors = []
            
            for build_ref in limited_builds:
                try:
                    build_number = build_ref.get('number')
                    if not build_number:
                        continue
                    
                    # Get basic build info
                    build_info = await jenkins_client.get_build_info(job_name, build_number, depth=1)
                    
                    # Extract basic build data
                    build_data = {
                        "number": build_number,
                        "result": build_info.get('result'),
                        "timestamp": build_info.get('timestamp'),
                        "duration": build_info.get('duration'),
                        "url": build_info.get('url'),
                        "building": build_info.get('building', False),
                        "queue_id": build_info.get('queueId')
                    }
                    
                    # Convert timestamp to ISO format if available
                    if build_data["timestamp"]:
                        build_data["timestamp"] = datetime.fromtimestamp(
                            build_data["timestamp"] / 1000
                        ).isoformat()
                    
                    # Include detailed information if requested
                    if include_details:
                        try:
                            # Extract build parameters
                            actions = build_info.get('actions', [])
                            parameters = {}
                            for action in actions:
                                if action and 'parameters' in action:
                                    for param in action['parameters']:
                                        if 'name' in param and 'value' in param:
                                            parameters[param['name']] = param['value']
                            
                            build_data["parameters"] = parameters
                            
                            # Extract artifacts
                            artifacts = []
                            for artifact in build_info.get('artifacts', []):
                                artifacts.append({
                                    "filename": artifact.get('fileName'),
                                    "relative_path": artifact.get('relativePath'),
                                    "size": artifact.get('size')
                                })
                            build_data["artifacts"] = artifacts
                            
                            # Extract test results if available
                            test_results = None
                            for action in actions:
                                if action and 'totalCount' in action:
                                    test_results = {
                                        "total": action.get('totalCount', 0),
                                        "failed": action.get('failCount', 0),
                                        "skipped": action.get('skipCount', 0),
                                        "passed": action.get('totalCount', 0) - action.get('failCount', 0) - action.get('skipCount', 0)
                                    }
                                    break
                            build_data["test_results"] = test_results
                            
                            # Extract changeset information
                            changeset = build_info.get('changeSet', {})
                            if changeset and changeset.get('items'):
                                changes = []
                                for item in changeset['items'][:5]:  # Limit to 5 changes
                                    changes.append({
                                        "author": item.get('author', {}).get('fullName', 'Unknown'),
                                        "message": item.get('msg', ''),
                                        "timestamp": item.get('timestamp')
                                    })
                                build_data["changes"] = changes
                            else:
                                build_data["changes"] = []
                                
                        except Exception as detail_error:
                            logger.warning(
                                "Failed to get detailed build information",
                                extra={
                                    "job_name": job_name,
                                    "build_number": build_number,
                                    "error": str(detail_error)
                                }
                            )
                            detail_errors.append({
                                "build_number": build_number,
                                "error": str(detail_error)
                            })
                    
                    processed_builds.append(build_data)
                    
                except Exception as build_error:
                    logger.warning(
                        "Failed to process build",
                        extra={
                            "job_name": job_name,
                            "build_number": build_ref.get('number', 'unknown'),
                            "error": str(build_error)
                        }
                    )
                    detail_errors.append({
                        "build_number": build_ref.get('number', 'unknown'),
                        "error": str(build_error)
                    })
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Prepare summary statistics
            summary = {
                "total_builds": len(builds_list),
                "builds_returned": len(processed_builds),
                "latest_build": processed_builds[0]["number"] if processed_builds else None,
                "oldest_build": processed_builds[-1]["number"] if processed_builds else None,
                "detail_errors_count": len(detail_errors)
            }
            
            # Prepare response data
            response_data = {
                "job_name": job_name,
                "builds": processed_builds,
                "summary": summary
            }
            
            # Add detail errors if any
            if detail_errors:
                response_data["detail_errors"] = detail_errors
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=end_time,
                jenkins_url=str(credentials.url),
                total_records=len(processed_builds),
                query_duration_ms=response_time_ms
            )
            
            # Prepare warnings
            warnings = []
            if len(builds_list) > max_builds:
                warnings.append(f"Results truncated: showing {len(processed_builds)} of {len(builds_list)} builds")
            if detail_errors:
                warnings.append(f"Failed to get details for {len(detail_errors)} builds")
            
            # Log successful operation
            await log_audit_event(
                event_type="jenkins_build_history_retrieved",
                user=tool_context.auth_context.user_identity,
                jenkins_url=str(credentials.url),
                job_name=job_name,
                builds_count=len(processed_builds),
                include_details=include_details,
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Build history retrieval successful",
                extra={
                    "job_name": job_name,
                    "builds_returned": len(processed_builds),
                    "total_builds": len(builds_list),
                    "response_time_ms": response_time_ms,
                    "include_details": include_details
                }
            )
            
            # Determine response status
            status = "success"
            if detail_errors:
                status = "warning" if processed_builds else "error"
            
            return JenkinsResponse(
                status=status,
                data=response_data,
                metadata=metadata,
                warnings=warnings
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="jenkins_build_history_error",
            error=str(e),
            user=tool_context.auth_context.user_identity,
            job_name=job_name,
            max_builds=max_builds,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Build history retrieval failed",
            extra={
                "job_name": job_name,
                "error": str(e),
                "max_builds": max_builds,
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "job_name": job_name,
            "builds": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=end_time,
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {
                    "job_name": job_name,
                    "max_builds": max_builds
                }
            }]
        ).dict()


async def get_artifacts(
    job_name: str,
    build_number: Optional[int] = None,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Retrieve artifacts from a Jenkins build.

    This tool fetches artifacts from a specific build or the latest build
    of a Jenkins job, including artifact metadata and download information.

    Args:
        job_name: Name of the Jenkins job
        build_number: Specific build number (optional, uses latest if not provided)
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing artifacts information and metadata

    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting artifacts retrieval",
        extra={
            "job_name": job_name,
            "build_number": build_number,
            "user_id": getattr(tool_context.auth_context.user_identity, 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    if not job_name or not job_name.strip():
        raise ValidationError(
            "Job name is required",
            field="job_name",
            value=job_name
        )

    job_name = job_name.strip()

    if build_number is not None and build_number <= 0:
        raise ValidationError(
            "Build number must be positive",
            field="build_number",
            value=build_number
        )

    # Validate permissions
    await validate_request_permissions(tool_context, "builds.read")

    try:
        # Get Jenkins credentials and create client
        credentials = await get_jenkins_credentials(tool_context)

        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Get build number if not provided
            if build_number is None:
                logger.debug(f"Getting latest build for job: {job_name}")
                job_info = await jenkins_client.get_job_info(job_name)
                last_build = job_info.get('lastBuild')
                if not last_build:
                    # No builds available
                    response_data = {
                        "job_name": job_name,
                        "build_number": None,
                        "artifacts": [],
                        "summary": {
                            "total_artifacts": 0,
                            "total_size_bytes": 0
                        }
                    }

                    end_time = datetime.utcnow()
                    response_time_ms = int((end_time - start_time).total_seconds() * 1000)

                    metadata = ResponseMetadata(
                        timestamp=end_time,
                        jenkins_url=str(credentials.url),
                        total_records=0,
                        query_duration_ms=response_time_ms
                    )

                    return JenkinsResponse(
                        status="success",
                        data=response_data,
                        metadata=metadata,
                        warnings=["No builds found for this job"]
                    ).dict()

                build_number = last_build.get('number')

            # Get build information
            logger.debug(f"Fetching build info for job: {job_name}, build: {build_number}")
            build_info = await jenkins_client.get_build_info(job_name, build_number)

            # Extract artifacts
            artifacts_list = build_info.get('artifacts', [])

            # Process artifacts
            processed_artifacts = []
            total_size = 0

            for artifact in artifacts_list:
                artifact_data = {
                    "filename": artifact.get('fileName', ''),
                    "relative_path": artifact.get('relativePath', ''),
                    "size_bytes": artifact.get('size', 0),
                    "display_path": artifact.get('displayPath', ''),
                    "download_url": f"{build_info.get('url', '')}artifact/{artifact.get('relativePath', '')}"
                }

                # Add human-readable size
                size_bytes = artifact_data["size_bytes"]
                if size_bytes:
                    if size_bytes < 1024:
                        artifact_data["size_human"] = f"{size_bytes} B"
                    elif size_bytes < 1024 * 1024:
                        artifact_data["size_human"] = f"{size_bytes / 1024:.1f} KB"
                    elif size_bytes < 1024 * 1024 * 1024:
                        artifact_data["size_human"] = f"{size_bytes / (1024 * 1024):.1f} MB"
                    else:
                        artifact_data["size_human"] = f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"
                else:
                    artifact_data["size_human"] = "Unknown"

                processed_artifacts.append(artifact_data)
                total_size += size_bytes

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare summary
            summary = {
                "total_artifacts": len(processed_artifacts),
                "total_size_bytes": total_size,
                "build_result": build_info.get('result'),
                "build_timestamp": build_info.get('timestamp'),
                "build_duration": build_info.get('duration')
            }

            # Add human-readable total size
            if total_size:
                if total_size < 1024:
                    summary["total_size_human"] = f"{total_size} B"
                elif total_size < 1024 * 1024:
                    summary["total_size_human"] = f"{total_size / 1024:.1f} KB"
                elif total_size < 1024 * 1024 * 1024:
                    summary["total_size_human"] = f"{total_size / (1024 * 1024):.1f} MB"
                else:
                    summary["total_size_human"] = f"{total_size / (1024 * 1024 * 1024):.1f} GB"
            else:
                summary["total_size_human"] = "0 B"

            # Prepare response data
            response_data = {
                "job_name": job_name,
                "build_number": build_number,
                "artifacts": processed_artifacts,
                "summary": summary
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=end_time,
                jenkins_url=str(credentials.url),
                total_records=len(processed_artifacts),
                query_duration_ms=response_time_ms
            )

            # Log successful operation
            await log_audit_event(
                event_type="jenkins_artifacts_retrieved",
                user=tool_context.auth_context.user_identity,
                jenkins_url=str(credentials.url),
                job_name=job_name,
                build_number=build_number,
                artifacts_count=len(processed_artifacts),
                total_size_bytes=total_size,
                response_time_ms=response_time_ms
            )

            logger.info(
                "Artifacts retrieval successful",
                extra={
                    "job_name": job_name,
                    "build_number": build_number,
                    "artifacts_count": len(processed_artifacts),
                    "total_size_bytes": total_size,
                    "response_time_ms": response_time_ms
                }
            )

            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="jenkins_artifacts_error",
            error=str(e),
            user=tool_context.auth_context.user_identity,
            job_name=job_name,
            build_number=build_number,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Artifacts retrieval failed",
            extra={
                "job_name": job_name,
                "build_number": build_number,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "job_name": job_name,
            "build_number": build_number,
            "artifacts": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=end_time,
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {
                    "job_name": job_name,
                    "build_number": build_number
                }
            }]
        ).dict()
