"""Connection validation tools for Jenkins Reader Agent."""

from datetime import datetime
from typing import Any, Dict

from google.adk.tools import ToolContext

from ..config.schemas import JenkinsResponse, ResponseMetadata
from ..utils.auth import get_jenkins_credentials
from ..utils.exceptions import ConnectionError, ValidationError
from ..utils.logging import get_logger, log_audit_event, log_security_event
from .jenkins_client import JenkinsClientWrapper

logger = get_logger(__name__)


async def validate_jenkins_connection(
    jenkins_url: str,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Validate connection to Jenkins server and retrieve basic information.
    
    This tool tests connectivity to a Jenkins server, validates credentials,
    and returns server information including version and user details.
    
    Args:
        jenkins_url: Jenkins server URL to validate
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing connection status, server info, and metadata
        
    Raises:
        ValidationError: If jenkins_url is invalid
        ConnectionError: If connection to <PERSON> fails
        AuthenticationError: If credentials are invalid
    """
    logger.info(
        "Starting Jenkins connection validation",
        extra={
            "jenkins_url": jenkins_url,
            "user_id": getattr(tool_context.auth_context.user_identity, 'user_id', 'unknown')
        }
    )
    
    # Validate input parameters
    if not jenkins_url or not jenkins_url.strip():
        raise ValidationError(
            "Jenkins URL is required",
            field="jenkins_url",
            value=jenkins_url
        )
    
    # Validate URL format and domain
    from ..config.settings import validate_jenkins_url
    if not validate_jenkins_url(jenkins_url):
        from ..config.settings import get_allowed_jenkins_domains
        allowed_domains = get_allowed_jenkins_domains()
        await log_security_event(
            event_type="invalid_jenkins_url_attempted",
            user=tool_context.auth_context.user_identity,
            severity="WARNING",
            jenkins_url=jenkins_url,
            allowed_domains=allowed_domains
        )
        raise ValidationError(
            f"Jenkins URL not in allowed domains: {allowed_domains}",
            field="jenkins_url",
            value=jenkins_url
        )
    
    start_time = datetime.utcnow()
    
    try:
        # Get Jenkins credentials
        credentials = await get_jenkins_credentials(tool_context)
        
        # Verify the URL matches credentials
        if str(credentials.url) != jenkins_url:
            logger.warning(
                "Jenkins URL mismatch",
                extra={
                    "requested_url": jenkins_url,
                    "credentials_url": str(credentials.url)
                }
            )
            # For security, we'll use the URL from credentials
            jenkins_url = str(credentials.url)
        
        # Create Jenkins client and test connection
        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Test basic connectivity
            connection_successful = await jenkins_client.test_connection()
            
            if not connection_successful:
                raise ConnectionError(
                    "Failed to establish connection to Jenkins server",
                    jenkins_url=jenkins_url
                )
            
            # Get comprehensive server information
            server_info = await jenkins_client.get_server_info()
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Prepare response data
            response_data = {
                "connection_status": "success",
                "server_info": {
                    "url": jenkins_url,
                    "version": server_info.get("version"),
                    "user": server_info.get("user", {}),
                    "last_tested": server_info.get("last_tested")
                },
                "connection_details": {
                    "response_time_ms": response_time_ms,
                    "authentication_method": "username_password",
                    "ssl_enabled": jenkins_url.startswith("https://")
                }
            }
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=end_time,
                jenkins_version=server_info.get("version"),
                jenkins_url=jenkins_url,
                total_records=1,
                query_duration_ms=response_time_ms
            )
            
            # Log successful connection
            await log_audit_event(
                event_type="jenkins_connection_validated",
                user=tool_context.auth_context.user_identity,
                jenkins_url=jenkins_url,
                jenkins_version=server_info.get("version"),
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Jenkins connection validation successful",
                extra={
                    "jenkins_url": jenkins_url,
                    "jenkins_version": server_info.get("version"),
                    "response_time_ms": response_time_ms
                }
            )
            
            # Return structured response
            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log security event for connection failures
        await log_security_event(
            event_type="jenkins_connection_failed",
            user=tool_context.auth_context.user_identity,
            severity="ERROR",
            jenkins_url=jenkins_url,
            error=str(e),
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Jenkins connection validation failed",
            extra={
                "jenkins_url": jenkins_url,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "connection_status": "failed",
            "error": {
                "type": type(e).__name__,
                "message": str(e),
                "jenkins_url": jenkins_url
            },
            "connection_details": {
                "response_time_ms": response_time_ms,
                "ssl_enabled": jenkins_url.startswith("https://")
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=end_time,
            jenkins_url=jenkins_url,
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        # Return error response
        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"jenkins_url": jenkins_url}
            }]
        ).dict()


async def get_jenkins_server_status(
    jenkins_url: str,
    tool_context: ToolContext
) -> Dict[str, Any]:
    """
    Get comprehensive Jenkins server status and health information.
    
    Args:
        jenkins_url: Jenkins server URL
        tool_context: ADK tool context
        
    Returns:
        Dict containing server status and health metrics
    """
    logger.info(
        "Getting Jenkins server status",
        extra={"jenkins_url": jenkins_url}
    )
    
    start_time = datetime.utcnow()
    
    try:
        # Get credentials and create client
        credentials = await get_jenkins_credentials(tool_context)
        
        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Get server information
            server_info = await jenkins_client.get_server_info()
            
            # Get basic job count (for health check)
            jobs = await jenkins_client.get_all_jobs(depth=0)
            job_count = len(jobs) if jobs else 0
            
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            status_data = {
                "server_status": "healthy" if server_info.get("connection_status") == "connected" else "unhealthy",
                "server_info": server_info,
                "metrics": {
                    "total_jobs": job_count,
                    "response_time_ms": response_time_ms,
                    "uptime_check": "passed" if response_time_ms < 5000 else "slow"
                }
            }
            
            metadata = ResponseMetadata(
                timestamp=end_time,
                jenkins_version=server_info.get("version"),
                jenkins_url=jenkins_url,
                total_records=1,
                query_duration_ms=response_time_ms
            )
            
            await log_audit_event(
                event_type="jenkins_status_checked",
                user=tool_context.auth_context.user_identity,
                jenkins_url=jenkins_url,
                job_count=job_count,
                response_time_ms=response_time_ms
            )
            
            return JenkinsResponse(
                status="success",
                data=status_data,
                metadata=metadata
            ).dict()
            
    except Exception as e:
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        logger.error(
            "Failed to get Jenkins server status",
            extra={
                "jenkins_url": jenkins_url,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )
        
        error_data = {
            "server_status": "error",
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=end_time,
            jenkins_url=jenkins_url,
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"jenkins_url": jenkins_url}
            }]
        ).dict()
