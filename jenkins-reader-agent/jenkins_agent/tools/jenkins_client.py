"""Jenkins client wrapper with rate limiting and error handling."""

import asyncio
import time
from typing import Any, Dict, List, Optional
from urllib.parse import urljoin

import httpx
from asyncio_throttle import Throttler
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
)

from ..config.schemas import JenkinsCredentials
from ..config.settings import get_settings
from ..utils.exceptions import (
    ConnectionError,
    AuthenticationError,
    JenkinsAPIError,
    TimeoutError,
    RateLimitError,
)
from ..utils.logging import get_logger, log_performance_event

logger = get_logger(__name__)


class JenkinsClientWrapper:
    """
    Wrapper around python-jenkins with enhanced features:
    - Rate limiting
    - Retry logic
    - Connection pooling
    - Comprehensive error handling
    - Performance monitoring
    """

    def __init__(self, credentials: JenkinsCredentials):
        """
        Initialize Jenkins client wrapper.

        Args:
            credentials: Jenkins authentication credentials
        """
        self.credentials = credentials
        self.settings = get_settings()

        # Initialize HTTP client
        self._client: Optional[httpx.AsyncClient] = None

        # Rate limiting
        self._throttler = Throttler(
            rate_limit=self.settings.rate_limit_per_minute,
            period=60
        )

        # Connection tracking
        self._last_connection_test = 0
        self._connection_test_interval = 300  # 5 minutes

        logger.info(
            "Initialized Jenkins client wrapper",
            extra={
                "jenkins_url": str(credentials.url),
                "username": credentials.username,
                "rate_limit": self.settings.rate_limit_per_minute
            }
        )

    @property
    def client(self) -> httpx.AsyncClient:
        """Get or create HTTP client instance."""
        if self._client is None:
            self._client = httpx.AsyncClient(
                auth=(self.credentials.username, self.credentials.password),
                timeout=self.settings.connection_timeout,
                verify=True
            )
        return self._client

    async def _rate_limited_call(self, func, *args, **kwargs) -> Any:
        """Execute function call with rate limiting."""
        async with self._throttler:
            start_time = time.time()
            try:
                # Run in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, func, *args, **kwargs)
                
                # Log performance
                duration_ms = int((time.time() - start_time) * 1000)
                await log_performance_event(
                    operation=func.__name__,
                    duration_ms=duration_ms,
                    jenkins_url=str(self.credentials.url)
                )
                
                return result
                
            except Exception as e:
                duration_ms = int((time.time() - start_time) * 1000)
                logger.error(
                    "Jenkins API call failed",
                    extra={
                        "operation": func.__name__,
                        "duration_ms": duration_ms,
                        "error": str(e)
                    }
                )
                raise self._handle_jenkins_exception(e, func.__name__)

    def _handle_jenkins_exception(self, exception: Exception, operation: str) -> Exception:
        """Convert Jenkins exceptions to our custom exceptions."""
        error_msg = str(exception)
        
        if "401" in error_msg or "Unauthorized" in error_msg:
            return AuthenticationError(
                f"Authentication failed for operation: {operation}",
                username=self.credentials.username,
                details={"original_error": error_msg}
            )
        elif "403" in error_msg or "Forbidden" in error_msg:
            return AuthenticationError(
                f"Access forbidden for operation: {operation}",
                username=self.credentials.username,
                details={"original_error": error_msg}
            )
        elif "404" in error_msg or "Not Found" in error_msg:
            return JenkinsAPIError(
                f"Resource not found for operation: {operation}",
                status_code=404,
                response_body=error_msg
            )
        elif "timeout" in error_msg.lower():
            return TimeoutError(
                f"Timeout during operation: {operation}",
                timeout_seconds=self.settings.connection_timeout,
                details={"original_error": error_msg}
            )
        elif "connection" in error_msg.lower():
            return ConnectionError(
                f"Connection failed for operation: {operation}",
                jenkins_url=str(self.credentials.url),
                details={"original_error": error_msg}
            )
        else:
            return JenkinsAPIError(
                f"Jenkins API error during operation: {operation}",
                response_body=error_msg
            )

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    async def get_version(self) -> str:
        """Get Jenkins server version."""
        async with self._throttler:
            url = urljoin(str(self.credentials.url), "api/json")
            response = await self.client.get(url)
            response.raise_for_status()
            return response.headers.get("X-Jenkins", "unknown")

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    async def get_whoami(self) -> Dict[str, Any]:
        """Get current user information."""
        async with self._throttler:
            url = urljoin(str(self.credentials.url), "me/api/json")
            response = await self.client.get(url)
            response.raise_for_status()
            return response.json()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    async def get_all_jobs(self, depth: int = 1) -> List[Dict[str, Any]]:
        """Get all Jenkins jobs."""
        async with self._throttler:
            url = urljoin(str(self.credentials.url), f"api/json?depth={depth}")
            response = await self.client.get(url)
            response.raise_for_status()
            data = response.json()
            return data.get("jobs", [])

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    async def get_job_info(self, name: str, depth: int = 1) -> Dict[str, Any]:
        """Get detailed information about a specific job."""
        async with self._throttler:
            url = urljoin(str(self.credentials.url), f"job/{name}/api/json?depth={depth}")
            response = await self.client.get(url)
            response.raise_for_status()
            return response.json()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    async def get_job_config(self, name: str) -> str:
        """Get job configuration XML."""
        async with self._throttler:
            url = urljoin(str(self.credentials.url), f"job/{name}/config.xml")
            response = await self.client.get(url)
            response.raise_for_status()
            return response.text

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    async def get_build_info(self, name: str, number: int, depth: int = 1) -> Dict[str, Any]:
        """Get build information."""
        async with self._throttler:
            url = urljoin(str(self.credentials.url), f"job/{name}/{number}/api/json?depth={depth}")
            response = await self.client.get(url)
            response.raise_for_status()
            return response.json()

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((ConnectionError, TimeoutError))
    )
    async def get_build_console_output(self, name: str, number: int) -> str:
        """Get build console output."""
        async with self._throttler:
            url = urljoin(str(self.credentials.url), f"job/{name}/{number}/consoleText")
            response = await self.client.get(url)
            response.raise_for_status()
            return response.text

    async def test_connection(self) -> bool:
        """
        Test Jenkins connection and cache result.
        
        Returns:
            bool: True if connection is successful
        """
        current_time = time.time()
        
        # Use cached result if recent
        if (current_time - self._last_connection_test) < self._connection_test_interval:
            return True
        
        try:
            await self.get_version()
            self._last_connection_test = current_time
            logger.info("Jenkins connection test successful")
            return True
        except Exception as e:
            logger.warning(
                "Jenkins connection test failed",
                extra={"error": str(e)}
            )
            return False

    async def get_server_info(self) -> Dict[str, Any]:
        """Get comprehensive server information."""
        try:
            version = await self.get_version()
            user_info = await self.get_whoami()
            
            return {
                "version": version,
                "user": user_info,
                "url": str(self.credentials.url),
                "connection_status": "connected",
                "last_tested": time.time()
            }
        except Exception as e:
            return {
                "version": None,
                "user": None,
                "url": str(self.credentials.url),
                "connection_status": "failed",
                "error": str(e),
                "last_tested": time.time()
            }

    def get_job_url(self, job_name: str) -> str:
        """Get full URL for a job."""
        return urljoin(str(self.credentials.url), f"job/{job_name}/")

    def get_build_url(self, job_name: str, build_number: int) -> str:
        """Get full URL for a build."""
        return urljoin(str(self.credentials.url), f"job/{job_name}/{build_number}/")

    async def close(self) -> None:
        """Clean up resources."""
        if self._client:
            await self._client.aclose()
            self._client = None

        logger.info("Jenkins client wrapper closed")

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
