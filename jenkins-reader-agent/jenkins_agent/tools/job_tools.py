"""Job management tools for Jenkins Reader Agent."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.schemas import JenkinsResponse, ResponseMetadata, JobInfo
from ..config.settings import get_settings
from ..utils.auth import get_jenkins_credentials
from ..utils.exceptions import ValidationError
from ..utils.logging import get_logger, log_audit_event, log_error_event
from ..utils.validation import validate_request_permissions, validate_input_parameters
from .jenkins_client import Jenkins<PERSON><PERSON><PERSON>rapper
from .security_tools import sanitize_job_config


def _datetime_to_iso(dt: datetime) -> str:
    """Convert datetime to ISO format string for JSON serialization."""
    return dt.isoformat() + 'Z' if dt else None

logger = get_logger(__name__)


async def get_jenkins_jobs(
    job_filter: Optional[str] = None,
    include_config: bool = False,
    max_results: Optional[int] = None,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Retrieve Jenkins job information with optional filtering.
    
    This tool fetches a list of Jenkins jobs with optional filtering by name pattern.
    It can optionally include job configurations and supports result limiting.
    
    Args:
        job_filter: Optional filter pattern for job names (case-insensitive substring match)
        include_config: Whether to include job configuration details
        max_results: Maximum number of jobs to return (defaults to settings limit)
        tool_context: ADK tool context with authentication and session info
        
    Returns:
        Dict containing job list, metadata, and any warnings/errors
        
    Raises:
        ValidationError: If input parameters are invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting Jenkins jobs retrieval",
        extra={
            "job_filter": job_filter,
            "include_config": include_config,
            "max_results": max_results,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )
    
    start_time = datetime.utcnow()
    settings = get_settings()
    
    # Validate input parameters
    allowed_params = ["job_filter", "include_config", "max_results"]
    params = {
        "job_filter": job_filter,
        "include_config": include_config,
        "max_results": max_results
    }
    validated_params = validate_input_parameters(
        {k: v for k, v in params.items() if v is not None},
        allowed_params
    )
    
    # Set default max_results if not provided
    if max_results is None:
        max_results = settings.max_results_per_query
    
    # Validate max_results
    if max_results <= 0 or max_results > settings.max_results_per_query:
        raise ValidationError(
            f"max_results must be between 1 and {settings.max_results_per_query}",
            field="max_results",
            value=max_results
        )
    
    # Validate permissions
    required_permission = "config.read" if include_config else "jobs.read"
    await validate_request_permissions(tool_context, required_permission)
    
    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            # Check if this is a mock context (for testing)
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                # Use test credentials for mock context
                from ..utils.auth import create_test_credentials
                credentials = create_test_credentials()
            else:
                # Use real Secret Manager credentials
                credentials = await get_jenkins_credentials(tool_context)
        else:
            # For testing without context, use test credentials
            from ..utils.auth import create_test_credentials
            credentials = create_test_credentials()
        
        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Get all jobs from Jenkins
            logger.debug("Fetching jobs from Jenkins")
            raw_jobs = await jenkins_client.get_all_jobs(depth=1)
            
            # Apply filtering if specified
            filtered_jobs = raw_jobs
            if job_filter:
                job_filter_lower = job_filter.lower()
                filtered_jobs = [
                    job for job in raw_jobs 
                    if job_filter_lower in job.get('name', '').lower()
                ]
                logger.debug(
                    "Applied job filter",
                    extra={
                        "filter": job_filter,
                        "original_count": len(raw_jobs),
                        "filtered_count": len(filtered_jobs)
                    }
                )
            
            # Apply result limiting
            limited_jobs = filtered_jobs[:max_results]
            was_truncated = len(filtered_jobs) > max_results
            
            if was_truncated:
                logger.info(
                    "Results truncated due to limit",
                    extra={
                        "total_matches": len(filtered_jobs),
                        "returned_count": len(limited_jobs),
                        "max_results": max_results
                    }
                )
            
            # Process jobs and enhance with additional information
            enhanced_jobs = []
            config_errors = []
            
            for job in limited_jobs:
                try:
                    job_info = {
                        "name": job.get('name', ''),
                        "url": job.get('url', ''),
                        "description": job.get('description', ''),
                        "buildable": job.get('buildable', True),
                        "color": job.get('color', 'notbuilt'),
                        "last_build_number": None,
                        "last_successful_build": None,
                        "last_failed_build": None,
                        "upstream_jobs": [],
                        "downstream_jobs": []
                    }
                    
                    # Extract build information if available
                    if 'lastBuild' in job and job['lastBuild']:
                        job_info["last_build_number"] = job['lastBuild'].get('number')
                    
                    if 'lastSuccessfulBuild' in job and job['lastSuccessfulBuild']:
                        job_info["last_successful_build"] = job['lastSuccessfulBuild'].get('number')
                    
                    if 'lastFailedBuild' in job and job['lastFailedBuild']:
                        job_info["last_failed_build"] = job['lastFailedBuild'].get('number')
                    
                    # Extract upstream/downstream projects if available
                    if 'upstreamProjects' in job:
                        job_info["upstream_jobs"] = [
                            proj.get('name', '') for proj in job['upstreamProjects']
                        ]
                    
                    if 'downstreamProjects' in job:
                        job_info["downstream_jobs"] = [
                            proj.get('name', '') for proj in job['downstreamProjects']
                        ]
                    
                    # Include configuration if requested
                    if include_config:
                        try:
                            config_xml = await jenkins_client.get_job_config(job_info["name"])
                            job_info["config"] = sanitize_job_config(config_xml)
                        except Exception as config_error:
                            logger.warning(
                                "Failed to get job configuration",
                                extra={
                                    "job_name": job_info["name"],
                                    "error": str(config_error)
                                }
                            )
                            config_errors.append({
                                "job_name": job_info["name"],
                                "error": str(config_error)
                            })
                    
                    enhanced_jobs.append(job_info)
                    
                except Exception as job_error:
                    logger.warning(
                        "Failed to process job",
                        extra={
                            "job_name": job.get('name', 'unknown'),
                            "error": str(job_error)
                        }
                    )
                    config_errors.append({
                        "job_name": job.get('name', 'unknown'),
                        "error": str(job_error)
                    })
            
            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Prepare response data
            response_data = {
                "jobs": enhanced_jobs,
                "summary": {
                    "total_jobs_found": len(filtered_jobs),
                    "jobs_returned": len(enhanced_jobs),
                    "was_truncated": was_truncated,
                    "filter_applied": job_filter is not None,
                    "config_included": include_config,
                    "config_errors_count": len(config_errors)
                }
            }
            
            # Add config errors if any
            if config_errors:
                response_data["config_errors"] = config_errors
            
            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_version=None,  # Will be filled by client if available
                jenkins_url=str(credentials.url),
                total_records=len(enhanced_jobs),
                query_duration_ms=response_time_ms
            )
            
            # Prepare warnings
            warnings = []
            if was_truncated:
                warnings.append(f"Results truncated: showing {len(enhanced_jobs)} of {len(filtered_jobs)} jobs")
            if config_errors:
                warnings.append(f"Failed to retrieve configuration for {len(config_errors)} jobs")
            
            # Log successful operation
            await log_audit_event(
                event_type="jenkins_jobs_retrieved",
                user=user_identity,
                jenkins_url=str(credentials.url),
                jobs_count=len(enhanced_jobs),
                filter_applied=job_filter,
                config_included=include_config,
                response_time_ms=response_time_ms
            )
            
            logger.info(
                "Jenkins jobs retrieval successful",
                extra={
                    "jobs_returned": len(enhanced_jobs),
                    "total_found": len(filtered_jobs),
                    "response_time_ms": response_time_ms,
                    "config_included": include_config
                }
            )
            
            # Determine response status
            status = "success"
            if config_errors:
                status = "warning" if enhanced_jobs else "error"
            
            return JenkinsResponse(
                status=status,
                data=response_data,
                metadata=metadata,
                warnings=warnings
            ).dict()
            
    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)
        
        # Log error event
        await log_error_event(
            event_type="jenkins_jobs_retrieval_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            job_filter=job_filter,
            include_config=include_config,
            response_time_ms=response_time_ms
        )
        
        logger.error(
            "Jenkins jobs retrieval failed",
            extra={
                "error": str(e),
                "job_filter": job_filter,
                "include_config": include_config,
                "response_time_ms": response_time_ms
            }
        )
        
        # Prepare error response
        error_data = {
            "jobs": [],
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }
        
        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )
        
        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {
                    "job_filter": job_filter,
                    "include_config": include_config
                }
            }]
        ).dict()


async def get_job_config(
    job_name: str,
    sanitize: bool = True,
    tool_context: ToolContext = None
) -> Dict[str, Any]:
    """
    Retrieve and optionally sanitize Jenkins job configuration.

    This tool fetches the configuration for a specific Jenkins job and can
    optionally sanitize it to remove sensitive information.

    Args:
        job_name: Name of the Jenkins job
        sanitize: Whether to sanitize the configuration (default: True)
        tool_context: ADK tool context with authentication and session info

    Returns:
        Dict containing job configuration and metadata

    Raises:
        ValidationError: If job_name is invalid
        AuthorizationError: If user lacks required permissions
    """
    logger.info(
        "Starting job configuration retrieval",
        extra={
            "job_name": job_name,
            "sanitize": sanitize,
            "user_id": getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown')
        }
    )

    start_time = datetime.utcnow()

    # Validate input parameters
    if not job_name or not job_name.strip():
        raise ValidationError(
            "Job name is required",
            field="job_name",
            value=job_name
        )

    job_name = job_name.strip()

    # Validate permissions
    await validate_request_permissions(tool_context, "config.read")

    try:
        # Get Jenkins credentials and create client
        auth_context = getattr(tool_context, 'auth_context', None)
        user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None

        if tool_context and auth_context and user_identity:
            # Check if this is a mock context (for testing)
            if hasattr(user_identity, 'user_id') and user_identity.user_id == 'test_user':
                # Use test credentials for mock context
                from ..utils.auth import create_test_credentials
                credentials = create_test_credentials()
            else:
                # Use real Secret Manager credentials
                credentials = await get_jenkins_credentials(tool_context)
        else:
            # For testing without context, use test credentials
            from ..utils.auth import create_test_credentials
            credentials = create_test_credentials()

        async with JenkinsClientWrapper(credentials) as jenkins_client:
            # Get job configuration
            logger.debug(f"Fetching configuration for job: {job_name}")
            config_xml = await jenkins_client.get_job_config(job_name)

            # Process configuration
            if sanitize:
                config_data = sanitize_job_config(config_xml)
                config_format = "sanitized"
            else:
                config_data = {"raw_xml": config_xml}
                config_format = "raw"

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)

            # Prepare response data
            response_data = {
                "job_name": job_name,
                "configuration": config_data,
                "format": config_format,
                "size_bytes": len(config_xml)
            }

            # Create response metadata
            metadata = ResponseMetadata(
                timestamp=_datetime_to_iso(end_time),
                jenkins_url=str(credentials.url),
                total_records=1,
                query_duration_ms=response_time_ms
            )

            # Log successful operation
            await log_audit_event(
                event_type="jenkins_job_config_retrieved",
                user=user_identity,
                jenkins_url=str(credentials.url),
                job_name=job_name,
                sanitized=sanitize,
                config_size=len(config_xml),
                response_time_ms=response_time_ms
            )

            logger.info(
                "Job configuration retrieval successful",
                extra={
                    "job_name": job_name,
                    "config_size": len(config_xml),
                    "sanitized": sanitize,
                    "response_time_ms": response_time_ms
                }
            )

            return JenkinsResponse(
                status="success",
                data=response_data,
                metadata=metadata
            ).dict()

    except Exception as e:
        # Calculate response time for failed requests
        end_time = datetime.utcnow()
        response_time_ms = int((end_time - start_time).total_seconds() * 1000)

        # Log error event
        await log_error_event(
            event_type="jenkins_job_config_error",
            error=str(e),
            user=getattr(getattr(getattr(tool_context, 'auth_context', None), 'user_identity', None), 'user_id', 'unknown'),
            job_name=job_name,
            response_time_ms=response_time_ms
        )

        logger.error(
            "Job configuration retrieval failed",
            extra={
                "job_name": job_name,
                "error": str(e),
                "response_time_ms": response_time_ms
            }
        )

        # Prepare error response
        error_data = {
            "job_name": job_name,
            "error": {
                "type": type(e).__name__,
                "message": str(e)
            }
        }

        metadata = ResponseMetadata(
            timestamp=_datetime_to_iso(end_time),
            total_records=0,
            query_duration_ms=response_time_ms
        )

        return JenkinsResponse(
            status="error",
            data=error_data,
            metadata=metadata,
            errors=[{
                "code": type(e).__name__,
                "message": str(e),
                "details": {"job_name": job_name}
            }]
        ).dict()
