"""Security tools for Jenkins Reader Agent."""

from typing import Any, Dict

from google.adk.tools import ToolContext

from ..utils.validation import validate_request_permissions as _validate_permissions
from ..utils.validation import sanitize_job_config as _sanitize_config

# Re-export validation functions as tools
async def validate_request_permissions(
    tool_context: ToolContext,
    required_permission: str
) -> bool:
    """
    Validate user permissions for Jenkins operations.
    
    Args:
        tool_context: ADK tool context
        required_permission: Required permission string
        
    Returns:
        bool: True if user has permission
    """
    return await _validate_permissions(tool_context, required_permission)


def sanitize_job_config(config_xml: str) -> Dict[str, Any]:
    """
    Sanitize Jenkins job configuration.
    
    Args:
        config_xml: Raw job configuration XML
        
    Returns:
        Dict: Sanitized configuration data
    """
    return _sanitize_config(config_xml)
