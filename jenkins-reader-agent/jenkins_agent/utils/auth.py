"""Authentication utilities for Jenkins Reader Agent."""

import json
from typing import Op<PERSON>

from google.cloud import secretmanager
from google.adk.tools import ToolContext

from ..config.schemas import JenkinsCredentials
from ..config.settings import get_settings
from .exceptions import AuthenticationError, ConfigurationError, UnauthorizedError
from .logging import get_logger

logger = get_logger(__name__)


async def get_jenkins_credentials(
    tool_context: Optional[ToolContext] = None,
    project_id: Optional[str] = None,
    secret_name: Optional[str] = None
) -> JenkinsCredentials:
    """
    Securely retrieve Jenkins credentials from Google Secret Manager.
    
    Args:
        tool_context: ADK tool context (optional)
        project_id: Google Cloud project ID (optional, uses settings if not provided)
        secret_name: Secret name (optional, uses settings if not provided)
        
    Returns:
        JenkinsCredentials: Validated Jenkins credentials
        
    Raises:
        AuthenticationError: If credentials cannot be retrieved or are invalid
        UnauthorizedError: If user lacks access to credentials
        ConfigurationError: If configuration is invalid
    """
    settings = get_settings()
    
    # Use provided values or fall back to settings
    project_id = project_id or settings.google_cloud_project
    secret_name = secret_name or settings.jenkins_credentials_secret
    
    if not project_id:
        raise ConfigurationError("Google Cloud project ID not configured")
    
    if not secret_name:
        raise ConfigurationError("Jenkins credentials secret name not configured")
    
    # Validate user permissions if tool context is provided
    if tool_context and hasattr(tool_context, 'auth_context'):
        user_identity = tool_context.auth_context.user_identity
        if not await has_jenkins_access(user_identity):
            logger.warning(
                "User lacks Jenkins access permissions",
                extra={"user_id": user_identity.user_id}
            )
            raise UnauthorizedError("User lacks Jenkins access permissions")

    # Build the secret name
    secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

    try:
        # Create Secret Manager client
        client = secretmanager.SecretManagerServiceClient()
        
        logger.info(
            "Retrieving Jenkins credentials from Secret Manager",
            extra={"secret_path": secret_path}
        )
        
        # Access the secret
        response = client.access_secret_version(request={"name": secret_path})
        secret_data = response.payload.data.decode("UTF-8")
        
        # Parse and validate credentials
        try:
            credentials_dict = json.loads(secret_data)
            credentials = JenkinsCredentials(**credentials_dict)
            
            logger.info(
                "Successfully retrieved Jenkins credentials",
                extra={
                    "jenkins_url": str(credentials.url),
                    "username": credentials.username
                }
            )
            
            return credentials
            
        except json.JSONDecodeError as e:
            logger.error(
                "Failed to parse Jenkins credentials JSON",
                extra={"error": str(e)}
            )
            raise AuthenticationError(
                "Invalid credentials format in Secret Manager",
                error_code="INVALID_FORMAT",
                details={"parse_error": str(e)}
            )
        except Exception as e:
            logger.error(
                "Failed to validate Jenkins credentials",
                extra={"error": str(e)}
            )
            raise AuthenticationError(
                "Invalid credentials in Secret Manager",
                error_code="VALIDATION_ERROR",
                details={"validation_error": str(e)}
            )
            
    except Exception as e:
        if isinstance(e, (AuthenticationError, UnauthorizedError, ConfigurationError)):
            raise
        
        logger.error(
            "Failed to retrieve Jenkins credentials from Secret Manager",
            extra={"error": str(e), "secret_path": secret_path}
        )
        raise AuthenticationError(
            "Failed to retrieve Jenkins credentials",
            error_code="SECRET_MANAGER_ERROR",
            details={"secret_manager_error": str(e)}
        )


async def validate_credentials(credentials: JenkinsCredentials) -> bool:
    """
    Validate Jenkins credentials by attempting a connection.

    Args:
        credentials: Jenkins credentials to validate

    Returns:
        bool: True if credentials are valid, False otherwise
    """
    try:
        from ..tools.jenkins_client import JenkinsClientWrapper

        # Create Jenkins client wrapper
        async with JenkinsClientWrapper(credentials) as client:
            # Test connection by getting version
            version = await client.get_version()

            logger.info(
                "Successfully validated Jenkins credentials",
                extra={
                    "jenkins_url": str(credentials.url),
                    "jenkins_version": version
                }
            )

            return True

    except Exception as e:
        logger.warning(
            "Failed to validate Jenkins credentials",
            extra={
                "jenkins_url": str(credentials.url),
                "error": str(e)
            }
        )
        return False


async def has_jenkins_access(user_identity) -> bool:
    """
    Check if user has access to Jenkins resources.
    
    Args:
        user_identity: User identity from ADK auth context
        
    Returns:
        bool: True if user has access, False otherwise
    """
    # TODO: Implement actual permission checking logic
    # This would typically check against Google Cloud IAM or custom RBAC
    
    # For now, return True for all authenticated users
    # In production, this should check specific permissions
    if hasattr(user_identity, 'user_id') and user_identity.user_id:
        logger.debug(
            "Checking Jenkins access for user",
            extra={"user_id": user_identity.user_id}
        )
        return True
    
    return False


def create_test_credentials() -> JenkinsCredentials:
    """
    Create test credentials for development/testing.
    
    Returns:
        JenkinsCredentials: Test credentials
    """
    return JenkinsCredentials(
        username="admin",
        password="Truxt@2025",
        url="https://jenkins.truxt.ai/"
    )
