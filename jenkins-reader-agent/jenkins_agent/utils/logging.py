"""Logging utilities for Jenkins Reader Agent."""

import logging
import sys
import time
from typing import Any, Dict, Optional

import structlog
from google.cloud import logging as cloud_logging

from ..config.settings import get_settings, is_production


def setup_logging() -> None:
    """Set up structured logging with Google Cloud Logging integration."""
    settings = get_settings()
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if is_production() else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, settings.log_level),
    )
    
    # Set up Google Cloud Logging in production
    if is_production() and not settings.testing:
        try:
            client = cloud_logging.Client(project=settings.google_cloud_project)
            client.setup_logging()
            
            # Create a Cloud Logging handler
            handler = client.get_default_handler()
            
            # Add the handler to the root logger
            root_logger = logging.getLogger()
            root_logger.addHandler(handler)
            
        except Exception as e:
            # Fall back to console logging if Cloud Logging setup fails
            logging.warning(f"Failed to setup Google Cloud Logging: {e}")


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        structlog.stdlib.BoundLogger: Configured logger instance
    """
    return structlog.get_logger(name)


async def log_audit_event(
    event_type: str,
    user: Optional[Any] = None,
    **kwargs: Any
) -> None:
    """
    Log an audit event for compliance and monitoring.
    
    Args:
        event_type: Type of audit event
        user: User identity (optional)
        **kwargs: Additional event data
    """
    logger = get_logger("audit")
    
    audit_data = {
        "event_type": event_type,
        "timestamp": time.time(),
        **kwargs
    }
    
    if user and hasattr(user, 'user_id'):
        audit_data["user_id"] = user.user_id
    
    logger.info("Audit event", **audit_data)


async def log_security_event(
    event_type: str,
    user: Optional[Any] = None,
    severity: str = "WARNING",
    **kwargs: Any
) -> None:
    """
    Log a security event for monitoring and alerting.
    
    Args:
        event_type: Type of security event
        user: User identity (optional)
        severity: Event severity level
        **kwargs: Additional event data
    """
    logger = get_logger("security")
    
    security_data = {
        "event_type": event_type,
        "severity": severity,
        "timestamp": time.time(),
        **kwargs
    }
    
    if user and hasattr(user, 'user_id'):
        security_data["user_id"] = user.user_id
    
    # Log at appropriate level based on severity
    if severity == "CRITICAL":
        logger.critical("Security event", **security_data)
    elif severity == "ERROR":
        logger.error("Security event", **security_data)
    elif severity == "WARNING":
        logger.warning("Security event", **security_data)
    else:
        logger.info("Security event", **security_data)


async def log_error_event(
    event_type: str,
    error: str,
    user: Optional[Any] = None,
    **kwargs: Any
) -> None:
    """
    Log an error event for debugging and monitoring.
    
    Args:
        event_type: Type of error event
        error: Error message or description
        user: User identity (optional)
        **kwargs: Additional event data
    """
    logger = get_logger("error")
    
    error_data = {
        "event_type": event_type,
        "error": error,
        "timestamp": time.time(),
        **kwargs
    }
    
    if user and hasattr(user, 'user_id'):
        error_data["user_id"] = user.user_id
    
    logger.error("Error event", **error_data)


async def log_performance_event(
    operation: str,
    duration_ms: int,
    **kwargs: Any
) -> None:
    """
    Log a performance event for monitoring and optimization.
    
    Args:
        operation: Operation name
        duration_ms: Operation duration in milliseconds
        **kwargs: Additional performance data
    """
    logger = get_logger("performance")
    
    performance_data = {
        "operation": operation,
        "duration_ms": duration_ms,
        "timestamp": time.time(),
        **kwargs
    }
    
    logger.info("Performance event", **performance_data)


class ContextLogger:
    """Context manager for logging with additional context."""
    
    def __init__(self, logger: structlog.stdlib.BoundLogger, **context: Any):
        self.logger = logger
        self.context = context
        self.bound_logger: Optional[structlog.stdlib.BoundLogger] = None
    
    def __enter__(self) -> structlog.stdlib.BoundLogger:
        self.bound_logger = self.logger.bind(**self.context)
        return self.bound_logger
    
    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        if exc_type is not None and self.bound_logger:
            self.bound_logger.error(
                "Exception in context",
                exc_type=exc_type.__name__,
                exc_val=str(exc_val)
            )


def with_context(logger: structlog.stdlib.BoundLogger, **context: Any) -> ContextLogger:
    """
    Create a context logger with additional context.
    
    Args:
        logger: Base logger
        **context: Additional context to bind
        
    Returns:
        ContextLogger: Context manager for logging
    """
    return ContextLogger(logger, **context)
