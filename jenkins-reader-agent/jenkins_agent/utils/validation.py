"""Validation utilities for Jenkins Reader Agent."""

import re
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Optional

from google.adk.tools import ToolContext

from ..config.settings import get_settings
from ..utils.exceptions import AuthorizationError, ValidationError, SecurityError
from ..utils.logging import get_logger, log_security_event

logger = get_logger(__name__)


async def validate_request_permissions(
    tool_context: ToolContext,
    required_permission: str
) -> bool:
    """
    Validate that the user has the required permissions for the operation.
    
    Args:
        tool_context: ADK tool context with user authentication
        required_permission: Required permission (e.g., "jobs.read", "builds.read")
        
    Returns:
        bool: True if user has permission
        
    Raises:
        AuthorizationError: If user lacks required permissions
    """
    # Safe access to auth context
    auth_context = getattr(tool_context, 'auth_context', None)
    user_identity = getattr(auth_context, 'user_identity', None) if auth_context else None
    user_id = getattr(user_identity, 'user_id', 'unknown') if user_identity else 'unknown'

    logger.debug(
        "Validating user permissions",
        extra={
            "required_permission": required_permission,
            "user_id": user_id
        }
    )
    
    # TODO: Implement actual permission checking logic
    # This would typically check against Google Cloud IAM or custom RBAC
    
    # For now, we'll implement basic permission validation
    # user_identity already defined above

    # Handle CLI mode where there's no user identity
    if not user_identity or not hasattr(user_identity, 'user_id') or not user_identity.user_id:
        # In CLI mode or testing, allow operations without user identity
        settings = get_settings()
        if settings.debug or settings.testing:
            logger.debug(
                "Permission granted (CLI/testing mode - no user identity)",
                extra={
                    "required_permission": required_permission,
                    "mode": "cli_testing"
                }
            )
            return True

        await log_security_event(
            event_type="permission_check_failed_no_user",
            severity="ERROR",
            required_permission=required_permission
        )
        raise AuthorizationError(
            "User identity not found",
            required_permission=required_permission
        )
    
    # Basic permission mapping (in production, this would be more sophisticated)
    permission_hierarchy = {
        "jobs.read": ["jenkins.viewer", "jenkins.admin"],
        "builds.read": ["jenkins.viewer", "jenkins.admin"],
        "config.read": ["jenkins.admin"],
        "system.read": ["jenkins.admin"]
    }
    
    # For development/testing, allow all authenticated users basic permissions
    settings = get_settings()
    if settings.debug or settings.testing:
        logger.debug(
            "Permission granted (development mode)",
            extra={
                "user_id": user_identity.user_id,
                "required_permission": required_permission
            }
        )
        return True
    
    # In production, implement actual permission checking
    # This would typically involve checking user roles against IAM or RBAC
    allowed_roles = permission_hierarchy.get(required_permission, [])
    
    # TODO: Replace with actual role checking
    # user_roles = await get_user_roles(user_identity)
    # has_permission = any(role in allowed_roles for role in user_roles)
    
    # For now, grant permission to all authenticated users
    has_permission = True
    
    if not has_permission:
        await log_security_event(
            event_type="permission_denied",
            user=user_identity,
            severity="WARNING",
            required_permission=required_permission,
            allowed_roles=allowed_roles
        )
        raise AuthorizationError(
            f"User lacks required permission: {required_permission}",
            required_permission=required_permission
        )
    
    logger.debug(
        "Permission validation successful",
        extra={
            "user_id": user_identity.user_id,
            "required_permission": required_permission
        }
    )
    
    return True


def sanitize_job_config(config_xml: str) -> Dict[str, Any]:
    """
    Sanitize Jenkins job configuration by removing sensitive information.
    
    Args:
        config_xml: Raw Jenkins job configuration XML
        
    Returns:
        Dict containing sanitized configuration data
        
    Raises:
        SecurityError: If configuration contains security violations
        ValidationError: If XML is malformed
    """
    logger.debug("Sanitizing job configuration")
    
    try:
        # Parse XML
        root = ET.fromstring(config_xml)
        
        # Extract basic job information
        job_config = {
            "job_type": root.tag,
            "description": "",
            "disabled": False,
            "parameters": [],
            "triggers": [],
            "build_steps": [],
            "publishers": [],
            "scm": {},
            "properties": []
        }
        
        # Extract description
        description_elem = root.find("description")
        if description_elem is not None and description_elem.text:
            job_config["description"] = description_elem.text.strip()
        
        # Check if job is disabled
        disabled_elem = root.find("disabled")
        if disabled_elem is not None:
            job_config["disabled"] = disabled_elem.text.lower() == "true"
        
        # Extract parameters
        params_elem = root.find(".//parameterDefinitions")
        if params_elem is not None:
            for param in params_elem:
                param_info = {
                    "type": param.tag,
                    "name": param.find("name").text if param.find("name") is not None else "",
                    "description": param.find("description").text if param.find("description") is not None else "",
                    "default_value": param.find("defaultValue").text if param.find("defaultValue") is not None else ""
                }
                # Sanitize sensitive parameter values
                if _is_sensitive_parameter(param_info["name"]):
                    param_info["default_value"] = "[REDACTED]"
                
                job_config["parameters"].append(param_info)
        
        # Extract triggers
        triggers_elem = root.find("triggers")
        if triggers_elem is not None:
            for trigger in triggers_elem:
                trigger_info = {
                    "type": trigger.tag,
                    "spec": trigger.find("spec").text if trigger.find("spec") is not None else ""
                }
                job_config["triggers"].append(trigger_info)
        
        # Extract SCM information (sanitized)
        scm_elem = root.find("scm")
        if scm_elem is not None:
            job_config["scm"] = {
                "type": scm_elem.get("class", ""),
                "url": _sanitize_scm_url(scm_elem.find(".//url").text if scm_elem.find(".//url") is not None else ""),
                "branches": [branch.find("name").text for branch in scm_elem.findall(".//hudson.plugins.git.BranchSpec") if branch.find("name") is not None]
            }
        
        # Extract build steps (sanitized)
        builders_elem = root.find("builders")
        if builders_elem is not None:
            for builder in builders_elem:
                build_step = {
                    "type": builder.tag,
                    "command": _sanitize_build_command(builder.find("command").text if builder.find("command") is not None else "")
                }
                job_config["build_steps"].append(build_step)
        
        # Extract publishers
        publishers_elem = root.find("publishers")
        if publishers_elem is not None:
            for publisher in publishers_elem:
                publisher_info = {
                    "type": publisher.tag
                }
                job_config["publishers"].append(publisher_info)
        
        # Extract properties
        properties_elem = root.find("properties")
        if properties_elem is not None:
            for prop in properties_elem:
                job_config["properties"].append({
                    "type": prop.tag
                })
        
        logger.debug(
            "Job configuration sanitized successfully",
            extra={
                "job_type": job_config["job_type"],
                "parameters_count": len(job_config["parameters"]),
                "triggers_count": len(job_config["triggers"])
            }
        )
        
        return job_config
        
    except ET.ParseError as e:
        logger.error(
            "Failed to parse job configuration XML",
            extra={"error": str(e)}
        )
        raise ValidationError(
            "Invalid XML configuration",
            details={"parse_error": str(e)}
        )
    except Exception as e:
        logger.error(
            "Error sanitizing job configuration",
            extra={"error": str(e)}
        )
        raise SecurityError(
            "Configuration sanitization failed",
            details={"error": str(e)}
        )


def _is_sensitive_parameter(param_name: str) -> bool:
    """Check if a parameter name indicates sensitive data."""
    sensitive_patterns = [
        r"password",
        r"secret",
        r"token",
        r"key",
        r"credential",
        r"auth",
        r"api[_-]?key",
        r"private[_-]?key"
    ]
    
    param_lower = param_name.lower()
    return any(re.search(pattern, param_lower) for pattern in sensitive_patterns)


def _sanitize_scm_url(url: str) -> str:
    """Sanitize SCM URL by removing credentials."""
    if not url:
        return ""
    
    # Remove credentials from URLs
    # Pattern: https://username:<EMAIL>/repo
    url = re.sub(r"://[^@/]+@", "://[REDACTED]@", url)
    
    return url


def _sanitize_build_command(command: str) -> str:
    """Sanitize build commands by removing sensitive information."""
    if not command:
        return ""
    
    # Redact common sensitive patterns
    sensitive_patterns = [
        (r"--password[=\s]+\S+", "--password=[REDACTED]"),
        (r"--token[=\s]+\S+", "--token=[REDACTED]"),
        (r"--api-key[=\s]+\S+", "--api-key=[REDACTED]"),
        (r"-p\s+\S+", "-p [REDACTED]"),
        (r"export\s+\w*(?:PASSWORD|SECRET|TOKEN|KEY)\w*=\S+", "export [REDACTED]=[REDACTED]")
    ]
    
    sanitized_command = command
    for pattern, replacement in sensitive_patterns:
        sanitized_command = re.sub(pattern, replacement, sanitized_command, flags=re.IGNORECASE)
    
    return sanitized_command


def validate_input_parameters(
    parameters: Dict[str, Any],
    allowed_params: List[str],
    required_params: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Validate input parameters against allowed and required lists.
    
    Args:
        parameters: Input parameters to validate
        allowed_params: List of allowed parameter names
        required_params: List of required parameter names
        
    Returns:
        Dict of validated parameters
        
    Raises:
        ValidationError: If validation fails
    """
    validated = {}
    required_params = required_params or []
    
    # Check for unknown parameters
    unknown_params = set(parameters.keys()) - set(allowed_params)
    if unknown_params:
        raise ValidationError(
            f"Unknown parameters: {list(unknown_params)}",
            details={"unknown_params": list(unknown_params), "allowed_params": allowed_params}
        )
    
    # Check for missing required parameters
    missing_params = set(required_params) - set(parameters.keys())
    if missing_params:
        raise ValidationError(
            f"Missing required parameters: {list(missing_params)}",
            details={"missing_params": list(missing_params), "required_params": required_params}
        )
    
    # Validate and copy allowed parameters
    for param in allowed_params:
        if param in parameters:
            validated[param] = parameters[param]
    
    return validated
