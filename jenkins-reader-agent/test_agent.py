#!/usr/bin/env python3
"""
Simple test script for Jenkins Reader Agent.

This script tests the basic functionality of the Jenkins Reader Agent
without requiring full ADK setup.
"""

import asyncio
import json
import sys
from datetime import datetime
from typing import Any, Dict

# Add the project root to Python path
sys.path.insert(0, '.')

from jenkins_agent.utils.auth import create_test_credentials, validate_credentials
from jenkins_agent.tools.jenkins_client import JenkinsClientWrapper
from jenkins_agent.tools.connection_tools import validate_jenkins_connection
from jenkins_agent.tools.job_tools import get_jenkins_jobs
from jenkins_agent.config.settings import get_settings
from jenkins_agent.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


class MockToolContext:
    """Mock tool context for testing without full ADK setup."""
    
    def __init__(self):
        self.auth_context = MockAuthContext()


class MockAuthContext:
    """Mock authentication context."""
    
    def __init__(self):
        self.user_identity = MockUserIdentity()


class MockUserIdentity:
    """Mock user identity."""
    
    def __init__(self):
        self.user_id = "test_user"
        self.email = "<EMAIL>"


async def test_jenkins_connection():
    """Test basic Jenkins connection."""
    print("🔍 Testing Jenkins connection...")
    
    try:
        # Create test credentials
        credentials = create_test_credentials()
        print(f"✅ Created test credentials for: {credentials.url}")
        
        # Test credential validation
        is_valid = await validate_credentials(credentials)
        print(f"✅ Credential validation: {'PASSED' if is_valid else 'FAILED'}")
        
        if not is_valid:
            print("❌ Cannot proceed with invalid credentials")
            return False
        
        # Test Jenkins client wrapper
        async with JenkinsClientWrapper(credentials) as client:
            version = await client.get_version()
            user_info = await client.get_whoami()
            
            print(f"✅ Jenkins version: {version}")
            print(f"✅ Connected as: {user_info.get('fullName', 'Unknown')}")
            
            return True
            
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False


async def test_connection_tool():
    """Test the connection validation tool."""
    print("\n🔧 Testing connection validation tool...")
    
    try:
        mock_context = MockToolContext()
        
        # Test with test Jenkins URL
        result = await validate_jenkins_connection(
            tool_context=mock_context,
            jenkins_url="https://jenkins.truxt.ai/"
        )
        
        print(f"✅ Connection tool result: {result['status']}")
        
        if result['status'] == 'success':
            server_info = result['data']['server_info']
            print(f"✅ Server version: {server_info.get('version', 'Unknown')}")
            print(f"✅ Response time: {result['metadata']['query_duration_ms']}ms")
        else:
            print(f"❌ Connection tool failed: {result.get('errors', [])}")
            
        return result['status'] == 'success'
        
    except Exception as e:
        print(f"❌ Connection tool test failed: {e}")
        return False


async def test_jobs_tool():
    """Test the jobs listing tool."""
    print("\n📋 Testing jobs listing tool...")
    
    try:
        mock_context = MockToolContext()
        
        # Test getting jobs list
        result = await get_jenkins_jobs(
            job_filter=None,
            include_config=False,
            max_results=10,
            tool_context=mock_context
        )
        
        print(f"✅ Jobs tool result: {result['status']}")
        
        if result['status'] in ['success', 'warning']:
            jobs_data = result['data']
            jobs_count = len(jobs_data.get('jobs', []))
            print(f"✅ Found {jobs_count} jobs")
            
            # Show first few jobs
            for i, job in enumerate(jobs_data.get('jobs', [])[:3]):
                print(f"  {i+1}. {job['name']} - {job['color']}")
                
            if result.get('warnings'):
                print(f"⚠️  Warnings: {result['warnings']}")
                
        else:
            print(f"❌ Jobs tool failed: {result.get('errors', [])}")
            
        return result['status'] in ['success', 'warning']
        
    except Exception as e:
        print(f"❌ Jobs tool test failed: {e}")
        return False


async def test_settings():
    """Test configuration settings."""
    print("\n⚙️  Testing configuration settings...")
    
    try:
        settings = get_settings()
        
        print(f"✅ Log level: {settings.log_level}")
        print(f"✅ Max concurrent requests: {settings.max_concurrent_requests}")
        print(f"✅ Rate limit per minute: {settings.rate_limit_per_minute}")
        print(f"✅ Allowed Jenkins domains: {settings.allowed_jenkins_domains}")
        print(f"✅ Debug mode: {settings.debug}")
        
        return True
        
    except Exception as e:
        print(f"❌ Settings test failed: {e}")
        return False


async def run_comprehensive_test():
    """Run comprehensive test suite."""
    print("🚀 Starting Jenkins Reader Agent Test Suite")
    print("=" * 50)
    
    test_results = []
    
    # Test 1: Settings
    result = await test_settings()
    test_results.append(("Settings", result))
    
    # Test 2: Basic connection
    result = await test_jenkins_connection()
    test_results.append(("Jenkins Connection", result))
    
    # Test 3: Connection tool
    if test_results[-1][1]:  # Only if connection works
        result = await test_connection_tool()
        test_results.append(("Connection Tool", result))
        
        # Test 4: Jobs tool
        if result:  # Only if connection tool works
            result = await test_jobs_tool()
            test_results.append(("Jobs Tool", result))
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:20} {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Jenkins Reader Agent is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total


async def main():
    """Main test function."""
    try:
        success = await run_comprehensive_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
