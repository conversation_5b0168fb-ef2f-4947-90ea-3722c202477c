#!/usr/bin/env python3
"""
Simple test script to verify auth_context fix is working.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from jenkins_agent.tools.job_tools import get_jenkins_jobs
from jenkins_agent.utils.validation import validate_request_permissions
from google.adk.tools import ToolContext


class MockToolContext:
    """Mock ToolContext for testing without auth_context."""
    
    def __init__(self):
        # Intentionally don't set auth_context to test the fix
        pass


async def test_auth_context_fix():
    """Test that tools handle missing auth_context gracefully."""
    
    print("🧪 Testing auth_context fix...")
    
    # Create mock context without auth_context
    mock_context = MockToolContext()
    
    try:
        # Test 1: Validation function
        print("📋 Test 1: Testing validate_request_permissions...")
        result = await validate_request_permissions(mock_context, "jobs.read")
        print(f"✅ validate_request_permissions passed: {result}")
        
    except Exception as e:
        print(f"❌ validate_request_permissions failed: {e}")
        return False
    
    try:
        # Test 2: Job tools function
        print("📋 Test 2: Testing get_jenkins_jobs...")
        result = await get_jenkins_jobs(tool_context=mock_context)
        print(f"✅ get_jenkins_jobs passed (status: {result.get('status', 'unknown')})")
        
    except AttributeError as e:
        if "auth_context" in str(e):
            print(f"❌ get_jenkins_jobs still has auth_context error: {e}")
            return False
        else:
            print(f"✅ get_jenkins_jobs passed (different error expected): {e}")
    except Exception as e:
        print(f"✅ get_jenkins_jobs passed (different error expected): {e}")
    
    print("🎉 All auth_context tests passed!")
    return True


if __name__ == "__main__":
    print("🚀 Starting auth_context fix test...")
    
    # Set environment variables for testing
    os.environ["JENKINS_TESTING"] = "true"
    os.environ["JENKINS_DEBUG"] = "true"
    
    success = asyncio.run(test_auth_context_fix())
    
    if success:
        print("✅ AUTH_CONTEXT FIX VERIFIED!")
        sys.exit(0)
    else:
        print("❌ AUTH_CONTEXT FIX FAILED!")
        sys.exit(1)
