#!/usr/bin/env python3
"""
Comprehensive test script for Jenkins Reader Agent.

This script tests all functionality including:
- Job listing and filtering
- Build history retrieval
- Build status analysis
- Configuration extraction
- Error handling
"""

import asyncio
import json
import sys
from datetime import datetime
from typing import Any, Dict, List

# Add the project root to Python path
sys.path.insert(0, '.')

from jenkins_agent.utils.auth import create_test_credentials
from jenkins_agent.tools.jenkins_client import JenkinsClientWrapper
from jenkins_agent.tools.connection_tools import validate_jenkins_connection
from jenkins_agent.tools.job_tools import get_jenkins_jobs, get_job_config
from jenkins_agent.tools.build_tools import get_build_history, get_artifacts
from jenkins_agent.config.settings import get_settings
from jenkins_agent.utils.logging import setup_logging, get_logger

# Setup logging
setup_logging()
logger = get_logger(__name__)


class MockToolContext:
    """Mock tool context for testing without full ADK setup."""
    
    def __init__(self):
        self.auth_context = MockAuthContext()


class MockAuthContext:
    """Mock authentication context."""
    
    def __init__(self):
        self.user_identity = MockUserIdentity()


class MockUserIdentity:
    """Mock user identity."""
    
    def __init__(self):
        self.user_id = "test_user"
        self.email = "<EMAIL>"


async def test_job_details():
    """Test detailed job information retrieval."""
    print("\n🔍 Testing detailed job information...")
    
    try:
        credentials = create_test_credentials()
        
        async with JenkinsClientWrapper(credentials) as client:
            # Get all jobs first
            jobs = await client.get_all_jobs(depth=2)
            print(f"✅ Found {len(jobs)} total jobs")
            
            if jobs:
                # Test with the first job
                job = jobs[0]
                job_name = job['name']
                print(f"📋 Analyzing job: {job_name}")
                
                # Get detailed job info
                job_info = await client.get_job_info(job_name, depth=2)
                print(f"   📊 Job status: {job_info.get('color', 'unknown')}")
                print(f"   🔗 Job URL: {job_info.get('url', 'N/A')}")
                print(f"   📝 Description: {job_info.get('description', 'No description')[:100]}...")
                
                # Check if job has builds
                builds = job_info.get('builds', [])
                if builds:
                    print(f"   🏗️  Total builds: {len(builds)}")
                    
                    # Get latest build info
                    latest_build = builds[0]
                    build_number = latest_build['number']
                    
                    build_info = await client.get_build_info(job_name, build_number, depth=2)
                    print(f"   🔢 Latest build: #{build_number}")
                    print(f"   ✅ Build result: {build_info.get('result', 'IN_PROGRESS')}")
                    print(f"   ⏱️  Duration: {build_info.get('duration', 0) / 1000:.1f}s")
                    
                    # Get build timestamp
                    timestamp = build_info.get('timestamp', 0)
                    if timestamp:
                        build_time = datetime.fromtimestamp(timestamp / 1000)
                        print(f"   📅 Build time: {build_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    
                    # Check for artifacts
                    artifacts = build_info.get('artifacts', [])
                    if artifacts:
                        print(f"   📦 Artifacts: {len(artifacts)} files")
                        for artifact in artifacts[:3]:
                            print(f"      - {artifact.get('fileName', 'unknown')}")
                    
                    # Get console output (first 500 chars)
                    try:
                        console_output = await client.get_build_console_output(job_name, build_number)
                        print(f"   📜 Console output preview: {console_output[:200]}...")
                    except Exception as e:
                        print(f"   ⚠️  Console output not available: {e}")
                
                else:
                    print("   ℹ️  No builds found for this job")
                
                return True
            else:
                print("❌ No jobs found")
                return False
                
    except Exception as e:
        print(f"❌ Job details test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_build_analysis():
    """Test build history and status analysis."""
    print("\n📊 Testing build history analysis...")
    
    try:
        mock_context = MockToolContext()
        
        # Get jobs first
        jobs_result = await get_jenkins_jobs(
            job_filter=None,
            include_config=False,
            max_results=5,
            tool_context=mock_context
        )
        
        if jobs_result['status'] != 'success':
            print("❌ Failed to get jobs for build analysis")
            return False
        
        jobs = jobs_result['data']['jobs']
        
        for job in jobs[:2]:  # Test first 2 jobs
            job_name = job['name']
            print(f"\n🔍 Analyzing builds for: {job_name}")
            
            try:
                # Get build history
                build_result = await get_build_history(
                    job_name=job_name,
                    max_builds=5,
                    include_details=True,
                    tool_context=mock_context
                )
                
                if build_result['status'] == 'success':
                    builds = build_result['data']['builds']
                    print(f"   📈 Found {len(builds)} recent builds")
                    
                    # Analyze build results
                    success_count = sum(1 for b in builds if b.get('result') == 'SUCCESS')
                    failure_count = sum(1 for b in builds if b.get('result') == 'FAILURE')
                    
                    print(f"   ✅ Successful: {success_count}")
                    print(f"   ❌ Failed: {failure_count}")
                    
                    if builds:
                        latest = builds[0]
                        print(f"   🔢 Latest build: #{latest.get('number')}")
                        print(f"   📊 Status: {latest.get('result', 'IN_PROGRESS')}")
                        
                        # Check for artifacts
                        if latest.get('artifacts'):
                            print(f"   📦 Has artifacts: {len(latest['artifacts'])}")
                
                else:
                    print(f"   ⚠️  Build history not available: {build_result.get('errors', [])}")
                    
            except Exception as e:
                print(f"   ❌ Build analysis failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Build analysis test failed: {e}")
        return False


async def test_configuration_extraction():
    """Test job configuration extraction."""
    print("\n⚙️  Testing configuration extraction...")
    
    try:
        mock_context = MockToolContext()
        
        # Get a job to test configuration
        jobs_result = await get_jenkins_jobs(
            job_filter=None,
            include_config=False,
            max_results=3,
            tool_context=mock_context
        )
        
        if jobs_result['status'] != 'success':
            print("❌ Failed to get jobs for config test")
            return False
        
        jobs = jobs_result['data']['jobs']
        
        for job in jobs[:1]:  # Test first job
            job_name = job['name']
            print(f"🔧 Extracting config for: {job_name}")
            
            try:
                config_result = await get_job_config(
                    job_name=job_name,
                    tool_context=mock_context
                )
                
                if config_result['status'] == 'success':
                    config_data = config_result['data']
                    print(f"   ✅ Config extracted successfully")
                    print(f"   📄 Config size: {len(config_data.get('raw_config', ''))} characters")
                    
                    # Check parsed elements
                    parsed = config_data.get('parsed_config', {})
                    if parsed:
                        print(f"   🔍 Job type: {parsed.get('job_type', 'unknown')}")
                        print(f"   🌿 SCM configured: {bool(parsed.get('scm'))}")
                        print(f"   🔨 Build steps: {len(parsed.get('build_steps', []))}")
                        print(f"   📧 Publishers: {len(parsed.get('publishers', []))}")
                
                else:
                    print(f"   ⚠️  Config extraction failed: {config_result.get('errors', [])}")
                    
            except Exception as e:
                print(f"   ❌ Config extraction error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def run_comprehensive_test():
    """Run comprehensive test suite."""
    print("🚀 Jenkins Reader Agent - Comprehensive Test Suite")
    print("=" * 60)
    
    test_results = []
    
    # Test 1: Basic functionality (from previous test)
    print("📋 Running basic functionality tests...")
    from test_agent import test_settings, test_jenkins_connection, test_connection_tool, test_jobs_tool
    
    basic_tests = [
        ("Settings", await test_settings()),
        ("Jenkins Connection", await test_jenkins_connection()),
        ("Connection Tool", await test_connection_tool()),
        ("Jobs Tool", await test_jobs_tool()),
    ]
    test_results.extend(basic_tests)
    
    # Test 2: Advanced functionality
    print("\n🔬 Running advanced functionality tests...")
    advanced_tests = [
        ("Job Details", await test_job_details()),
        ("Build Analysis", await test_build_analysis()),
        ("Configuration Extraction", await test_configuration_extraction()),
    ]
    test_results.extend(advanced_tests)
    
    # Print comprehensive summary
    print("\n" + "=" * 60)
    print("📊 Comprehensive Test Results Summary:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, success in test_results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:25} {status}")
        if success:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Jenkins Reader Agent is fully functional!")
        print("✅ Ready for production deployment")
    else:
        print(f"\n⚠️  {total-passed} tests failed. Review the output above for details.")
    
    return passed == total


async def main():
    """Main test function."""
    try:
        success = await run_comprehensive_test()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
