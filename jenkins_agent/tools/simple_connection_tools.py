"""Simplified connection tools that bypass complex authentication."""

import os
import jenkins
from typing import Dict, Any
from google.adk.tools import ToolContext
from ..utils.logging import get_logger

logger = get_logger(__name__)


async def test_jenkins_connection_simple(tool_context: ToolContext = None) -> Dict[str, Any]:
    """
    Simple Jenkins connection test using environment variables.
    
    Args:
        tool_context: ADK tool context (optional, not used for auth)
        
    Returns:
        Dict with connection status and server info
    """
    logger.info("Testing Jenkins connection with environment variables")
    
    try:
        # Get credentials from environment
        jenkins_url = os.getenv('JENKINS_URL')
        jenkins_username = os.getenv('JENKINS_USERNAME')
        jenkins_token = os.getenv('JENKINS_API_TOKEN')
        
        if not all([jenkins_url, jenkins_username, jenkins_token]):
            return {
                "success": False,
                "error": "Missing Jenkins environment variables",
                "details": {
                    "jenkins_url": bool(jenkins_url),
                    "jenkins_username": bool(jenkins_username),
                    "jenkins_token": bool(jenkins_token)
                }
            }
        
        # Create Jenkins client
        server = jen<PERSON><PERSON>(
            jenkins_url,
            username=jenkins_username,
            password=jenkins_token
        )
        
        # Test connection by getting version and user info
        version = server.get_version()
        user_info = server.get_whoami()
        
        logger.info(f"Successfully connected to Jenkins {version}")
        
        return {
            "success": True,
            "version": version,
            "server_info": {
                "url": jenkins_url,
                "version": version,
                "user": user_info,
                "connection_method": "environment_variables"
            },
            "connection_details": {
                "authentication_method": "username_password",
                "ssl_enabled": jenkins_url.startswith("https://")
            }
        }
        
    except Exception as e:
        logger.error(f"Jenkins connection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "jenkins_url": os.getenv('JENKINS_URL', 'unknown')
        }


async def list_jenkins_jobs_simple(tool_context: ToolContext = None) -> Dict[str, Any]:
    """
    Simple Jenkins job listing using environment variables.
    
    Args:
        tool_context: ADK tool context (optional, not used for auth)
        
    Returns:
        Dict with job list and metadata
    """
    logger.info("Listing Jenkins jobs with environment variables")
    
    try:
        # Get credentials from environment
        jenkins_url = os.getenv('JENKINS_URL')
        jenkins_username = os.getenv('JENKINS_USERNAME')
        jenkins_token = os.getenv('JENKINS_API_TOKEN')
        
        if not all([jenkins_url, jenkins_username, jenkins_token]):
            return {
                "success": False,
                "error": "Missing Jenkins environment variables",
                "jobs": []
            }
        
        # Create Jenkins client
        server = jenkins.Jenkins(
            jenkins_url,
            username=jenkins_username,
            password=jenkins_token
        )
        
        # Get all jobs
        jobs = server.get_all_jobs()
        
        # Format job information
        formatted_jobs = []
        for job in jobs:
            formatted_jobs.append({
                "name": job.get("name", "Unknown"),
                "url": job.get("url", ""),
                "color": job.get("color", "unknown"),
                "status": _color_to_status(job.get("color", "unknown")),
                "buildable": job.get("buildable", False),
                "description": job.get("description", "")
            })
        
        logger.info(f"Found {len(formatted_jobs)} Jenkins jobs")
        
        return {
            "success": True,
            "jobs": formatted_jobs,
            "total_count": len(formatted_jobs),
            "server_info": {
                "url": jenkins_url,
                "job_count": len(formatted_jobs)
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to list Jenkins jobs: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "jobs": []
        }


async def get_job_info_simple(tool_context: ToolContext, job_name: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific Jenkins job.
    
    Args:
        tool_context: ADK tool context (optional, not used for auth)
        job_name: Name of the Jenkins job
        
    Returns:
        Dict with job information
    """
    logger.info(f"Getting info for job: {job_name}")
    
    try:
        # Get credentials from environment
        jenkins_url = os.getenv('JENKINS_URL')
        jenkins_username = os.getenv('JENKINS_USERNAME')
        jenkins_token = os.getenv('JENKINS_API_TOKEN')
        
        if not all([jenkins_url, jenkins_username, jenkins_token]):
            return {
                "success": False,
                "error": "Missing Jenkins environment variables",
                "job_name": job_name
            }
        
        # Create Jenkins client
        server = jenkins.Jenkins(
            jenkins_url,
            username=jenkins_username,
            password=jenkins_token
        )
        
        # Get job info
        job_info = server.get_job_info(job_name)
        
        # Get recent builds
        builds = []
        if job_info.get("builds"):
            for build in job_info["builds"][:5]:  # Last 5 builds
                build_number = build["number"]
                try:
                    build_info = server.get_build_info(job_name, build_number)
                    builds.append({
                        "number": build_number,
                        "result": build_info.get("result", "UNKNOWN"),
                        "timestamp": build_info.get("timestamp"),
                        "duration": build_info.get("duration"),
                        "url": build_info.get("url")
                    })
                except Exception as e:
                    logger.warning(f"Failed to get build info for {job_name}#{build_number}: {e}")
        
        formatted_info = {
            "name": job_info.get("name"),
            "description": job_info.get("description", ""),
            "url": job_info.get("url"),
            "buildable": job_info.get("buildable", False),
            "color": job_info.get("color", "unknown"),
            "status": _color_to_status(job_info.get("color", "unknown")),
            "last_build": job_info.get("lastBuild"),
            "last_successful_build": job_info.get("lastSuccessfulBuild"),
            "last_failed_build": job_info.get("lastFailedBuild"),
            "recent_builds": builds
        }
        
        logger.info(f"Retrieved info for job: {job_name}")
        
        return {
            "success": True,
            "job": formatted_info
        }
        
    except Exception as e:
        logger.error(f"Failed to get job info for {job_name}: {e}")
        return {
            "success": False,
            "error": str(e),
            "error_type": type(e).__name__,
            "job_name": job_name
        }


def _color_to_status(color: str) -> str:
    """
    Convert Jenkins color to human-readable status.
    
    Args:
        color: Jenkins job color
        
    Returns:
        Human-readable status
    """
    color_map = {
        "blue": "success",
        "green": "success", 
        "red": "failed",
        "yellow": "unstable",
        "grey": "not_built",
        "disabled": "disabled",
        "aborted": "aborted",
        "notbuilt": "not_built"
    }
    
    # Handle animated colors (building)
    if color.endswith("_anime"):
        base_color = color.replace("_anime", "")
        return f"building_{color_map.get(base_color, 'unknown')}"
    
    return color_map.get(color, "unknown")
