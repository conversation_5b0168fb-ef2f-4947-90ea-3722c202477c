"""Simple Jenkins tools that use environment variables directly."""

import jenkins
from typing import Dict, Any, List
from ..config.settings import get_settings
from ..utils.auth import get_jenkins_credentials_from_env
from ..utils.logging import get_logger

logger = get_logger(__name__)


def test_jenkins_connection() -> Dict[str, Any]:
    """
    Test Jenkins connection using environment variables.
    
    Returns:
        Dict with connection status and server info
    """
    try:
        settings = get_settings()
        
        logger.info(f"Testing connection to <PERSON>: {settings.jenkins_url}")
        
        # Create Jenkins client
        server = jenkins.Jenkins(
            settings.jenkins_url,
            username=settings.jenkins_username,
            password=settings.jenkins_api_token
        )
        
        # Test connection by getting version
        version = server.get_version()
        user_info = server.get_whoami()
        
        logger.info(f"Successfully connected to <PERSON> {version}")
        
        return {
            "success": True,
            "version": version,
            "server_info": user_info,
            "url": settings.jenkins_url
        }
        
    except Exception as e:
        logger.error(f"Jenkins connection failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "url": settings.jenkins_url if settings else "unknown"
        }


def list_jenkins_jobs() -> Dict[str, Any]:
    """
    List all <PERSON> jobs using environment variables.
    
    Returns:
        Dict with job list and metadata
    """
    try:
        settings = get_settings()
        
        logger.info("Listing Jenkins jobs")
        
        # Create Jenkins client
        server = jenkins.Jenkins(
            settings.jenkins_url,
            username=settings.jenkins_username,
            password=settings.jenkins_api_token
        )
        
        # Get all jobs
        jobs = server.get_all_jobs()
        
        # Format job information
        formatted_jobs = []
        for job in jobs:
            formatted_jobs.append({
                "name": job.get("name", "Unknown"),
                "url": job.get("url", ""),
                "color": job.get("color", "unknown"),
                "status": _color_to_status(job.get("color", "unknown")),
                "buildable": job.get("buildable", False)
            })
        
        logger.info(f"Found {len(formatted_jobs)} Jenkins jobs")
        
        return {
            "success": True,
            "jobs": formatted_jobs,
            "total_count": len(formatted_jobs)
        }
        
    except Exception as e:
        logger.error(f"Failed to list Jenkins jobs: {e}")
        return {
            "success": False,
            "error": str(e),
            "jobs": []
        }


def get_job_info(job_name: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific Jenkins job.
    
    Args:
        job_name: Name of the Jenkins job
        
    Returns:
        Dict with job information
    """
    try:
        settings = get_settings()
        
        logger.info(f"Getting info for job: {job_name}")
        
        # Create Jenkins client
        server = jenkins.Jenkins(
            settings.jenkins_url,
            username=settings.jenkins_username,
            password=settings.jenkins_api_token
        )
        
        # Get job info
        job_info = server.get_job_info(job_name)
        
        # Get recent builds
        builds = []
        if job_info.get("builds"):
            for build in job_info["builds"][:5]:  # Last 5 builds
                build_number = build["number"]
                build_info = server.get_build_info(job_name, build_number)
                builds.append({
                    "number": build_number,
                    "result": build_info.get("result", "UNKNOWN"),
                    "timestamp": build_info.get("timestamp"),
                    "duration": build_info.get("duration"),
                    "url": build_info.get("url")
                })
        
        formatted_info = {
            "name": job_info.get("name"),
            "description": job_info.get("description", ""),
            "url": job_info.get("url"),
            "buildable": job_info.get("buildable", False),
            "color": job_info.get("color", "unknown"),
            "status": _color_to_status(job_info.get("color", "unknown")),
            "last_build": job_info.get("lastBuild"),
            "last_successful_build": job_info.get("lastSuccessfulBuild"),
            "last_failed_build": job_info.get("lastFailedBuild"),
            "recent_builds": builds
        }
        
        logger.info(f"Retrieved info for job: {job_name}")
        
        return {
            "success": True,
            "job": formatted_info
        }
        
    except Exception as e:
        logger.error(f"Failed to get job info for {job_name}: {e}")
        return {
            "success": False,
            "error": str(e),
            "job_name": job_name
        }


def _color_to_status(color: str) -> str:
    """
    Convert Jenkins color to human-readable status.
    
    Args:
        color: Jenkins job color
        
    Returns:
        Human-readable status
    """
    color_map = {
        "blue": "success",
        "green": "success", 
        "red": "failed",
        "yellow": "unstable",
        "grey": "not_built",
        "disabled": "disabled",
        "aborted": "aborted",
        "notbuilt": "not_built"
    }
    
    # Handle animated colors (building)
    if color.endswith("_anime"):
        base_color = color.replace("_anime", "")
        return f"building_{color_map.get(base_color, 'unknown')}"
    
    return color_map.get(color, "unknown")
