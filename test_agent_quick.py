#!/usr/bin/env python3
"""
Quick test script to verify Jenkins Reader Agent functionality
"""

import sys
import os
sys.path.insert(0, '.')

def test_agent_import():
    """Test that the agent can be imported successfully"""
    try:
        from jenkins_agent import root_agent, jenkins_agent
        print("✅ Agent import successful")
        print(f"   Root agent type: {type(root_agent)}")
        print(f"   Jenkins agent type: {type(jenkins_agent)}")
        return True
    except Exception as e:
        print(f"❌ Agent import failed: {e}")
        return False

def test_agent_configuration():
    """Test that the agent configuration is valid"""
    try:
        from jenkins_agent.config.settings import get_settings
        settings = get_settings()
        print("✅ Agent configuration loaded")
        print(f"   Project: {settings.google_cloud_project}")
        print(f"   Location: {settings.google_cloud_location}")
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_tools_import():
    """Test that tools can be imported"""
    try:
        from jenkins_agent.tools import (
            connection_tools,
            job_tools,
            build_tools,
            security_tools
        )
        print("✅ Tools import successful")
        print("   Available tool modules:")
        print("   - connection_tools")
        print("   - job_tools") 
        print("   - build_tools")
        print("   - security_tools")
        return True
    except Exception as e:
        print(f"❌ Tools import failed: {e}")
        return False

def test_authentication():
    """Test authentication setup"""
    try:
        from jenkins_agent.utils.auth import setup_google_auth
        print("✅ Authentication module available")
        
        # Check if service account key exists
        if os.path.exists('service-account-key.json'):
            print("✅ Service account key file found")
        else:
            print("⚠️  Service account key file not found (expected for security)")
        
        return True
    except Exception as e:
        print(f"❌ Authentication test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Jenkins Reader Agent - Quick Test Suite")
    print("=" * 50)
    
    tests = [
        ("Agent Import", test_agent_import),
        ("Configuration", test_agent_configuration),
        ("Tools Import", test_tools_import),
        ("Authentication", test_authentication),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing: {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
        
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Agent is ready for use.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
