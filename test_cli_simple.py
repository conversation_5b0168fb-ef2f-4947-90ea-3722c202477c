#!/usr/bin/env python3
"""
Simple CLI test that bypasses complex authentication and uses environment variables directly.
"""

import os
import sys
import jenkins
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_env_vars():
    """Test that environment variables are loaded correctly."""
    print("🔧 Testing environment variables...")
    
    jenkins_url = os.getenv('JENKINS_URL')
    jenkins_username = os.getenv('JENKINS_USERNAME') 
    jenkins_token = os.getenv('JENKINS_API_TOKEN')
    google_project = os.getenv('GOOGLE_CLOUD_PROJECT')
    
    print(f"   Jenkins URL: {jenkins_url}")
    print(f"   Jenkins Username: {jenkins_username}")
    print(f"   Jenkins Token: {'***' + jenkins_token[-4:] if jenkins_token else 'None'}")
    print(f"   Google Project: {google_project}")
    
    if all([jenkins_url, jenkins_username, jenkins_token]):
        print("✅ All Jenkins environment variables are set")
        return True
    else:
        print("❌ Missing Jenkins environment variables")
        return False

def test_jenkins_direct():
    """Test Jenkins connection directly using python-jenkins library."""
    print("\n🔗 Testing direct Jenkins connection...")
    
    try:
        jenkins_url = os.getenv('JENKINS_URL')
        jenkins_username = os.getenv('JENKINS_USERNAME')
        jenkins_token = os.getenv('JENKINS_API_TOKEN')
        
        # Create Jenkins client directly
        server = jenkins.Jenkins(
            jenkins_url,
            username=jenkins_username,
            password=jenkins_token
        )
        
        # Test connection
        version = server.get_version()
        user_info = server.get_whoami()
        
        print(f"✅ Jenkins connection successful!")
        print(f"   Version: {version}")
        print(f"   User: {user_info.get('fullName', 'Unknown')}")
        print(f"   Authorities: {user_info.get('authorities', [])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Jenkins connection failed: {e}")
        return False

def test_jenkins_jobs_direct():
    """Test listing Jenkins jobs directly."""
    print("\n📋 Testing Jenkins job listing...")
    
    try:
        jenkins_url = os.getenv('JENKINS_URL')
        jenkins_username = os.getenv('JENKINS_USERNAME')
        jenkins_token = os.getenv('JENKINS_API_TOKEN')
        
        # Create Jenkins client
        server = jenkins.Jenkins(
            jenkins_url,
            username=jenkins_username,
            password=jenkins_token
        )
        
        # Get all jobs
        jobs = server.get_all_jobs()
        
        print(f"✅ Found {len(jobs)} Jenkins jobs:")
        
        # Show first 10 jobs with status
        for i, job in enumerate(jobs[:10]):
            name = job.get('name', 'Unknown')
            color = job.get('color', 'unknown')
            status = color_to_status(color)
            print(f"   {i+1:2d}. {name:<25} - {status}")
        
        if len(jobs) > 10:
            print(f"   ... and {len(jobs) - 10} more jobs")
        
        return True
        
    except Exception as e:
        print(f"❌ Job listing failed: {e}")
        return False

def test_agent_import():
    """Test importing the agent without authentication context."""
    print("\n🤖 Testing agent import...")
    
    try:
        sys.path.insert(0, '.')
        from jenkins_agent import root_agent
        
        print(f"✅ Agent imported successfully!")
        print(f"   Agent type: {type(root_agent)}")
        print(f"   Agent name: {getattr(root_agent, 'name', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent import failed: {e}")
        return False

def color_to_status(color):
    """Convert Jenkins color to readable status."""
    color_map = {
        'blue': 'SUCCESS',
        'red': 'FAILED', 
        'yellow': 'UNSTABLE',
        'grey': 'NOT_BUILT',
        'disabled': 'DISABLED',
        'aborted': 'ABORTED'
    }
    
    if color.endswith('_anime'):
        base_color = color.replace('_anime', '')
        return f"BUILDING ({color_map.get(base_color, 'UNKNOWN')})"
    
    return color_map.get(color, 'UNKNOWN')

def main():
    """Run all CLI tests."""
    print("🧪 Jenkins Reader Agent - CLI Test Suite")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", test_env_vars),
        ("Direct Jenkins Connection", test_jenkins_direct),
        ("Jenkins Jobs Listing", test_jenkins_jobs_direct),
        ("Agent Import", test_agent_import),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Testing: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= 3:  # At least 3 out of 4 tests should pass
        print("🎉 CLI tests mostly successful! Ready for ADK Web testing.")
        return 0
    else:
        print("❌ Multiple CLI tests failed. Fix issues before proceeding.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
